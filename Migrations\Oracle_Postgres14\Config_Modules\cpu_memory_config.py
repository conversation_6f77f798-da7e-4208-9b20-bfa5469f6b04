import os, sys
from import_file import import_file
from datetime import datetime, timedelta
from common_modules.api import api_authentication, decrypt_database_details
from common_modules.stored_procedures import fetch_tracking_status


def copy_dag_files(project_id, connection_id,connection_type, local_migration_path, dag_path, root_path):
    source_file = local_migration_path + '/' + 'Dag_Files' + '/' + 'cpu_memory_dag_file.py'
    target_file = dag_path + '/' + f'cpu_memory_dag_file_{connection_id}.py'

    with open(source_file, 'r') as file:
        file_content = file.read()
        file_content = file_content.replace('@Project_Id', project_id).replace('@Connection_Id', connection_id).replace(
            '@Connection_Type', connection_type)

    with open(target_file, 'w') as f:
        f.write(file_content)

    dag_triggers_file = local_migration_path + '/' + 'Dag_Files' + '/' + 'dag_triggers.py'
    trigger_destination_file = dag_path + '/' + 'dag_triggers.py'
    with open(dag_triggers_file, 'rb') as f:
        file_content = f.read()
    with open(trigger_destination_file, 'wb') as f:
        f.write(file_content)



def cpu_memory_trigger(task_name, project_id, migration_name, source_connection_id, target_connection_id,cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')
        dag_path = getattr(import_object, 'Dag_Path')

        local_migration_path = local_root_path + '/' + 'Migrations' + '/' + migration_name

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)

            token_data = api_authentication()

            project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
            project_function_call = getattr(import_object, 'connect_database')

            connection_id, connection_type,tracking_data= '', '',''
            if source_connection_id != '':
                connection_id = source_connection_id
                connection_type = 'Source'

            if target_connection_id != '':
                connection_id = target_connection_id
                connection_type = 'Target'

            project_connection = project_function_call(project_DB_details)
            tracking_data = fetch_tracking_status(project_connection, connection_id, connection_type)

            if tracking_data:
                last_record_entry = tracking_data[-1][1]

            current_time = datetime.now()

            if current_time - last_record_entry < timedelta(minutes=5):
                print("Please check another dag running for storing cpu memory utilization for same connection")
            else:
                copy_dag_files(project_id, connection_id, connection_type, local_migration_path, dag_path, root_path)

    else:
        print(f'Config path {config_path} not found')
