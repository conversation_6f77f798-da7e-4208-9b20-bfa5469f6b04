import os, requests, json, argparse

application_url = 'https://dvqcam.qmigrator.ai/api/v1/'


def api_authentication():
    url_login = str(application_url) + 'ServiceAccount/Login?'
    params = {
        "username": "qmig"
    }
    response = requests.post(url_login, params=params)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = json.loads(status_response['text'])
        token_data = data['token']
        return token_data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def api_encrypt(token_data, data, migid):
    url = str(application_url) + 'Client/EncryptForPython'
    payload = {
        'migid': str(migid),
        'plaintext': data
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response = requests.post(url, data=json.dumps(payload), headers=headers)
    status_response = {'status_code': response.status_code, 'text': response.text}
    resp = json.loads(status_response['text'])['message']
    return resp


def api_decrypt(token_data, data, migid):
    url = str(application_url) + 'Client/DecryptForPython'
    payload = {
        'migid': str(migid),
        'encryptedtext': data
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response = requests.post(url, data=json.dumps(payload), headers=headers)
    status_response = {'status_code': response.status_code, 'text': response.text}
    resp = json.loads(status_response['text'])['message']
    return resp


def encrypt_file(file_path, migid):
    with open(file_path, 'r') as original_file:
        original = original_file.read()
    encrypted = api_encrypt(token_data, original, migid)
    with open(file_path, 'w') as encrypted_file:
        encrypted_file.write(encrypted)


def decrypt_file(file_path, migid):
    with open(file_path, 'r') as encrypted_file:
        encrypted = encrypted_file.read()
    decrypted = api_decrypt(token_data, encrypted, migid)
    local_root_path = os.path.dirname(os.path.realpath(__file__))
    with open(local_root_path + '/' + file_path, 'w') as decrypted_file:
        decrypted_file.write(decrypted)


parser = argparse.ArgumentParser()

parser.add_argument('-mig_name', '--mig_name',
                    help="Provide Migration name", nargs='?', const='', default='')
parser.add_argument('-migid', '--migid',
                    help="Provide Migration id", nargs='?', const='', default='')

args = parser.parse_args()
migration_name = args.mig_name
migid = args.migid

token_data = api_authentication()

config_folder_path = 'Migrations/' + migration_name + '/Config_Modules'
if os.path.exists(config_folder_path):
    config_files_list = os.listdir(config_folder_path)
    for config_file in config_files_list:
        encrypt_file(config_folder_path + '/' + config_file, migid)

triggers_folder_path = 'Migrations/' + migration_name + '/Dag_Files'
if os.path.exists(triggers_folder_path):
    trigger_files_list = os.listdir(triggers_folder_path)
    trigger_files_list = [file for file in trigger_files_list if '_triggers' in file]
    for trigger_file in trigger_files_list:
        encrypt_file(triggers_folder_path + '/' + trigger_file, migid)