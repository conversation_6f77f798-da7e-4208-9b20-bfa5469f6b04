import os, requests, json, sys
from import_file import import_file
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.exceptions import AirflowFailException

connection_url = os.getenv("API_HOST", 'http://qmig-eng.qmig-ns.svc.cluster.local:8080')
connection_url = connection_url + '/api/v1/'


def api_authentication():
    url = connection_url + 'Common/Security/VerifyPython'
    project_id = os.environ['PROJECT_ID']
    params = {
        "projectId": str(project_id),
        "content": "8/2DlAH8q1R+untey0ZuqMuD89WX4m2XoSCWhxQ+UOk="}
    response = requests.get(url, params=params)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = json.loads(status_response['text'])
        token_data = data['token']
        return token_data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def api_decrypt(data, token_data):
    url = connection_url + 'Common/Cryptography/DecryptData'
    payload = {
        "NeedToDecrypt": data,
        "user": "Python"
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.post(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    encrypted_source = status_response1['text']
    return encrypted_source


def decrypt_file(token_data, file_path, decryption_path, dag_name, task_name):
    with open(file_path, 'r') as encrypted_file:
        encrypted = encrypted_file.read()
    decrypted = api_decrypt(encrypted, token_data)

    if not os.path.exists(decryption_path):
        os.makedirs(decryption_path)

    file_name = file_path.split('/')[-1]
    with open(decryption_path + '/' + dag_name + '_' + task_name + '_' + file_name, 'w') as decrypted_file:
        decrypted_file.write(decrypted)


def delete_files_in_directory(file_path):
    if os.path.isfile(file_path):
        os.remove(file_path)


def cpu_memory_utilization_task(ti, **kwargs):
    try:
        token_data = kwargs['token_data']
        dag_name = kwargs['dag_id']
        task_name = kwargs['task_name']

        decryption_path = os.environ['EXTRA_FOLDER'] + '/tmp'
        sys.path.append(decryption_path)

        current_directory = os.getcwd() + '/' + 'dags'

        decrypt_file(token_data, current_directory + '/' + 'dag_triggers.py', decryption_path, dag_name, task_name)
        import_object = import_file(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')
        delete_files_in_directory(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')

        function_name = [i for i in dir(import_object) if i.lower() == str('cpu_memory_utilization_trigger').lower()][0]
        post_validation_function_call = getattr(import_object, function_name.strip())
        validation_flag = post_validation_function_call(ti, **kwargs)

        if validation_flag:
            raise AirflowFailException('Post Validation Failed')
    except Exception as error:
        print(f"Error at Post Validation Task: {str(error)}")
        raise AirflowFailException('Error at Post Validation Task')


def create_dag(dag_id, tag_name, schedule_interval, default_args, max_active_runs, project_id, connection_id,
               connection_type, token_data):
    dag = DAG(dag_id, schedule_interval=schedule_interval, default_args=default_args,
              render_template_as_native_obj=True, max_active_runs=max_active_runs,
              tags=[tag_name], catchup=False, is_paused_upon_creation=False)

    cpu_memory_task = PythonOperator(
        task_id='cpu_memory_utilization_task',
        op_kwargs={'dag_id': dag_id, 'project_id': project_id, 'connection_id': connection_id,
                   'connection_type': connection_type, 'task_name': f'{connection_id}_cpu_memory_utilization','token_data':token_data},
        python_callable=cpu_memory_utilization_task,
        dag=dag)
    cpu_memory_task
    return dag


project_id = int('@Project_Id')
connection_id = '@Connection_Id'
connection_type = '@Connection_Type'

token_data = api_authentication()

dag_name = f'{connection_id}_CPU_Memory_Utilization'
tag_name = f'{connection_type}_CPU_Memory_Utilization'
default_args = {'owner': 'airflow',
                'start_date': datetime(2023, 9, 11, 7, 00, 00),
                'retries': 3,
                'retry_delay': timedelta(minutes=4)
                }
schedule_interval = "*/5 * * * *"
max_active_runs = 1

globals()[dag_name] = create_dag(dag_name, tag_name, schedule_interval, default_args, max_active_runs, project_id,
                                 connection_id, connection_type, token_data)
