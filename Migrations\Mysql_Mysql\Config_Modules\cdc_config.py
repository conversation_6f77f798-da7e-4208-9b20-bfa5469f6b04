import os, sys, re, shutil
from import_file import import_file
import pandas as pd
from common_modules.api import api_authentication, decrypt_database_details
from common_modules.stored_procedures import insert_config_name


def copy_cdc_dag_files(file_name, config_files_path, local_migration_path, dag_path):
    source_file = local_migration_path + '/' + 'Dag_Files' + '/' + 'cdc_dag_file.py'
    target_file = dag_path + '/' + f'cdc_dag_file_{file_name}.py'
    with open(source_file, 'r') as file:
        file_content = file.read()
        file_replaced_content = re.sub('@Config_File_Path',
                                       f'{config_files_path}/CDC/{file_name}.xlsx'.replace('\\', '/'),
                                       file_content)
    with open(target_file, 'w') as f:
        f.write(file_replaced_content)

    dag_triggers_file = local_migration_path + '/' + 'Dag_Files' + '/' + 'dag_triggers.py'
    trigger_destination_file = dag_path + '/' + 'dag_triggers.py'
    with open(dag_triggers_file, 'rb') as f:
        file_content = f.read()
    with open(trigger_destination_file, 'wb') as f:
        f.write(file_content)


def cdc_load_trigger(task_name, project_id, migration_name, source_connection_id, target_connection_id, schema_name,
                     target_schema, table_name, cdc_load_type, file_name, cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')
        dag_path = getattr(import_object, 'Dag_Path')

        working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')
        local_migration_path = local_root_path + '/' + 'Migrations' + '/' + migration_name

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)

            token_data = api_authentication()

            source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
            source_function_call = getattr(import_object, 'DB_connection')

            project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
            project_function_call = getattr(import_object, 'connect_database')

            execute_query_function_call = getattr(import_object, 'execute_query')

            if target_connection_id in ['', None]:
                config_files_path = root_path + '/' + str(source_connection_id) + '/' + 'Config_Files'
            else:
                config_files_path = root_path + '/' + str(source_connection_id) + '/' + str(
                    target_connection_id) + '/' + 'Config_Files'

            if not os.path.exists(config_files_path + '/' + 'CDC'):
                os.makedirs(config_files_path + '/' + 'CDC')

            if not os.path.exists(working_directory_path):
                os.makedirs(working_directory_path)

            schema_list_query = """
            SELECT u.username FROM DBA_USERS u
            LEFT JOIN
                DBA_SEGMENTS s ON u.username = s.owner
            WHERE
                u.username NOT IN (
                    'SYSTEM', 'SYS', 'APPOQSYS', 'REMOTE_SCHEDULER_AGENT', 'DBSFWMUSER',
                    'CTXSYS', 'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS',
                    'DVSYS', 'GSMADMIN_INTERNAL', 'ORDPLUGINS', 'ORDDATA', 'MDSYS',
                    'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM', 'XDB', 'WMSYS',
                    'ORDSYS', 'DBSNMP', 'DVF', 'APEX_030200', 'EXFSYS', 'OWBSYS',
                    'ONBSYS_AUDIT', 'SYSMAN', 'SCOTT'
                )
                AND u.account_status = 'OPEN'
            GROUP BY u.username ORDER BY u.username
            """

            table_list_query = """
            select DISTINCT OBJECT_NAME from dba_objects a where NOT exists (select 1 from dba_mviews MV
            WHERE MV.MVIEW_NAME=a.object_name AND MV.OWNER=upper('@schemaname')) AND A.OWNER=upper('@schemaname') and
            OBJECT_NAME not like '%$%' and OBJECT_NAME not like 'SYS_%' AND A.OBJECT_TYPE='TABLE' 
            AND a.STATUS = 'VALID' AND TEMPORARY='N' ORDER BY 1
            """

            tables_list = []
            source_connection, error = source_function_call(source_DB_details)
            if table_name != '':
                tables_list = [(schema_name.upper(), i.upper()) for i in table_name.split(',') if i.strip()]
            elif schema_name != '' and table_name == '':
                schema_list = [i for i in schema_name.split(',') if i.strip()]
                if schema_list:
                    for schema in schema_list:
                        table_list_query = table_list_query.replace('@schemaname', schema.upper())
                        schema_table_list = execute_query_function_call(source_connection, table_list_query)
                        if schema_table_list:
                            tables_list.extend([(schema.upper(), i[0]) for i in schema_table_list])
            elif schema_name == '' and table_name == '':
                schema_list = execute_query_function_call(source_connection, schema_list_query)
                if schema_list:
                    for schema in schema_list:
                        table_list_query = table_list_query.replace('@schemaname', schema[0].upper())
                        schema_table_list = execute_query_function_call(source_connection, table_list_query)
                        if schema_table_list:
                            tables_list.extend([(schema.upper(), i[0]) for i in schema_table_list])

            cdc_file_name = 'cdc_' + file_name.replace('.xlsx', '')
            cdc_config_file_path = config_files_path + '/' + 'CDC' + '/' + cdc_file_name + '.xlsx'
            temp_cdc_config_path = working_directory_path + '/' + cdc_file_name + '.xlsx'

            with pd.ExcelWriter(temp_cdc_config_path, engine="xlsxwriter") as writer:
                tables_str = ','.join([f'{table_tuple[0]}.{table_tuple[1]}' for table_tuple in tables_list])
                cdc_config_list = [
                    (task_name, project_id, migration_name, source_connection_id,
                     target_connection_id,
                     schema_name, tables_str, target_schema,
                     cdc_load_type, cdc_file_name)]
                cdc_config_df = pd.DataFrame(cdc_config_list,
                                             columns=['Process_Type', 'Project_Id',
                                                      'Migration_Name',
                                                      'Source_Connection_Id', 'Target_Connection_Id',
                                                      'Schema', 'Table_Name',
                                                      'Target_Schema', 'CDC_Load_Type', 'File_Name'])
                cdc_config_df = cdc_config_df.transpose()
                key_value_pairs = cdc_config_df.to_dict()[0]
                cdc_config_df = pd.DataFrame(list(key_value_pairs.items()),
                                             columns=['Parameter', 'Value'])
                cdc_config_df.fillna('', inplace=True)
                cdc_config_df.to_excel(writer, sheet_name="Configuration", index=False)

                dag_tasks_list = [
                    (cdc_file_name, str(source_DB_details['db_name']).capitalize(),
                     'CDC_' + file_name.replace('.xlsx', ''))]
                tasks_df = pd.DataFrame(dag_tasks_list,
                                        columns=['File_Name', 'DB_Name', 'Dag Name'])
                tasks_df.fillna('', inplace=True)
                tasks_df.to_excel(writer, sheet_name='Dags', index=False)

            shutil.copyfile(temp_cdc_config_path, cdc_config_file_path)
            project_connection = project_function_call(project_DB_details)

            insert_config_name(project_connection, cdc_file_name, task_name, source_connection_id,
                               target_connection_id, None, None, 'Created')
            copy_cdc_dag_files(cdc_file_name, config_files_path.replace(root_path + '/', ''), local_migration_path, dag_path)
        else:
            print(f'Module path {db_module_path} not found')
    else:
        print(f'Config path {config_path} not found')
