def insert_config_name(connection, excel_file_name, task_name, source_connection_id, target_connection_id, request_cpu,
                       limit_cpu, config_status):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_audit_config_insert(%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
                       (excel_file_name, task_name, source_connection_id, target_connection_id, request_cpu, limit_cpu,
                        config_status,'dataset'))
        data = cursor.fetchall()
    except Exception as error:
        data = None
        print("Error at insert config name : ", str(error))
    finally:
        connection.commit()
        cursor.close()
    return data


def transaction_insert(connection, config_id, transaction_number, transaction_file, config_status):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_transaction_insert(%s,%s,%s,%s,%s);fetch all in "dataset";',
                       (config_id, transaction_number, transaction_file, config_status, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def insert_config_status(connection, config_id, config_status):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_audit_config_status_insert(%s,%s,%s);fetch all in "dataset";',
                       (config_id, config_status, 'dataset'))
    except Exception as error:
        print("Error at insert config status: ", str(error))
    finally:
        connection.commit()
        cursor.close()


def get_config_files(connection, source_connection_id, target_connection_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            f"""select config_id, config_name, transaction_number, transaction_file, process_type, config_status from audit_config
            where source_connection_id = {source_connection_id} and target_connection_id = {target_connection_id}
            and process_type in ('Initial_Data_Load', 'E2E_Data_Load')  and  config_status = 'Created' order by created_time""")
        data = cursor.fetchall()
    except Exception as error:
        data = None
        print(f"Error at fetching config files: {str(error)}")
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def get_running_dags(connection):
    cursor = connection.cursor()
    try:
        cursor.execute(
            f"""select dag_id, dag_name, config_id from audit_dags where dag_status = 'Running' order by dag_start_time""")
        data = cursor.fetchall()
    except Exception as error:
        data = None
        print(f"Error at fetching running dags: {str(error)}")
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def get_config_processed_dags(connection, config_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            f"select dag_id, dag_name, dag_status from audit_dags where config_id = '{config_id}' and dag_status in ('Success', 'Fail')")
        data = cursor.fetchall()
    except Exception as error:
        data = None
        print(f"Error at fetching config processed dags: {str(error)}")
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def fetch_agent_status(connection, source_connection_id, target_connection_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            f"""select agent_id, agent_status from public.audit_job_agent_tracking 
            where source_connection_id = {source_connection_id} and target_connection_id = {target_connection_id} 
            and agent_status = 'Running'""")
        data = cursor.fetchall()
    except Exception as error:
        data = None
        print("Error at fetch job agent tracking: ", str(error))
    finally:
        connection.commit()
        cursor.close()
    return data


def insert_job_agent_tracking(connection, source_connection_id, target_connection_id, agent_start_time, agent_status):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_audit_job_agent_tracking_insert(%s,%s,%s,%s,%s);fetch all in "dataset";',
                       (source_connection_id, target_connection_id, agent_start_time, agent_status, 'dataset'))
    except Exception as error:
        print("Error at insert job agent tracking: ", str(error))
    finally:
        connection.commit()
        cursor.close()


def update_job_agent_tracking(connection, agent_id, agent_status, agent_error, agent_end_time):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_audit_job_agent_tracking_update(%s,%s,%s,%s,%s);fetch all in "dataset";',
                       (agent_id, agent_status, agent_error, agent_end_time, 'dataset'))
    except Exception as error:
        print("Error at update job agent tracking: ", str(error))
    finally:
        connection.commit()
        cursor.close()


def fetch_tracking_status(connection, connection_id, connection_type):
    cursor = connection.cursor()
    try:
        cursor.execute(
            f"""select id, created_time from public.audit_cpu_memory_tracking 
            where connection_id = {connection_id} and connection_type = {connection_type}""")
        data = cursor.fetchall()
    except Exception as error:
        data = None
        print(f"Error at fetch_tracking_status : {str(error)}")
    finally:
        connection.commit()
        cursor.close()
    return data


# def get_failed_tasks(connection, file_name, process_type, dag_name):
#     cursor = connection.cursor()
#     try:
#         print('call public.prj_sp_failed_task_select(%s,%s,%s,%s);fetch all in "dataset";',
#               (file_name, process_type, dag_name, 'dataset'))
#         cursor.execute('call public.prj_sp_failed_task_select(%s,%s,%s,%s);fetch all in "dataset";',
#                        (file_name, process_type, dag_name, 'dataset'))
#         data = cursor.fetchall()
#     except Exception as err:
#         data = None
#         print("Error at getting catch up tasks: ", err)
#     finally:
#         connection.commit()
#         cursor.close()
#     return data
