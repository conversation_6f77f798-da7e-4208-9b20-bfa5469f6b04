import os, sys, json, requests
import pandas as pd
from datetime import datetime, timedelta
from import_file import import_file
from airflow import DAG
from airflow.operators.python import PythonOperator

connection_url = os.getenv("API_HOST", 'http://qmig-eng.qmig-ns.svc.cluster.local:8080')
connection_url = connection_url + '/api/v1/'


def api_authentication():
    url = connection_url + 'Common/Security/VerifyPython'
    project_id = os.environ['PROJECT_ID']
    params = {
        "projectId": str(project_id),
        "content": "8/2DlAH8q1R+untey0ZuqMuD89WX4m2XoSCWhxQ+UOk="}
    response = requests.get(url, params=params)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = json.loads(status_response['text'])
        token_data = data['token']
        return token_data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def api_decrypt(data, token_data):
    url = connection_url + 'Common/Cryptography/DecryptData'
    payload = {
        "NeedToDecrypt": data,
        "user": "Python"
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.post(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    encrypted_source = status_response1['text']
    return encrypted_source


def decrypt_file(token_data, file_path, decryption_path, dag_name, task_name):
    with open(file_path, 'r') as encrypted_file:
        encrypted = encrypted_file.read()
    decrypted = api_decrypt(encrypted, token_data)

    if not os.path.exists(decryption_path):
        os.makedirs(decryption_path)

    file_name = file_path.split('/')[-1]
    with open(decryption_path + '/' + dag_name + '_' + task_name + '_' + file_name, 'w') as decrypted_file:
        decrypted_file.write(decrypted)


def delete_files_in_directory(file_path):
    if os.path.isfile(file_path):
        os.remove(file_path)


def load_files_task_trigger(**kwargs):
    token_data = kwargs['token_data']
    dag_name = kwargs['dag_id']
    task_name = kwargs['task_name']

    decryption_path = os.environ['EXTRA_FOLDER'] + '/tmp'
    sys.path.append(decryption_path)

    current_directory = os.getcwd() + '/' + 'dags'

    decrypt_file(token_data, current_directory + '/' + 'dag_triggers.py', decryption_path, dag_name, task_name)
    import_object = import_file(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')
    delete_files_in_directory(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')

    function_name = [i for i in dir(import_object) if i.lower() == str('load_files_trigger').lower()][0]
    load_files_function_call = getattr(import_object, function_name.strip())
    load_files_function_call(**kwargs)


def create_dag(dag_id, tag_name, schedule_interval, pause_flag, default_args, max_active_runs, process_type, project_id,
               source_connection_id, target_connection_id, file_name, token_data):
    dag = DAG(dag_id, schedule_interval=schedule_interval, default_args=default_args,
              render_template_as_native_obj=True, max_active_runs=max_active_runs,
              tags=[tag_name], is_paused_upon_creation=pause_flag)

    load_files_task = PythonOperator(
        task_id='load_files_' + str(file_name),
        op_kwargs={'dag_id': dag_id, 'project_id': project_id, 'process_type': process_type,
                   'source_connection_id': source_connection_id,
                   'target_connection_id': target_connection_id, 'token_data': token_data,
                   'task_name': 'load_files_' + str(file_name), 'file_name': file_name},
        python_callable=load_files_task_trigger,
        weight_rule='absolute',
        trigger_rule='all_success',
        dag=dag)
    load_files_task
    return dag


extra_path = os.environ['EXTRA_FOLDER']
excel_file = '{0}/@Config_File_Path'.format(extra_path)

config_read = pd.read_excel(excel_file, sheet_name='Configuration')
config_read.fillna('', inplace=True)
config_list = config_read.to_dict(orient='records')

config_data = {}
for i in config_list: config_data.update({i['Parameter']: i['Value']})

token_data = api_authentication()

schedule_interval = None
pause_flag = True

dags_df = pd.read_excel(excel_file, sheet_name='Dags')
dags_df = dags_df.groupby('Dag Name')

for dag_name, dag_tables_list in dags_df:
    default_args = {'owner': 'airflow',
                    'start_date': datetime(2023, 9, 11, 7, 00, 00),
                    'retries': 3,
                    'retry_delay': timedelta(minutes=4)
                    }
    max_active_runs = 1

    tag_name = f"{config_data['File_Name']} Load Files"

    globals()[dag_name] = create_dag(dag_name, tag_name, schedule_interval, pause_flag, default_args, max_active_runs,
                                     config_data['Process_Type'], config_data['Project_Id'],
                                     config_data['Source_Connection_Id'], config_data['Target_Connection_Id'],
                                     config_data['File_Name'], token_data)
