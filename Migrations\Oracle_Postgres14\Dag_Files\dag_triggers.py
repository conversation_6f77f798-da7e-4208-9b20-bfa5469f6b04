import os, re, requests, json, psycopg2, cx_Oracle, logging, io, csv, shutil, sys
from datetime import datetime
import pandas as pd
import numpy as np
import xml.etree.ElementTree as ET

connection_url = os.getenv("API_HOST", 'http://qmig-eng.qmig-ns.svc.cluster.local:8080')
connection_url = connection_url + '/api/v1/'


def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


def DB_connection(database_data):
    host_name = database_data['host']
    port = database_data['port']
    service_name = database_data['db_name']
    user_name = database_data['name']
    password = database_data['password']
    try:
        dsn = cx_Oracle.makedsn(host=host_name, port=port, service_name=service_name)
        connection = cx_Oracle.Connection(user=user_name, password=password, dsn=dsn)
        error = ''
    except cx_Oracle.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near database connection", e)
    return connection, error


def target_DB_connection(db_data):
    try:
        connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                      host=db_data['host'], database=db_data['db_name'],
                                      port=db_data['port'])
        error = ''
    except psycopg2.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near target database connection", e)
    return connection, error


def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        data = cursor.fetchall()
        data = [[str(value) if isinstance(value, cx_Oracle.LOB) else value for value in row] for row in data]
    except cx_Oracle.DatabaseError as e:
        print("Issue found near database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data


def api_authentication():
    url = connection_url + 'Common/Security/VerifyPython'
    project_id = os.environ['PROJECT_ID']
    params = {
        "projectId": str(project_id),
        "content": "8/2DlAH8q1R+untey0ZuqMuD89WX4m2XoSCWhxQ+UOk="}
    response = requests.get(url, params=params)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = json.loads(status_response['text'])
        token_data = data['token']
        return token_data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def get_project_DB_info(token_data, project_id):
    url = connection_url + 'Common/Master/GetProjectMigDetailSelect'
    payload = {
        "projectId": str(project_id),
        "migsrcType": "D"
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.post(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = data_text['jsonResponseData']
        data_text = [dict for dict in data_text['Table1'] if dict['dbconname'] == 'PRJDB' + str(project_id)][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def get_source_DB_info(token_data, project_id, source_connection_id):
    url = connection_url + 'Common/Master/GetDBConnections'
    payload = {
        "projectId": str(project_id),
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.get(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = [dict for dict in data_text['Table1'] if
                     dict['migsrctgt'] == 'S' and dict['Connection_ID'] == source_connection_id][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def get_target_DB_info(token_data, project_id, target_connection_id):
    url = connection_url + 'Common/Master/GetDBConnections'
    payload = {
        "projectId": str(project_id),
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.get(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = [dict for dict in data_text['Table1'] if
                     dict['migsrctgt'] == 'T' and dict['Connection_ID'] == target_connection_id][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def decrypt_string(encrypted_str, token_data):
    application_url = connection_url + 'Common/Cryptography/DecryptData'
    payload = {
        "NeedToDecrypt": encrypted_str
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response = requests.post(application_url, data=json.dumps(payload), headers=headers)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = response.text
        return data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def decrypt_database_details(token_data, project_id, category, connection_id):
    prj_data = {}
    prj_db_data = {}
    if category == 'Project':
        prj_db_data = get_project_DB_info(token_data, project_id)
    elif category == 'Source':
        prj_db_data = get_source_DB_info(token_data, project_id, connection_id)
    elif category == 'Target':
        prj_db_data = get_target_DB_info(token_data, project_id, connection_id)
    password = decrypt_string(prj_db_data['dbpassword'], token_data)
    userid = decrypt_string(prj_db_data['dbuserid'], token_data)
    userid = re.split('\s+', re.sub(r"[\x00-\x1F\x7F]", ' ', userid))
    userid = ' '.join(userid).strip()
    password = re.split('\s+', re.sub(r"[\x00-\x1F\x7F]", ' ', password))
    password = ' '.join(password).strip()
    port = prj_db_data['dbport']
    host = prj_db_data['dbhost']
    if category == 'Project':
        service_name = ''
        parallelprocess = ''
    else:
        service_name = prj_db_data['service_name']
        parallelprocess = prj_db_data['parallelprocess']
    dbname = prj_db_data['dbname']
    prj_data['password'] = password
    prj_data['name'] = userid
    prj_data['port'] = port
    prj_data['host'] = host
    prj_data['db_name'] = dbname
    prj_data['service_name'] = service_name
    prj_data['parallelprocess'] = parallelprocess
    return prj_data


def get_config_id(connection, file_name, process_type):
    cursor = connection.cursor()
    try:
        cursor.execute(
            f"select config_id, transaction_number, transaction_file, config_status from audit_config where config_name = '{file_name}' and process_type = '{process_type}'")
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def transaction_insert(connection, config_id, transaction_number, transaction_file, config_status):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_transaction_insert(%s,%s,%s,%s,%s);fetch all in "dataset";',
                       (config_id, transaction_number, transaction_file, config_status, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def dag_insert(connection, dag_name, dag_type, schema_name, target_schema, table_name,
               table_size, concurrency, chunk_size, chunk_parts, dag_status, config_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_dags_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (dag_name, dag_type, schema_name, target_schema, table_name, table_size, concurrency, chunk_size,
             chunk_parts, dag_status, config_id, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def dag_update(connection, dag_id, dag_status, dag_end_time):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_audit_dags_update(%s,%s,%s,%s);fetch all in "dataset";',
                       (dag_id, dag_status, dag_end_time, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def task_insert(connection, task_name, task_type, attempt, lower_bound, upper_bound, request_memory, limit_memory,
                task_start_time,
                task_status, dag_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_task_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (task_name, task_type, attempt, str(lower_bound), str(upper_bound), request_memory, limit_memory,
             str(task_start_time),
             task_status, dag_id, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def task_update(connection, task_id, task_row_count, task_status, task_error, task_end_time, extraction_time,
                transform_time, load_time):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_task_update(%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (
                task_id, task_row_count, task_status, task_error, task_end_time, extraction_time, transform_time,
                load_time,
                'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def pre_validation_trigger(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    table_name = kwargs['table_name']
    file_name = kwargs['file_name']
    process_type = kwargs['process_type']
    token_data = kwargs['token_data']
    dag_name = kwargs['dag_id']

    source_name = 'Oracle'
    target_name = 'Postgres'

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
    source_connection, error = DB_connection(source_DB_details)

    config_data = get_config_id(project_connection, file_name, process_type)
    config_id = config_data[0][0]

    if config_data[0][1] in ['', None]:
        scn_fetch_query = "SELECT CURRENT_SCN FROM V$DATABASE"
        scn_data = execute_query(source_connection, scn_fetch_query)
        scn_number = scn_data[0][0]

        transaction_insert(project_connection, config_id, scn_number, None, 'Running')
    else:
        scn_number = config_data[0][1]

    dag_id = dag_insert(project_connection, dag_name, 'Data_Migration', schema_name, target_schema, table_name,
                        kwargs['table_size'],
                        kwargs['concurrency'], kwargs['chunk_size'], kwargs['chunk_parts'],
                        'Running', config_id)
    dag_id = dag_id[0][0]
    ti.xcom_push(key='dag_config', value={'dag_id': dag_id, 'config_id': config_id, 'scn_number': scn_number})

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, 'pre_validation_task', 'Pre_Validation', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]
    try:
        extra_path = os.environ['EXTRA_FOLDER']
        if target_connection_id in ['', None]:
            root_folder = extra_path + '/' + str(source_connection_id)
        else:
            root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

        scn_files_folder = root_folder + '/' + 'SCN_Files' + '/' + file_name.lower()
        if not os.path.exists(scn_files_folder):
            os.makedirs(scn_files_folder)

        current_scn_file = scn_files_folder + '/' + 'current_scn_file.txt'
        with open(current_scn_file, 'w') as cscn:
            cscn.write(str(scn_number))

        open_scn_file = scn_files_folder + '/' + 'open_scn_file.txt'
        with open(open_scn_file, 'w') as file_openscn:
            file_openscn.write('0')

        commit_scn_file = scn_files_folder + '/' + 'commit_scn_file.txt'
        with open(commit_scn_file, 'w') as file_csn:
            file_csn.write('0')

        validation_flag = False

        column_result_df, partition_result_df, datatypes_result_df = pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

        if target_connection_id not in ['', None]:
            target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
            target_connection, error = target_DB_connection(target_DB_details)

            xml_path = extra_path + '/' + 'Validation_Queries' + '/' + 'validation_queries.xml'
            tree = ET.parse(xml_path)
            root = tree.getroot()

            objects_list = ['Table', 'Partition', 'Datatype']

            for object_type in objects_list:
                source_query_tag = 'Table_Validation_Queries/Source/' + '/' + object_type
                object_source_query = list(root.iterfind(source_query_tag))[0].text
                object_source_query = object_source_query.replace('@schemaname', schema_name.upper()).replace('@order',
                                                                                                              '').replace(
                    '@degree', str(source_DB_details['parallelprocess'])).replace('@tablename', table_name.upper())

                source_object_output = execute_query(source_connection, object_source_query)

                target_query_tag = 'Table_Validation_Queries/Target/' + '/' + object_type
                object_target_query = list(root.iterfind(target_query_tag))[0].text
                object_target_query = object_target_query.replace('@schemaname', target_schema.lower()).replace(
                    '@order',
                    '').replace(
                    '@degree', str(target_DB_details['parallelprocess'])).replace('@tablename', table_name.lower())
                target_object_output = execute_query(target_connection, object_target_query)

                if object_type == "Table":
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Source_Schema_Name', 'Table_Name', 'Table_Type'])

                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Target_Schema_Name', 'Table_Name', 'Table_Type'])
                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    source_data_df['Table_Type'] = source_data_df['Table_Type'].replace({'base table': 'table'})
                    target_data_df['Table_Type'] = target_data_df['Table_Type'].replace({'base table': 'table'})
                    matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Table_Type'],
                                            suffixes=('_Source', '_Target'), how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                    matched_tables_list = matched_data['Table_Name'].values.tolist()

                    if table_name.lower() in matched_tables_list:
                        source_column_count_query = f"select COLUMN_NAME, DATA_TYPE from ALL_TAB_COLUMNS where upper(OWNER) = '{schema_name.upper()}' and upper(TABLE_NAME) = '{table_name.upper()}' ORDER BY COLUMN_ID"
                        target_column_count_query = f"select column_name, data_type from information_schema.columns where upper(table_schema) = '{target_schema.upper()}' and UPPER(table_name) = '{table_name.upper()}' order by ordinal_position "
                        source_column_data = execute_query(source_connection, source_column_count_query)
                        target_column_data = execute_query(target_connection, target_column_count_query)

                        source_column_df = pd.DataFrame(source_column_data,
                                                        columns=['Column_Name', 'Data_Type'])
                        target_column_df = pd.DataFrame(target_column_data,
                                                        columns=['Column_Name', 'Data_Type'])
                        source_column_df = source_column_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                        target_column_df = target_column_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                        matched_column_data = pd.merge(source_column_df, target_column_df, on=['Column_Name'],
                                                       suffixes=('_Source', '_Target'), how='inner')
                        matched_column_data['Status'] = f"Available in both {source_name} and {target_name}"

                        source_data = pd.merge(source_column_df, target_column_df, on=['Column_Name'],
                                               suffixes=('_Source', '_Target'), how='left', indicator=True)
                        source_data = source_data[source_data['_merge'] == 'left_only']
                        source_data['Status'] = f"Available in {source_name} not in {target_name}"

                        target_data = pd.merge(source_column_df, target_column_df, on=['Column_Name'],
                                               suffixes=('_Source', '_Target'), how='right', indicator=True)
                        target_data = target_data[target_data['_merge'] == 'right_only']
                        target_data['Status'] = f"Available in {target_name} not in {source_name}"

                        column_result_df = pd.concat([matched_column_data, source_data, target_data], ignore_index=True)
                        column_result_df = column_result_df.drop(columns=['_merge'], errors='ignore')

                        if not source_data.empty or not target_data.empty:
                            validation_flag = True
                            print(
                                f"Column count not matching between source {len(source_column_df)} and target {len(target_column_df)}, please check the report for detailed information")
                    else:
                        validation_flag = True
                        print(
                            f"{table_name} not present in both sides, please check the report for detailed information")

                elif object_type == 'Partition':
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Schema_name', 'Table_Name', 'Partition_Name'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Schema_name', 'Table_Name', 'Partition_Name'])

                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                    source_data_df['Partition_Name'] = source_data_df.apply(
                        lambda row: f"{row['Table_Name']}_{row['Partition_Name']}" if isinstance(row['Partition_Name'],
                                                                                                 str) else row[
                            'Partition_Name'], axis=1)
                    if not source_data_df.empty or not target_data_df.empty:

                        matched_data = pd.merge(source_data_df, target_data_df,
                                                on=['Table_Name', 'Partition_Name'],
                                                suffixes=('_Source', '_Target'), how='inner')
                        matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                        source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Partition_Name'],
                                               suffixes=('_Source', '_Target'), how='left', indicator=True)
                        source_data = source_data[source_data['_merge'] == 'left_only']
                        source_data['Status'] = f"Available in {source_name} not in {target_name}"

                        target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Partition_Name'],
                                               suffixes=('_Source', '_Target'), how='right', indicator=True)
                        target_data = target_data[target_data['_merge'] == 'right_only']
                        target_data['Status'] = f"Available in {target_name} not in {source_name}"

                        partition_result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                        partition_result_df = partition_result_df.drop(columns=['_merge'], errors='ignore')

                        if len(source_data_df) == len(target_data_df):
                            if source_data.empty != target_data.empty:
                                validation_flag = True
                                print(
                                    f"Partition names not matching between source and target, please check the report for detailed information")
                        else:
                            validation_flag = True
                            print(
                                f"Partiton count not matching between source {len(source_data_df)} and target {len(target_data_df)}, please check the report for detailed information")

                if object_type == 'Datatype':
                    datatypes_dict = {'char': 'character',
                                      'date': 'timestamp without time zone',
                                      'long': 'text',
                                      'long raw': 'bytea',
                                      'clob': 'text',
                                      'nclob': 'text',
                                      'blob': 'bytea',
                                      'bfile': 'bytea',
                                      'raw': 'bytea',
                                      'float': 'double precision',
                                      'urowid': 'oid',
                                      'rowid': 'oid',
                                      'timestamp': 'timestamp without time zone',
                                      'timestamp(6)': 'timestamp without time zone',
                                      'timestamp(6) with time zone': 'timestamp with time zone',
                                      'xmltype': 'xml',
                                      'binary_integer': 'integer',
                                      'pls_integer': 'integer',
                                      'timestamp with time zone': 'timestamp with time zone',
                                      'timestamp with local time zone': 'timestamp with time zone',
                                      'varchar2': 'character varying',
                                      'number': 'numeric',
                                      'nvarchar2': 'character varying',
                                      'obj_snsdetails': 'user-defined',
                                      'timestamp(8)': 'timestamp without time zone',
                                      'timestamp(8) with time zone': 'timestamp with time zone',
                                      'nchar': 'character',
                                      'oid': 'text'}

                    datatypes_dict = {key.lower(): value.lower() for key, value in datatypes_dict.items()}

                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Source_Schema', 'Table_Name', 'Column_Name',
                                                           'Source_Datatype',
                                                           'Source_Datatype_Length', 'Source_Index', 'Table_Type'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Target_Schema', 'Table_Name', 'Column_Name',
                                                           'Target_Datatype',
                                                           'Target_Datatype_Length', 'Target_Index', 'Table_Type'])
                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                    source_data_df['Table_Type'] = source_data_df['Table_Type'].replace({'base table': 'table'})
                    target_data_df['Table_Type'] = target_data_df['Table_Type'].replace({'base table': 'table'})
                    source_data_df['Source_Modified_Datatype'] = source_data_df['Source_Datatype'].replace(
                        datatypes_dict)
                    source_data_df['Source_Datatype_Length'] = source_data_df[
                        'Source_Datatype_Length'].str.replace('(', '', regex=False).str.replace(')', '',
                                                                                                regex=False)
                    target_data_df['Target_Modified_Datatype'] = target_data_df['Target_Datatype']
                    target_data_df['Target_Datatype_Length'] = target_data_df[
                        'Target_Datatype_Length'].str.replace('(', '', regex=False).str.replace(')', '',
                                                                                                regex=False)

                    merged_df = pd.merge(source_data_df, target_data_df,
                                         on=['Table_Name', 'Column_Name', 'Table_Type'], how='inner')

                    datatypes_result = []
                    for index, row in merged_df.iterrows():
                        if row['Source_Modified_Datatype'] == row['Target_Modified_Datatype'] and row[
                            'Source_Index'] == \
                                row[
                                    'Target_Index'] and row['Source_Datatype_Length'] == row[
                            'Target_Datatype_Length']:
                            status = 'Matched'
                        elif (row['Source_Modified_Datatype'] == 'double precision' and row[
                            'Target_Modified_Datatype'] == 'double precision') and row[
                            'Target_Datatype_Length'] == '53':
                            status = 'Matched'
                        elif (row['Source_Modified_Datatype'] == 'numeric') and row['Target_Modified_Datatype'] in [
                            'smallint',
                            'bigint',
                            'integer',
                            'numeric']:
                            status = 'Matched'
                        else:
                            if (row['Source_Modified_Datatype'] == row['Target_Modified_Datatype']) and (
                                    row['Source_Index'] == row['Target_Index']) and (
                                    row['Source_Datatype_Length'] in ['', None, 'None', 'none'] or row[
                                'Target_Datatype_Length'] in ['', None, 'None', 'none']):
                                status = 'Matched'
                            else:
                                status = 'Not_Matched'
                                break
                        datatypes_result.append(
                            (row['Source_Schema'], row['Target_Schema'], row['Table_Name'], row['Column_Name'],
                             row['Source_Datatype'],
                             row['Source_Datatype_Length'], row['Target_Datatype'],
                             row['Target_Datatype_Length'], row['Source_Index'], row['Target_Index'],
                             row['Table_Type'], status))
                    datatypes_result_df = pd.DataFrame(datatypes_result,
                                                       columns=['Schema_Name', 'Target_Schema', 'Table_Name',
                                                                'Column_Name',
                                                                'Source_Datatype',
                                                                'Source_Datatype_Length', 'Target_Datatype',
                                                                'Target_Datatype_Length',
                                                                'Source_Index', 'Target_Index', 'Table_Type', 'Status'])
                    if 'Not_Matched' in datatypes_result_df['Status'].values.tolist():
                        validation_flag = True
                        print(
                            "Datatypes not matching between source and target, please check the report for detailed information")

        if validation_flag:
            pre_validation_path = root_folder + '/' + 'Dag_Validation_Reports' + '/' + file_name.lower() + '/' + dag_name
            if not os.path.exists(pre_validation_path):
                os.makedirs(pre_validation_path)

            with pd.ExcelWriter(pre_validation_path + '/' + 'pre_validation.xlsx') as writer:
                column_result_df.to_excel(writer, sheet_name='Columns', index=False)
                partition_result_df.to_excel(writer, sheet_name='Partitions', index=False)
                datatypes_result_df.to_excel(writer, sheet_name='Datatype_Matching', index=False)
            print(f"Pre validation failed for {table_name}. Please check report for detailed information")
            task_end_time = datetime.now()
            task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

            dag_end_time = datetime.now()
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
        else:
            print(f"Pre validation completed for {table_name}")
            task_end_time = datetime.now()
            task_update(project_connection, task_id, None, 'Success', None, task_end_time, None, None, None)
    except Exception as error:
        validation_flag = True
        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        if ti.try_number == 3:
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
    return validation_flag


def transform_blob(data):
    if data is not None:
        return psycopg2.Binary(data.read())


def transform_string_data(data):
    if data is not None:
        return str(data).replace("\'", "")


def transform_xml_data(data):
    if data is not None:
        text = str(data)
        text = re.sub(r'<!\[CDATA\[', '', text)
        text = re.sub(r'\]\]>', '', text)
        text = text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;').replace('"', '&quot;').replace("'",
                                                                                                                   '&apos;')
        text = text.replace("\'", "")
        return text


def transform_raw(data):
    if data is not None:
        return psycopg2.Binary(data)


def recursive_fetch(source_connection, target_DB_details, schema_name, target_schema, table_name,
                    number_zero_precision_columns, column_names_list,
                    column_names_list_postgres, parent_lower_bound, parent_upper_bound):
    problem_rows = []
    chunk_query = """
                    WITH total_count AS (
                    SELECT COUNT(*) AS total_rows
                    FROM @schema.@tablename
                    WHERE ROWID BETWEEN '@lowerbound' AND '@upperbound'  --rowid
                    ),
                    numbered_records AS (
                    SELECT rowid as rolex,
                    ROW_NUMBER() OVER (ORDER BY rowid) AS rn
                    FROM @schema.@tablename
                    WHERE ROWID BETWEEN '@lowerbound' AND '@upperbound'  --rowid
                    )
                    SELECT
                    CEIL(u.rn / (j.total_rows / 2)) AS chunk,  -- Chunk size is dynamically calculated
                    MIN(u.rolex) AS min_rowid,
                    MAX(u.rolex) AS max_rowid
                    FROM numbered_records u, total_count j
                    GROUP BY CEIL(u.rn / (j.total_rows / 2)) -- Chunk size is dynamically calculated
                    ORDER BY chunk
                """
    chunk_query = chunk_query.replace('@lowerbound', str(parent_lower_bound)).replace('@upperbound',
                                                                                      str(parent_upper_bound)).replace(
        '@tablename', table_name).replace('@schema', schema_name)

    source_cursor = source_connection.cursor()
    source_cursor.execute(chunk_query)
    chunks = source_cursor.fetchall()

    target_connection = psycopg2.connect(database=target_DB_details['db_name'],
                                         user=target_DB_details['name'],
                                         password=target_DB_details['password'],
                                         host=target_DB_details['host'], port=target_DB_details['port'],
                                         options=f"-c search_path={target_schema}")

    cursor = target_connection.cursor()
    temp_table_name = f"temp_{table_name}"
    cursor.execute(
        f"CREATE TEMP TABLE {temp_table_name} ON COMMIT PRESERVE ROWS AS SELECT * FROM {target_schema}.{table_name} WHERE 1=0")
    for chunk in chunks:
        chunk_number, min_rowid, max_rowid = chunk

        print(f"Processing chunk {chunk_number} from {min_rowid} to {max_rowid}")
        source_cursor.execute(
            f"SELECT {','.join(column_names_list)} FROM {schema_name}.{table_name} WHERE ROWID BETWEEN '{min_rowid}' AND '{max_rowid}'")
        rows = source_cursor.fetchall()

        df = pd.DataFrame(rows, columns=column_names_list_postgres)
        transform_dict = {r'\\0': r'\\\\0', r'\t': r'\\t', r'\n': r'\\n', r'\r\n': r'\\r\\n', r'\r': r'\\r'}
        all_null_columns = df.columns[df.isnull().all()]
        columns_to_replace = df.columns.difference(all_null_columns)
        df.loc[:, columns_to_replace].replace(transform_dict, regex=True, inplace=True)

        string_columns = df.select_dtypes(include=['object', 'string']).columns
        non_empty_string_columns = [col for col in string_columns if not df[col].isnull().all()]

        for col in non_empty_string_columns:
            df[col] = df[col].str.replace('\x00', '', regex=False)

        for column_name in number_zero_precision_columns:
            df[column_name] = df[column_name].astype('Int64')
        df.replace({'NaT': np.nan}, inplace=True)
        df.replace({np.nan: None}, inplace=True)

        buffer = io.StringIO()
        df.to_csv(buffer, sep='\t', header=None, index=None)
        buffer.seek(0)
        try:
            with target_connection.cursor() as cursor:
                cursor.copy_expert(f"COPY {table_name} FROM stdin WITH CSV DELIMITER '\t' NULL ''", buffer)
        except Exception as e:
            print('entered exception')
            print(f"Error loading chunk {chunk_number}: {e}")
            if not len(rows) <= 1:
                inter_problem_rows = recursive_fetch(source_connection, target_DB_details, schema_name, target_schema,
                                                     table_name,
                                                     number_zero_precision_columns,
                                                     column_names_list_postgres, min_rowid, max_rowid)
                problem_rows.extend(inter_problem_rows)
            else:
                print(f" Problematic record isolated at record:{rows}")
                problem_rows.extend(rows)
    return problem_rows


def recursive_fetch_lob(source_connection, target_DB_details, schema_name, target_schema, table_name,
                        number_zero_precision_columns, column_names_list, column_names_list_postgres,
                        parent_lower_bound,
                        parent_upper_bound, blob_columns_list, raw_columns_list, clob_columns_list, xml_columns_list, ):
    problem_rows = []
    chunk_query = """
                    WITH total_count AS (
                    SELECT COUNT(*) AS total_rows
                    FROM @schema.@tablename
                    WHERE ROWID BETWEEN '@lowerbound' AND '@upperbound'  --rowid
                    ),
                    numbered_records AS (
                    SELECT rowid as rolex,
                    ROW_NUMBER() OVER (ORDER BY rowid) AS rn
                    FROM @schema.@tablename
                    WHERE ROWID BETWEEN '@lowerbound' AND '@upperbound'  --rowid
                    )
                    SELECT
                    CEIL(u.rn / (j.total_rows / 2)) AS chunk,  -- Chunk size is dynamically calculated
                    MIN(u.rolex) AS min_rowid,
                    MAX(u.rolex) AS max_rowid
                    FROM numbered_records u, total_count j
                    GROUP BY CEIL(u.rn / (j.total_rows / 2)) -- Chunk size is dynamically calculated
                    ORDER BY chunk
                """
    chunk_query = chunk_query.replace('@lowerbound', str(parent_lower_bound)).replace('@upperbound',
                                                                                      str(parent_upper_bound)).replace(
        '@tablename', table_name).replace('@schema', schema_name)

    source_cursor = source_connection.cursor()
    source_cursor.execute(chunk_query)
    chunks = source_cursor.fetchall()

    target_connection = psycopg2.connect(database=target_DB_details['db_name'],
                                         user=target_DB_details['name'],
                                         password=target_DB_details['password'],
                                         host=target_DB_details['host'], port=target_DB_details['port'],
                                         options=f"-c search_path={target_schema}")

    cursor = target_connection.cursor()
    temp_table_name = f"temp_{table_name}"
    cursor.execute(
        f"CREATE TEMP TABLE {temp_table_name} ON COMMIT PRESERVE ROWS AS SELECT * FROM {target_schema}.{table_name} WHERE 1=0")
    for chunk in chunks:
        chunk_number, min_rowid, max_rowid = chunk

        print(f"Processing chunk {chunk_number} from {min_rowid} to {max_rowid}")
        source_cursor.execute(
            f"SELECT {','.join(column_names_list)} FROM {schema_name}.{table_name} WHERE ROWID BETWEEN '{min_rowid}' AND '{max_rowid}'")
        rows = source_cursor.fetchall()

        df = pd.DataFrame(rows, columns=column_names_list_postgres)

        for column_name in blob_columns_list:
            df[column_name] = df[column_name].apply(transform_blob)
        for column_name in raw_columns_list:
            df[column_name] = df[column_name].apply(transform_raw)
        for column_name in clob_columns_list:
            df[column_name] = df[column_name].apply(transform_string_data)
        for column_name in xml_columns_list:
            df[column_name] = df[column_name].apply(transform_xml_data)

        date_columns = df.select_dtypes(include=['datetime64', 'datetime64[ns]']).columns.tolist()
        df[date_columns] = df[date_columns].astype(str)

        for column_name in number_zero_precision_columns:
            df[column_name] = df[column_name].astype('Int64')
        df.replace({'NaT': np.nan}, inplace=True)
        df.replace({np.nan: None}, inplace=True)

        column_names_list_postgres_str = ','.join(column_names_list_postgres)

        final_table_data_list = [tuple(x) for x in df.values]
        query_values_list = ['%s' for i in range(len(column_names_list_postgres))]
        query_values_str = str(tuple(query_values_list)).replace("'", "")
        data_insert_query = "insert into {0}.{1} ({2}) values {3}".format(target_schema, table_name,
                                                                          column_names_list_postgres_str,
                                                                          query_values_str)
        try:
            target_cursor = target_connection.cursor()
            target_cursor.executemany(data_insert_query, final_table_data_list)
        except Exception as e:
            print('entered exception')
            print(f"Error loading chunk {chunk_number}: {e}")
            if not len(rows) <= 1:
                inter_problem_rows = recursive_fetch(source_connection, target_DB_details, schema_name, target_schema,
                                                     table_name,
                                                     number_zero_precision_columns,
                                                     column_names_list_postgres, min_rowid, max_rowid)
                problem_rows.extend(inter_problem_rows)
            else:
                print(f" Problematic record isolated at record:{rows}")
                problem_rows.extend(rows)
    return problem_rows


def table_migration(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    table_name = kwargs['table_name']
    target_schema = kwargs['target_schema']
    part_name = kwargs['task_name']
    token_data = kwargs['token_data']
    lower_bound = kwargs['lower_bound']
    upper_bound = kwargs['upper_bound']
    request_memory = kwargs['request_memory']
    limit_memory = kwargs['limit_memory']
    table_category = kwargs['table_category']
    data_load_type = kwargs['data_load_type']

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']
    scn_number = dag_config['scn_number']
    attempt = ti.try_number

    extraction_time = None
    transform_time = None
    load_time = None

    if lower_bound != 0 and upper_bound != 0:
        source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
        source_connection, error = DB_connection(source_DB_details)

        if attempt in [2, 3, 4]:
            chunk_query = f"""
                WITH total_count AS (
                SELECT COUNT(*) AS total_rows
                FROM {schema_name.upper()}.{table_name.upper()}
                WHERE ROWID BETWEEN '{str(lower_bound)}' AND '{str(upper_bound)}'  --rowid
                ),
                numbered_records AS (
                    SELECT rowid as rolex, 
                       ROW_NUMBER() OVER (ORDER BY rowid) AS rn
                    FROM {schema_name.upper()}.{table_name.upper()}
                    WHERE ROWID BETWEEN '{str(lower_bound)}' AND '{str(upper_bound)}'  --rowid
                )
                SELECT 
                    CEIL(u.rn / (j.total_rows / {attempt})) AS chunk,  -- Chunk size is dynamically calculated
                    MIN(u.rolex) AS min_rowid, 
                    MAX(u.rolex) AS max_rowid
                FROM numbered_records u, total_count j
                GROUP BY CEIL(u.rn / (j.total_rows / {attempt})) -- Chunk size is dynamically calculated
                ORDER BY chunk
                """
            chunks_data = execute_query(source_connection, chunk_query)
        elif attempt == 1:
            chunks_data = [(1, str(lower_bound), str(upper_bound))]
        else:
            chunks_data = [(1, str(lower_bound), str(upper_bound))]

        if chunks_data:
            for chunk_tuple in chunks_data:
                chunk_number, lower_bound, upper_bound = chunk_tuple[0], chunk_tuple[1], chunk_tuple[2]

                project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
                project_connection = connect_database(project_DB_details)
                task_start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
                task_id = task_insert(project_connection, str(table_name).lower() + '_' + part_name.lower(),
                                      'Data_Migration',
                                      attempt,
                                      lower_bound, upper_bound, request_memory, limit_memory, task_start_time, 'Running',
                                      dag_id)
                task_id = task_id[0][0]

                try:
                    extra_path = os.environ['EXTRA_FOLDER']
                    if target_connection_id in ['', None]:
                        root_folder = extra_path + '/' + str(source_connection_id)
                    else:
                        root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

                    log_path = root_folder + '/' + 'Data_Migration_Logs'
                    if not os.path.exists(log_path):
                        os.makedirs(log_path)
                    task_logger = logging.getLogger(__name__)
                    task_logger.setLevel(logging.DEBUG)
                    log_handler = logging.FileHandler(
                        log_path + '/' + f"{str(table_name).lower()}_{part_name.lower()}_{attempt}_{chunk_number}.log")
                    log_handler.setFormatter(logging.Formatter("%(asctime)s [%(levelname)s] - %(message)s"))
                    log_handler.addFilter(lambda record: record.levelno != logging.WARNING)
                    task_logger.addHandler(log_handler)

                    task_logger.debug(table_name)
                    task_logger.debug('Table execution started')
                    task_logger.debug(f'scn_number=={scn_number}')

                    source_DB_details = decrypt_database_details(token_data, project_id, 'Source',
                                                                 str(source_connection_id))
                    source_connection, error = DB_connection(source_DB_details)

                    datatypes_fetch_query = "SELECT COLUMN_NAME,DATA_TYPE FROM ALL_TAB_COLUMNS WHERE table_name = '{0}' AND OWNER = '{1}' ORDER BY COLUMN_ID".format(
                        table_name.upper(),
                        schema_name.upper())
                    datatypes_data = execute_query(source_connection, datatypes_fetch_query)

                    blob_columns_list = []
                    raw_columns_list = []
                    xml_columns_list = []
                    clob_columns_list = []
                    column_names_list_postgres = []
                    column_names_list = []

                    if table_category == 'Lob':
                        for row in datatypes_data:
                            column_names_list_postgres.append(row[0].lower())
                            if row[1].upper() == 'XMLTYPE':
                                column_names_list.append('xmltype.getclobval(' + str(row[0]) + ')')
                                xml_columns_list.append(row[0].lower())
                            elif str(row[1]).upper() == 'BLOB':
                                column_names_list.append(row[0])
                                blob_columns_list.append(row[0].lower())
                            elif str(row[1]).upper() == 'RAW':
                                column_names_list.append(row[0])
                                raw_columns_list.append(row[0].lower())
                            elif str(row[1]).upper() == 'CLOB':
                                column_names_list.append(row[0])
                                clob_columns_list.append(row[0].lower())
                            else:
                                column_names_list.append(row[0])
                    else:
                        column_names_list = [row[0] for row in datatypes_data]
                        column_names_list_postgres = [row[0].lower() for row in datatypes_data]

                    column_names_str = ','.join(column_names_list)
                    column_names_list_postgres_str = ','.join(column_names_list_postgres)

                    number_columns_fetch_query = "SELECT COLUMN_NAME, CASE WHEN data_type = 'NUMBER' THEN data_type | | '(' | | data_precision | | ',' | | data_scale | | ')' END AS data_type_with_precision FROM all_tab_columns WHERE owner||'.'||table_name = '{0}.{1}' AND data_scale = 0".format(
                        schema_name.upper(), table_name.upper())
                    columns_data = execute_query(source_connection, number_columns_fetch_query)
                    number_zero_precision_columns = [row[0].lower() for row in columns_data]

                    source_data_query = """select /*+ rowid({1}) */ {5} from {0}.{1} AS OF SCN {2} where rowid >= '{3}' and rowid <= '{4}'""".format(
                        schema_name.upper(), table_name.upper(), scn_number, str(lower_bound), str(upper_bound),
                        column_names_str)
                    task_logger.debug(source_data_query)

                    extraction_start_time = datetime.now()
                    source_cursor = source_connection.cursor()
                    source_cursor.arraysize = 100000
                    source_cursor.prefetchrows = 100001
                    source_cursor.execute(source_data_query)
                    table_data_list = source_cursor.fetchall()
                    source_cursor.close()
                    extraction_end_time = datetime.now()
                    extraction_time = (extraction_end_time - extraction_start_time).total_seconds() / 60
                    print(f"Extracted records: {len(table_data_list)}")

                    if table_data_list:
                        transform_start_time = datetime.now()
                        df = pd.DataFrame(table_data_list, columns=column_names_list_postgres)

                        if table_category == 'Lob':
                            if data_load_type == 'File':
                                for column_name in blob_columns_list + clob_columns_list:
                                    if df[column_name].dtype == object:
                                        df[column_name] = df[column_name].apply(
                                            lambda x: x.read() if isinstance(x, cx_Oracle.LOB) else x)
                            else:
                                for column_name in blob_columns_list:
                                    df[column_name] = df[column_name].apply(transform_blob)
                                for column_name in raw_columns_list:
                                    df[column_name] = df[column_name].apply(transform_raw)
                                for column_name in clob_columns_list:
                                    df[column_name] = df[column_name].apply(transform_string_data)
                                for column_name in xml_columns_list:
                                    df[column_name] = df[column_name].apply(transform_xml_data)

                            date_columns = df.select_dtypes(include=['datetime64', 'datetime64[ns]']).columns.tolist()
                            df[date_columns] = df[date_columns].astype(str)
                        else:
                            transform_dict = {r'\\0': r'\\\\0', r'\t': r'\\t', r'\n': r'\\n', r'\r\n': r'\\r\\n',
                                              r'\r': r'\\r'}
                            all_null_columns = df.columns[df.isnull().all()]
                            columns_to_replace = df.columns.difference(all_null_columns)
                            df.loc[:, columns_to_replace].replace(transform_dict, regex=True, inplace=True)

                            string_columns = df.select_dtypes(include=['object', 'string']).columns
                            non_empty_string_columns = [col for col in string_columns if not df[col].isnull().all()]

                            for col in non_empty_string_columns:
                                df[col] = df[col].apply(lambda x: x.replace('\x00', '') if isinstance(x, str) else x)
                                # df[col] = df[col].str.replace('\x00', '', regex=False)

                        for column_name in number_zero_precision_columns:
                            df[column_name] = df[column_name].astype('Int64')
                        df.replace({'NaT': np.nan}, inplace=True)
                        df.replace({np.nan: None}, inplace=True)
                        transform_end_time = datetime.now()
                        transform_time = (transform_end_time - transform_start_time).total_seconds() / 60

                        print(f"Transformed records: {len(table_data_list)}")
                        if data_load_type == 'File':
                            data_files_path = f"{root_folder}/Data_Files/{schema_name.lower()}/{table_name.lower()}"
                            if not os.path.exists(data_files_path):
                                os.makedirs(data_files_path)
                            load_start_time = datetime.now()
                            if table_category == 'Lob':
                                if os.path.isfile(root_folder + '/' + 'Config_Files' + '/' + 'chunk_configuration.json'):
                                    with open(root_folder + '/' + 'Config_Files' + '/' + 'chunk_configuration.json',
                                              'r') as f:
                                        chunk_configuration_dict = json.loads(f.read())

                                column_delimiter = chunk_configuration_dict['Data_File_Column_Delimiter']
                                row_delimiter = chunk_configuration_dict['Data_File_Row_Delimiter']
                                file_name = data_files_path + '/' + table_name.lower() + '_' + part_name.lower() + '_data.csv'
                                df.to_csv(file_name, sep=column_delimiter, header=column_names_list_postgres, index=None,
                                          lineterminator=row_delimiter)

                                # file_name = data_files_path + '/' + table_name.lower() + '_' + part_name.lower() + '.parquet'
                                # df.columns = df.columns.str.lower()
                                # df.to_parquet(file_name, engine="pyarrow", index=False)
                            else:
                                file_name = data_files_path + '/' + table_name.lower() + '_' + part_name.lower() + '.csv'
                                df.to_csv(file_name, sep='\t', header=column_names_list_postgres, index=None)
                            print(f"Data saved to {file_name}")
                            load_end_time = datetime.now()

                        else:
                            target_DB_details = decrypt_database_details(token_data, project_id, 'Target',
                                                                         str(target_connection_id))
                            target_connection = psycopg2.connect(database=target_DB_details['db_name'],
                                                                 user=target_DB_details['name'],
                                                                 password=target_DB_details['password'],
                                                                 host=target_DB_details['host'],
                                                                 port=target_DB_details['port'],
                                                                 options=f"-c search_path={target_schema}")
                            task_logger.debug("Connected to target database")

                            if table_category == 'Lob':
                                final_table_data_list = [tuple(x) for x in df.values]
                                query_values_list = ['%s' for i in range(len(column_names_list_postgres))]
                                query_values_str = str(tuple(query_values_list)).replace("'", "")
                                data_insert_query = "insert into {0}.{1} ({2}) values {3}".format(target_schema, table_name,
                                                                                                  column_names_list_postgres_str,
                                                                                                  query_values_str)
                                load_start_time = datetime.now()
                                try:
                                    target_cursor = target_connection.cursor()
                                    target_cursor.executemany(data_insert_query, final_table_data_list)
                                    target_connection.commit()
                                    target_cursor.close()
                                    load_end_time = datetime.now()
                                except Exception as load_error:
                                    print(f"Error occurred at loading: {load_error}")
                                    problem_rows = recursive_fetch_lob(source_connection, target_DB_details, schema_name,
                                                                       target_schema, table_name,
                                                                       number_zero_precision_columns, column_names_list,
                                                                       column_names_list_postgres, lower_bound, upper_bound,
                                                                       blob_columns_list, raw_columns_list,
                                                                       clob_columns_list, xml_columns_list)

                                    primary_key_query = f"""
                                            SELECT DISTINCT cols.column_name, atc.data_type FROM all_cons_columns cols JOIN all_constraints cons 
                                            ON cons.constraint_name = cols.constraint_name AND cons.owner = cols.owner JOIN all_tab_columns atc
                                            ON cols.owner = atc.owner AND cols.table_name = atc.table_name AND cols.column_name = atc.column_name 
                                            WHERE cons.constraint_type = 'P'AND cons.owner = '{schema_name}' AND cons.table_name = '{table_name}'
                                            """
                                    primary_key_data = execute_query(source_connection, primary_key_query)
                                    if primary_key_data:
                                        error_df = pd.DataFrame(problem_rows, columns=column_names_list_postgres)
                                        primary_key = primary_key_data[0][0]
                                        records = error_df[primary_key.lower()].values.tolist()
                                        print(records)
                                    else:
                                        print(problem_rows)
                                    ti.max_tries = 0
                                    ti.UP_FOR_RETRY
                            else:
                                load_start_time = datetime.now()
                                buffer = io.StringIO()
                                df.to_csv(buffer, sep='\t', header=None, index=None)
                                buffer.seek(0)
                                try:
                                    with target_connection.cursor() as cursor:
                                        cursor.copy_expert(
                                            "COPY " + table_name.lower() + " FROM stdin WITH CSV DELIMITER '\t' NULL ''",
                                            buffer)
                                    target_connection.commit()
                                    target_connection.close()
                                    load_end_time = datetime.now()
                                except Exception as load_error:
                                    print(f"Error occurred at loading: {load_error}")
                                    problem_rows = recursive_fetch(source_connection, target_DB_details, schema_name,
                                                                   target_schema, table_name,
                                                                   number_zero_precision_columns, column_names_list,
                                                                   column_names_list_postgres, lower_bound, upper_bound)

                                    primary_key_query = f"""
                                        SELECT DISTINCT cols.column_name, atc.data_type FROM all_cons_columns cols JOIN all_constraints cons 
                                        ON cons.constraint_name = cols.constraint_name AND cons.owner = cols.owner JOIN all_tab_columns atc
                                        ON cols.owner = atc.owner AND cols.table_name = atc.table_name AND cols.column_name = atc.column_name 
                                        WHERE cons.constraint_type = 'P'AND cons.owner = '{schema_name}' AND cons.table_name = '{table_name}'
                                        """
                                    primary_key_data = execute_query(source_connection, primary_key_query)
                                    if primary_key_data:
                                        error_df = pd.DataFrame(problem_rows, columns=column_names_list_postgres)
                                        primary_key = primary_key_data[0][0]
                                        records = error_df[primary_key.lower()].values.tolist()
                                        print(records)
                                    else:
                                        print(problem_rows)
                                    ti.max_tries = 0
                                    ti.UP_FOR_RETRY
                        load_time = (load_end_time - load_start_time).total_seconds() / 60

                        task_status = 'Success'
                        task_logger.debug('Table execution ended')

                        task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')

                        project_connection = connect_database(project_DB_details)
                        task_update(project_connection, task_id, len(table_data_list), task_status, None, task_end_time,
                                    extraction_time, transform_time, load_time)
                except Exception as e:
                    task_status = 'Fail'
                    task_error = str(e)

                    task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
                    project_connection = connect_database(project_DB_details)
                    task_update(project_connection, task_id, None, task_status, task_error, task_end_time, extraction_time,
                                transform_time,
                                load_time)
                    ti.UP_FOR_RETRY

        else:
            print("No chunks data as attempt is not specified. Attempt should be in 1,2,3,4")
            ti.UP_FOR_RETRY
    else:
        print("No data present in the table, hence moving to next task")


def post_validation_trigger(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    table_name = kwargs['table_name']
    token_data = kwargs['token_data']

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']
    scn_number = dag_config['scn_number']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, 'post_validation_task', 'Post_Validation', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    validation_flag = False
    try:

        source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
        source_connection, error = DB_connection(source_DB_details)

        source_row_count_query = f"select /*+ PARALLEL({source_DB_details['parallelprocess']}) */ count(*) from {schema_name.upper()}.{table_name.upper()} AS OF SCN {scn_number}"
        source_row_count = execute_query(source_connection, source_row_count_query)[0][0]

        target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
        target_connection, error = target_DB_connection(target_DB_details)

        target_row_count_query = f"select /*+ PARALLEL({target_DB_details['parallelprocess']}) */ count(*) from {target_schema.lower()}.{table_name.lower()}"
        target_row_count = execute_query(target_connection, target_row_count_query)[0][0]

        difference_count = abs(target_row_count - source_row_count)
        if difference_count != 0:
            validation_flag = True
            print(
                f"Row count not matching between source {source_row_count} and target {target_row_count}")

        if validation_flag:
            # insert into project db
            print(f"Post validation failed for {table_name}. Please check data migration ")

            task_end_time = datetime.now()
            task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

            dag_end_time = datetime.now()
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
        else:
            print(f"Post validation completed for {table_name}")

            task_end_time = datetime.now()
            task_update(project_connection, task_id, None, 'Success', None, task_end_time, None, None, None)
    except Exception as error:
        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        if ti.try_number == 3:
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
        ti.UP_FOR_RETRY
    return validation_flag


def get_constraints_list(connection, source_connection_id, schema_name, table_name):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_get_constraints_list(%s,%s,%s,%s);fetch all in "dataset";',
                       (source_connection_id, schema_name, table_name, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        print("Error at fetching constraints list: " + str(err))
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def deploy_table_constraints_trigger(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    target_schema = kwargs['target_schema']
    schema_name = kwargs['schema']
    table_name = kwargs['table_name']
    token_data = kwargs['token_data']

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, 'deploy_constraints_task', 'Deploy_Constraints', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]
    try:
        tables_data_list = get_constraints_list(project_connection, source_connection_id, schema_name, table_name)

        target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
        table_ddl_code_list = [i[0] for i in tables_data_list]
        print("length of table_ddl_code_list==", len(table_ddl_code_list))

        if len(table_ddl_code_list) > 0:
            target_connection = psycopg2.connect(database=target_DB_details['db_name'],
                                                 user=target_DB_details['name'],
                                                 password=target_DB_details['password'],
                                                 host=target_DB_details['host'], port=target_DB_details['port'],
                                                 options=f"-c search_path={target_schema}")
            for query in table_ddl_code_list:
                query = re.sub(rf'{schema_name}\.', target_schema + '.', query,
                               flags=re.IGNORECASE | re.DOTALL)
                cursor = target_connection.cursor()
                try:
                    cursor.execute(query)
                except psycopg2.DatabaseError as e:
                    print(f"Issue found near deploying constraint '{query}': {str(e)}")
                finally:
                    cursor.close()
                    target_connection.commit()
        print("Deployed the constraints and indexes for the table: " + table_name)

        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Success', None, task_end_time, None, None, None)
    except Exception as error:
        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        if ti.try_number == 3:
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)

        ti.UP_FOR_RETRY


def complete_validation_trigger(ti, **kwargs):
    process_type = kwargs['process_type']
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    table_name = kwargs['table_name']
    file_name = kwargs['file_name']
    token_data = kwargs['token_data']
    dag_name = kwargs['dag_id']

    source_name = 'Oracle'
    target_name = 'Postgres'

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, 'complete_validation_task', 'Complete_Validation', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    validation_flag = False

    try:
        extra_path = os.environ['EXTRA_FOLDER']
        primary_key_result_df = foreign_key_result_df = check_constraint_result_df = unique_constraint_result_df = default_constraint_result_df = not_null_constraint_result_df = index_result_df = pd.DataFrame()

        if target_connection_id in ['', None]:
            root_folder = extra_path + '/' + str(source_connection_id)
        else:
            root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)
        if target_connection_id not in ['', None]:
            xml_path = extra_path + '/' + 'Validation_Queries' + '/validation_queries.xml'
            tree = ET.parse(xml_path)
            root = tree.getroot()
            objects_list = ['Primary_Key', 'Unique_Constraint', 'Foreign_Key', 'Not_Null_Constraint',
                            'Default_Constraint', 'Check_Constraint', 'Index']

            source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
            source_connection, error = DB_connection(source_DB_details)

            target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
            target_connection, error = target_DB_connection(target_DB_details)

            for object_type in objects_list:
                source_query_tag = 'Table_Validation_Queries/Source/' + '/' + object_type
                object_source_query = list(root.iterfind(source_query_tag))[0].text
                object_source_query = object_source_query.replace('@schemaname', schema_name.upper()).replace('@order',
                                                                                                              '').replace(
                    '@degree', str(source_DB_details['parallelprocess'])).replace('@tablename', table_name.upper())
                source_object_output = execute_query(source_connection, object_source_query)

                target_query_tag = 'Table_Validation_Queries/Target/' + '/' + object_type
                object_target_query = list(root.iterfind(target_query_tag))[0].text
                object_target_query = object_target_query.replace('@schemaname', target_schema.lower()).replace(
                    '@order',
                    '').replace(
                    '@degree', str(target_DB_details['parallelprocess'])).replace('@tablename', table_name.lower())
                target_object_output = execute_query(target_connection, object_target_query)

                if (object_type == 'Primary_Key') or (object_type == 'Foreign_Key') or (
                        object_type == 'Check_Constraint') or (
                        object_type == 'Unique_Constraint'):
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Schema_Name', 'Table_Name', object_type,
                                                           'Source_Constraint_Name',
                                                           'Source_' + object_type + '_Status'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Schema_Name', 'Table_Name', object_type,
                                                           'Target_Constraint_Name',
                                                           'Target_' + object_type + '_Status'])

                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                    matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                            suffixes=('_Source', '_Target'),
                                            how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                    source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                           suffixes=('_Source', '_Target'),
                                           how='left', indicator=True)
                    source_data = source_data[source_data['_merge'] == 'left_only'].drop(
                        columns=['Target_Constraint_Name', 'Target_' + object_type + '_Status', '_merge'])
                    source_data['Status'] = f"Available in {source_name} not in {target_name}"
                    source_data['Target_Constraint_Name'] = 'Nan'
                    source_data['Target_' + object_type + '_Status'] = 'Nan'

                    target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                           suffixes=('_Source', '_Target'),
                                           how='right', indicator=True)
                    target_data = target_data[target_data['_merge'] == 'right_only'].drop(
                        columns=['Source_Constraint_Name', 'Source_' + object_type + '_Status', '_merge'])
                    target_data['Status'] = f"Available in {target_name} not in {source_name}"
                    target_data['Source_Constraint_Name'] = 'Nan'
                    target_data['Source_' + object_type + '_Status'] = 'Nan'
                    result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                    result_df.rename(columns={'_merge': 'Status'}, inplace=True, errors='ignore')

                elif object_type == "Default_Constraint":
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Schema_Name', 'Table_Name', 'Constraint_Name',
                                                           'Source_Column_Name'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Schema_Name', 'Table_Name', 'Constraint_Name',
                                                           'Target_Column_Name'])
                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Constraint_Name'],
                                            suffixes=('_Source', '_Target'), how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"
                    source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Constraint_Name'],
                                           suffixes=('_Source', '_Target'), how='left', indicator=True)
                    source_data = source_data[source_data['_merge'] == 'left_only']
                    source_data['Status'] = f"Available in {source_name} not in {target_name}"
                    source_data['Target_Column_Name'] = 'Nan'
                    target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Constraint_Name'],
                                           suffixes=('_Source', '_Target'), how='right', indicator=True)
                    target_data = target_data[target_data['_merge'] == 'right_only']
                    target_data['Status'] = f"Available in {target_name} not in {source_name}"
                    target_data['Source_Column_Name'] = 'Nan'
                    result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                    result_df = result_df.drop(columns=['_merge'], errors='ignore')

                elif object_type == "Not_Null_Constraint":
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Schema_name', 'Constraint_Name', 'Constraint_Value'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Schema_name', 'Constraint_Name', 'Constraint_Value'])
                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    matched_data = pd.merge(source_data_df, target_data_df, on=['Constraint_Name', 'Constraint_Value'],
                                            suffixes=('_Source', '_Target'), how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"
                    source_data = pd.merge(source_data_df, target_data_df, on=['Constraint_Name', 'Constraint_Value'],
                                           suffixes=('_Source', '_Target'), how='left', indicator=True)
                    source_data = source_data[source_data['_merge'] == 'left_only'].drop(columns=['Schema_name_Target',
                                                                                                  '_merge'])
                    source_data['Status'] = f"Available in {source_name} not in {target_name}"
                    target_data = pd.merge(source_data_df, target_data_df, on=['Constraint_Name', 'Constraint_Value'],
                                           suffixes=('_Source', '_Target'), how='right', indicator=True)
                    target_data = target_data[target_data['_merge'] == 'right_only'].drop(
                        columns=['Schema_name_Source', '_merge'])
                    target_data['Status'] = f"Available in {target_name} not in {source_name}"
                    result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)


                elif object_type == "Index":
                    source_data_df = pd.DataFrame(source_object_output)
                    if len(source_data_df):
                        source_data_df = source_data_df.loc[:, [0, 1, 2, 3, 6]]
                        source_data_df.columns = ['Schema_Name', 'Table_Name', 'Index_Name', 'Source_Index_Column',
                                                  'Source_Index_DDL']
                    else:
                        source_data_df = pd.DataFrame(
                            columns=['Schema_Name', 'Table_Name', 'Index_Name', 'Source_Index_Column',
                                     'Source_Index_DDL'])

                    target_data_df = pd.DataFrame(target_object_output)
                    if len(target_data_df):
                        target_data_df = target_data_df.loc[:, [0, 1, 2, 3, 6]]
                        target_data_df.columns = ['Schema_Name', 'Table_Name', 'Index_Name', 'Target_Index_Column',
                                                  'Target_Index_DDL']
                    else:
                        target_data_df = pd.DataFrame(
                            columns=['Schema_Name', 'Table_Name', 'Index_Name', 'Target_Index_Column',
                                     'Target_Index_DDL'])

                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df['Target_Index_Column'] = target_data_df['Target_Index_Column'].str.replace("['", '',
                                                                                                              regex=False).replace(
                        "']", '', regex=False).replace('["', '', regex=False).replace('"]', '', regex=False)
                    target_data_df['Index_Name'] = target_data_df['Index_Name'].str.replace('___idx1$', '', case=False,
                                                                                            regex=True)

                    matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Index_Name'],
                                            suffixes=('_Source', '_Target'), how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                    source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Index_Name'],
                                           suffixes=('_Source', '_Target'), how='left', indicator=True)
                    source_data = source_data[source_data['_merge'] == 'left_only'].drop(
                        columns=['Target_Index_Column', 'Target_Index_DDL', '_merge'])
                    source_data['Status'] = f"Available in {source_name} not in {target_name}"
                    source_data['Target_Index_Column'] = 'Nan'
                    source_data['Target_Index_DDL'] = 'Nan'

                    target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Index_Name'],
                                           suffixes=('_Source', '_Target'), how='right', indicator=True)
                    target_data = target_data[target_data['_merge'] == 'right_only'].drop(
                        columns=['Source_Index_Column', 'Source_Index_DDL', '_merge'])
                    target_data['Status'] = f"Available in {target_name} not in {source_name}"
                    target_data['Source_Index_Column'] = 'Nan'
                    target_data['Source_Index_DDL'] = 'Nan'

                    result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                    result_df = result_df.drop(columns=['_merge'], errors='ignore')
                else:
                    result_df = source_data = target_data = pd.DataFrame()

                if not result_df.empty and object_type == 'Primary_Key':
                    primary_key_result_df = result_df
                elif not result_df.empty and object_type == 'Foreign_Key':
                    foreign_key_result_df = result_df
                elif not result_df.empty and object_type == 'Check_Constraint':
                    check_constraint_result_df = result_df
                elif not result_df.empty and object_type == 'Unique_Constraint':
                    unique_constraint_result_df = result_df
                elif not result_df.empty and object_type == 'Default_Constraint':
                    default_constraint_result_df = result_df
                elif not result_df.empty and object_type == 'Not_Null_Constraint':
                    not_null_constraint_result_df = result_df
                elif not result_df.empty and object_type == 'Index':
                    index_result_df = result_df

                if not source_data.empty or not target_data.empty:
                    validation_flag = True
                    print(
                        f"{object_type}s not matching between source and target for {table_name}, please check the report for detailed information")
        if validation_flag:
            complete_validation_path = root_folder + '/' + 'Dag_Validation_Reports' + '/' + file_name.lower() + '/' + dag_name
            if not os.path.exists(complete_validation_path):
                os.makedirs(complete_validation_path)

            with pd.ExcelWriter(complete_validation_path + '/' + 'complete_validation.xlsx') as writer:
                primary_key_result_df.to_excel(writer, sheet_name='Primary_Key', index=False)
                foreign_key_result_df.to_excel(writer, sheet_name='Foreign_Key', index=False)
                check_constraint_result_df.to_excel(writer, sheet_name='Check_Constraint', index=False)
                unique_constraint_result_df.to_excel(writer, sheet_name='Unique_Constraint', index=False)
                default_constraint_result_df.to_excel(writer, sheet_name='Default_Constraint', index=False)
                not_null_constraint_result_df.to_excel(writer, sheet_name='Not_Null_Constraint', index=False)
                index_result_df.to_excel(writer, sheet_name='Index', index=False)
            print(f"Complete validation failed for {table_name}. Please check report for detailed information")
            task_end_time = datetime.now()
            task_update(project_connection, task_id, None, 'Fail', 'Validation Flag True', task_end_time, None, None,
                        None)

            dag_end_time = datetime.now()
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
        else:
            print(f"Complete validation completed for {table_name}")
            task_end_time = datetime.now()
            task_update(project_connection, task_id, None, 'Success', None, task_end_time, None, None, None)

            if process_type in ('Initial_Data_Load', 'E2E_Data_Load'):
                dag_end_time = datetime.now()
                dag_update(project_connection, dag_id, 'Success', dag_end_time)
    except Exception as error:
        print('entered exception')
        print(f"Complete validation failed for {table_name}. Please check report for detailed information")
        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        if ti.try_number == 3:
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
        ti.UP_FOR_RETRY
    return validation_flag


def remove_double_quotes(sql):
    in_single_quote = False
    to_lowercase = False
    result = []
    buffer = []
    for char in sql:
        if char == "'" and not in_single_quote:
            in_single_quote = True
            segment = ''.join(buffer)
            if to_lowercase:
                segment = re.sub(r'\"([^\"]+)\"', lambda m: m.group(1).lower(), segment)
            else:
                segment = segment.replace('"', '')
            result.append(segment)
            buffer = []
        elif char == "'" and in_single_quote:
            in_single_quote = False

        if in_single_quote:
            result.append(''.join(buffer) + char)
            buffer = []
        else:
            buffer.append(char)
    if buffer:
        segment = ''.join(buffer)
        if to_lowercase:
            segment = re.sub(r'\"([^\"]+)\"', lambda m: m.group(1).lower(), segment)
        else:
            segment = segment.replace('"', '')
        result.append(segment)
    return ''.join(result)


def transform_statement(batch_statement):
    if 'EMPTY_BLOB()' in batch_statement or 'EMPTY_CLOB()' in batch_statement:
        batch_statement = batch_statement.replace("EMPTY_BLOB()", "NULL").replace("EMPTY_CLOB()", "NULL")
    if 'HEXTORAW' in batch_statement:
        hex_list = re.findall(r'\bHEXTORAW\(.*?\)', batch_statement,
                              flags=re.DOTALL | re.I)
        for word in hex_list:
            modified_word = word.rstrip(')') + ",'hex')"
            batch_statement = batch_statement.replace(word, modified_word)
        batch_statement = re.sub(r"HEXTORAW\(", r'decode(', batch_statement)
    return batch_statement


def open_new_csv_file(file_name):
    print(f'Creating CSV file {file_name}')
    csv_file = open(file_name, mode='w', newline='')
    writer = csv.writer(csv_file)
    writer.writerow(['Start_SCN', 'Statement', 'Commit_SCN'])
    csv_file.flush()
    return csv_file, writer


def cdc_data_insert(connection, batch_number, batch_length, batch_size, load_type, data_file_name, batch_start_time,
                    batch_extraction_time, batch_transform_time, batch_load_time, batch_end_time, task_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_cdc_data_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (batch_number, batch_length, batch_size, load_type, data_file_name, batch_start_time, batch_extraction_time,
             batch_transform_time, batch_load_time, batch_end_time, task_id, 'dataset'))
        data = cursor.fetchall()
    except Exception as error:
        data = None
        print(f"Error occurred at cdc data insert: {str(error)}")
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def cdc_errors_insert(connection, batch_error_list):
    cdc_error_insert_query = "insert into public.audit_cdc_error_tracking (transaction, transaction_error, transaction_error_time, data_id) values (%s,%s,%s,%s)"

    cursor = connection.cursor()
    try:
        cursor.executemany(cdc_error_insert_query, batch_error_list)
    except Exception as error:
        print(f"Error occurred at cdc data insert: {str(error)}")
        pass
    finally:
        connection.commit()
        cursor.close()


def cdc_trigger(ti, **kwargs):
    process_type = kwargs['process_type']
    dag_name = kwargs['dag_id']
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    table_name = kwargs['table_name']
    file_name = kwargs['file_name']
    cdc_load_type = kwargs['cdc_load_type']
    token_data = kwargs['token_data']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
    source_connection, error = DB_connection(source_DB_details)

    config_data = get_config_id(project_connection, file_name, process_type)
    config_id = config_data[0][0]

    target_DB_details = {}
    extra_path = os.environ['EXTRA_FOLDER']
    if target_connection_id in ['', None]:
        root_folder = extra_path + '/' + str(source_connection_id)
    else:
        root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

        target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))

    scn_files_folder = root_folder + '/' + 'SCN_Files' + '/' + file_name.lower()
    if config_data[0][1] in ['', None]:
        os.makedirs(scn_files_folder)
        scn_fetch_query = "SELECT CURRENT_SCN FROM V$DATABASE"
        scn_data = execute_query(source_connection, scn_fetch_query)
        scn_number = scn_data[0][0]

        current_scn_file = scn_files_folder + '/' + 'current_scn_file.txt'
        with open(current_scn_file, 'w') as cscn:
            cscn.write(str(scn_number))
        current_scn = str(scn_number)

        open_scn_file = scn_files_folder + '/' + 'open_scn_file.txt'
        with open(open_scn_file, 'w') as file_openscn:
            file_openscn.write('0')
        open_scn = '0'

        commit_scn_file = scn_files_folder + '/' + 'commit_scn_file.txt'
        with open(commit_scn_file, 'w') as file_csn:
            file_csn.write('0')
        commit_scn = '0'

        transaction_insert(project_connection, config_id, scn_number, None, 'Running')
    else:
        current_scn_file = scn_files_folder + '/' + 'current_scn_file.txt'
        with open(current_scn_file, 'r') as cscn:
            current_scn = cscn.readline()

        open_scn_file = scn_files_folder + '/' + 'open_scn_file.txt'
        with open(open_scn_file, 'r') as file_openscn:
            open_scn = file_openscn.readline()

        commit_scn_file = scn_files_folder + '/' + 'commit_scn_file.txt'
        with open(commit_scn_file, 'r') as file_csn:
            commit_scn = file_csn.readline()
    print(f'current_scn: {current_scn}, open_scn: {open_scn}, commit_scn: {commit_scn}')

    dag_id = dag_insert(project_connection, dag_name, 'CDC', schema_name, target_schema, table_name,
                        None,
                        None, None, None,
                        'Running', config_id)
    dag_id = dag_id[0][0]

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, 'cdc_' + str(file_name), 'CDC', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]
    if open_scn != '0' and int(current_scn) > int(open_scn):
        open_scn_list = [i for i in open_scn.split(',') if i != '']
        current_scn = str(min(open_scn_list))

    if commit_scn == '0':
        commit_scn = current_scn

    cdc_data_folder = root_folder + '/' + 'CDC_Data_Files' + '/' + file_name.lower()
    if not os.path.exists(cdc_data_folder):
        os.makedirs(cdc_data_folder)

    batch_number = 1

    current_file_name, file_limit_size, csv_file = None, '', ''
    if cdc_load_type == 'File':
        file_limit_size = 1 * 1024 * 1024
        start_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S-%f")
        current_file_name = cdc_data_folder + '/' + f"CDC_{file_name.lower()}_{start_time}_batch_{batch_number}.csv"
        csv_file, writer = open_new_csv_file(current_file_name)

    start_current_scn, start_commit_scn = '0', '0'

    try:
        while True:
            if start_current_scn != str(current_scn) and start_commit_scn != str(commit_scn):
                start_current_scn, start_commit_scn = current_scn, commit_scn

                log_file_list = []
                source_cursor = source_connection.cursor()

                get_sequence_query = f"SELECT MIN(SEQUENCE#) AS SEQ# FROM V$LOG_HISTORY WHERE FIRST_CHANGE# <= {current_scn} AND NEXT_CHANGE# > {current_scn}"
                source_cursor.execute(get_sequence_query)
                sequence_number = source_cursor.fetchone()
                sequence_number = sequence_number[0]

                if sequence_number is not None:
                    get_log_paths_query = (
                        f"SELECT 'DBMS_LOGMNR.ADD_LOGFILE(''' ||NAME||''');' AS name, SEQUENCE#, THREAD#, deleted FROM V$ARCHIVED_LOG WHERE SEQUENCE# >={sequence_number}")
                    source_cursor.execute(get_log_paths_query)
                    log_paths = source_cursor.fetchmany(100)
                else:
                    get_log_paths_query = f"""SELECT DISTINCT 'DBMS_LOGMNR.ADD_LOGFILE(''' ||MEMBER||''');' AS name,e.* FROM V$LOGFILE e JOIN v$log f ON e.GROUP# =f.GROUP# where e.IS_RECOVERY_DEST_FILE= 'NO'  and ARCHIVED = 'NO'"""
                    source_cursor.execute(get_log_paths_query)
                    log_paths = source_cursor.fetchall()

                if log_paths:
                    log_file_list.extend([item[0] for item in log_paths])
                    log_path_string = '\n'.join([str(item[0]) for item in log_paths])
                    log_path_formatted_query = """begin \n{}\n end;""".format(log_path_string)
                    source_cursor.execute(log_path_formatted_query)

                    log_miner_query = f"""
                        begin
                       DBMS_LOGMNR.START_LOGMNR(
                          STARTSCN =>{current_scn}, -- or use STARTTIME or STARTDATE as needed
                           OPTIONS => DBMS_LOGMNR.NO_ROWID_IN_STMT + DBMS_LOGMNR.STRING_LITERALS_IN_STMT + DBMS_LOGMNR.DICT_FROM_ONLINE_CATALOG
                       ); END;"""
                    source_cursor.execute(log_miner_query)

                    data_fetch_query = f"""SELECT DISTINCT (e.XIDUSN || '.' || e.XIDSLT || '.' || e.XIDSQN) AS XID,
                                                    e.USERNAME,
                                                    e.SCN,f.SCN as SCN_FOR_COMMIT,
                                                    f.START_SCN,
                                                    e.SQL_REDO,
                                                    f.COMMIT_SCN,
                                                    e.OPERATION, f.OPERATION as OPERATION_COMMIT,
                                                    e.ROLLBACK
                                    FROM V$LOGMNR_CONTENTS e
                                    INNER JOIN V$LOGMNR_CONTENTS f
                                        ON (e.XIDUSN || '.' || e.XIDSLT || '.' || e.XIDSQN) = (f.XIDUSN || '.' || f.XIDSLT || '.' || f.XIDSQN)
                                    WHERE e.OPERATION IN ('INSERT', 'UPDATE', 'DELETE')
                                    AND e.SEG_OWNER || '.' || e.TABLE_NAME in ('{table_name}') and f.SCN > {commit_scn}
                                      AND (e.XIDUSN || '.' || e.XIDSLT || '.' || e.XIDSQN) NOT IN (
                                          SELECT DISTINCT (r.XIDUSN || '.' || r.XIDSLT || '.' || r.XIDSQN) AS rollback_xid
                                          FROM V$LOGMNR_CONTENTS r
                                          WHERE r.ROLLBACK = 1
                                      )
                                    ORDER BY e.SCN"""
                    source_cursor.execute(data_fetch_query)

                    while True:
                        extract_start_time = datetime.now()
                        batch_data = source_cursor.fetchmany(1000)
                        extract_end_time = datetime.now()
                        extract_time = (extract_end_time - extract_start_time).total_seconds() / 60
                        if batch_data:
                            batch_start_time = datetime.now()
                            transform_start_time = datetime.now()
                            batch_df = pd.DataFrame(batch_data,
                                                    columns=['XID', 'USERNAME', 'SCN', 'SCN_FOR_COMMIT', 'START_SCN',
                                                             'SQL_REDO',
                                                             'COMMIT_SCN', 'OPERATION', 'OPERATION_COMMIT', 'ROLLBACK'])

                            open_scn_list = \
                                batch_df.groupby('XID').filter(lambda x: 'COMMIT' not in x['OPERATION_COMMIT'].values)[
                                    'SCN'].drop_duplicates().tolist()
                            if open_scn_list:
                                with open(open_scn_file, 'w') as file_openscn:
                                    file_openscn.write(','.join(open_scn_list))

                            committed_transactions = batch_df[batch_df['OPERATION_COMMIT'] == 'COMMIT']
                            committed_scn_list = committed_transactions['SCN'].drop_duplicates().tolist()

                            if committed_scn_list:
                                batch_end_scn = int(batch_df['SCN'].max())
                                batch_commit_scn = int(committed_transactions['SCN_FOR_COMMIT'].max())
                                data_df = committed_transactions[['SCN', 'SQL_REDO', 'SCN_FOR_COMMIT']]
                                data_df['SQL_REDO'] = data_df['SQL_REDO'].apply(remove_double_quotes)
                                data_df['SQL_REDO'] = data_df['SQL_REDO'].apply(transform_statement)
                                if len([i for i in schema_name.split(',') if i != '']) == 1 and target_schema not in [
                                    '', None]:
                                    data_df['SQL_REDO'] = data_df['SQL_REDO'].apply(lambda batch_statement: re.sub(
                                        rf'\b{schema_name}\s*\.',
                                        target_schema + '.',
                                        batch_statement,
                                        flags=re.IGNORECASE | re.DOTALL
                                    ))
                                transform_end_time = datetime.now()
                                transform_time = (transform_end_time - transform_start_time).total_seconds() / 60

                                batch_error_list, load_time, cdc_data_file = [], None, None
                                if cdc_load_type == 'File':
                                    data_df.to_csv(csv_file, header=False, index=False)
                                    csv_file.flush()
                                    file_size = os.path.getsize(current_file_name)
                                    cdc_data_file = current_file_name

                                    if file_size >= file_limit_size:
                                        csv_file.close()
                                        end_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S-%f")
                                        renamed_file_name = current_file_name.replace('.csv', f'_{end_time}.csv')

                                        os.rename(current_file_name, renamed_file_name)
                                        print(f"File renamed to: {renamed_file_name}")

                                        start_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S-%f")
                                        current_file_name = cdc_data_folder + '/' + f"CDC_{file_name.lower()}_{start_time}_batch_{batch_number}.csv"
                                        csv_file, writer = open_new_csv_file(current_file_name)

                                    current_scn, commit_scn = batch_end_scn + 1, batch_commit_scn
                                    if isinstance(current_scn, int) and current_scn is not None:
                                        with open(current_scn_file, 'w') as f_csn:
                                            f_csn.write(str(current_scn))

                                    if isinstance(commit_scn, int) and commit_scn != 0:
                                        with open(commit_scn_file, 'w') as f_commit:
                                            f_commit.write(str(commit_scn))

                                elif cdc_load_type == 'Database':
                                    load_start_time = datetime.now()
                                    batch_statement_list = data_df['SQL_REDO'].values.tolist()
                                    batch_statement = '\n'.join(batch_statement_list)
                                    target_connection, error = target_DB_connection(target_DB_details)
                                    target_cursor = target_connection.cursor()

                                    try:
                                        target_cursor.execute(batch_statement)

                                        current_scn, commit_scn = batch_end_scn + 1, batch_commit_scn
                                        if isinstance(current_scn, int) and current_scn is not None:
                                            with open(current_scn_file, 'w') as f_csn:
                                                f_csn.write(str(current_scn))

                                        if isinstance(commit_scn, int) and commit_scn != 0:
                                            with open(commit_scn_file, 'w') as f_commit:
                                                f_commit.write(str(commit_scn))

                                    except psycopg2.Error as error:
                                        for index, record in data_df.iterrows():
                                            record_end_scn = record['SCN']
                                            record_commit_scn = int(record['SCN_FOR_COMMIT'])
                                            statement = record['SQL_REDO']

                                            try:
                                                target_cursor.execute(statement)

                                                current_scn, commit_scn = record_end_scn + 1, record_commit_scn
                                                if isinstance(current_scn, int) and current_scn is not None:
                                                    with open(current_scn_file, 'w') as f_csn:
                                                        f_csn.write(str(current_scn))

                                                if isinstance(commit_scn, int) and commit_scn != 0:
                                                    with open(commit_scn_file, 'w') as f_commit:
                                                        f_commit.write(str(commit_scn))
                                            except psycopg2.Error as error:
                                                print("Error occurred at statement: " + str(
                                                    statement) + " loading to postgres: " + str(
                                                    error))

                                                error_tuple = (tuple(record.values), str(error), datetime.now())
                                                batch_error_list.append(error_tuple)
                                    finally:
                                        target_connection.commit()
                                        target_cursor.close()

                                    load_end_time = datetime.now()
                                    load_time = (load_end_time - load_start_time).total_seconds() / 60

                                batch_end_time = datetime.now()
                                batch_number = batch_number + 1

                                batch_length = len(data_df['SQL_REDO'].values.tolist())
                                batch_size = sum(
                                    sys.getsizeof(statement) for statement in data_df['SQL_REDO'].values.tolist())

                                project_connection = connect_database(project_DB_details)
                                cdc_data_id = cdc_data_insert(project_connection, batch_number, batch_length,
                                                              batch_size, cdc_load_type,
                                                              cdc_data_file, batch_start_time, extract_time,
                                                              transform_time, load_time,
                                                              batch_end_time, task_id)

                                if batch_error_list:
                                    batch_error_list = [t + (cdc_data_id,) for t in batch_error_list]
                                    cdc_errors_insert(project_connection, batch_error_list)
                        else:
                            break
                else:
                    print("No log paths found")
            else:
                print("No transactions are being performed on database")
    except Exception as error:
        print(f"CDC failed for {file_name}")
        project_connection = connect_database(project_DB_details)

        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        if ti.try_number == 3:
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
        ti.UP_FOR_RETRY


def load_files_tracking_insert(connection, data_file_name, start_time, load_time, end_time, task_id):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_audit_load_files_tracking_insert(%s,%s,%s,%,%s);fetch all in "dataset";',
                       (data_file_name, start_time, load_time, end_time, task_id, 'dataset'))
        data = cursor.fetchall()
    except Exception as error:
        data = None
        print(f"Error occurred at cdc data insert: {str(error)}")
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def load_files_errors_insert(connection, batch_error_list):
    cdc_error_insert_query = "insert into public.audit_load_files_error_tracking (transaction, transaction_error, transaction_error_time, load_file_id) values (%s,%s,%s,%s,%s)"

    cursor = connection.cursor()
    try:
        cursor.executemany(cdc_error_insert_query, batch_error_list)
    except Exception as error:
        print(f"Error occurred at cdc data insert: {str(error)}")
        pass
    finally:
        connection.commit()
        cursor.close()


def load_files_trigger(ti, **kwargs):
    dag_name = kwargs['dag_id']
    process_type = kwargs['process_type']
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    file_name = kwargs['file_name']
    token_data = kwargs['token_data']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    config_data = get_config_id(project_connection, file_name, process_type)
    config_id = config_data[0][0]

    dag_id = dag_insert(project_connection, dag_name, 'Load_Files', None, None, None,
                        None, None, None, None,
                        'Running', config_id)
    dag_id = dag_id[0][0]

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, 'load_files_' + str(file_name), 'Load_Files', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    extra_path = os.environ['EXTRA_FOLDER']

    root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)
    target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))

    archive_folder = root_folder + '/' + 'Archived_CDC_Files' + '/' + file_name.lower()
    if not os.path.exists(archive_folder):
        os.makedirs(archive_folder)

    cdc_data_folder = root_folder + '/' + 'CDC_Data_Files' + '/' + file_name.lower()
    load_files_tracker = cdc_data_folder + '/' + 'load_files_tracker.txt'

    processing_file_name, processing_index = '', ''
    if os.path.isfile(load_files_tracker):
        with open(load_files_tracker, 'r') as f:
            index_data = f.read().strip().split(',')
            print('Position Data from the File ', index_data)
            if len(index_data) == 2:
                processing_file_name, processing_index = index_data[0], int(index_data[1])
    try:

        batch_size = 1000
        while True:
            cdc_data_files = [cdc_data_folder + '/' + file for file in os.listdir(cdc_data_folder) if
                              file.endswith('.csv')]

            for data_file in cdc_data_files:
                batch_split_list = data_file.split('batch_')[1].split('_')
                if len(batch_split_list) > 1:
                    if processing_file_name == data_file.split('/')[-1] and processing_index != '':
                        if processing_index == '-1':
                            shutil.move(data_file, archive_folder + '/' + data_file.split('/')[-1])
                            continue
                        else:
                            start_index = int(processing_index) + 1
                    else:
                        start_index = 0
                    batch_error_list, load_end_time = [], None
                    data = pd.read_csv(data_file)
                    load_start_time = datetime.now()
                    if len([i for i in range(start_index, len(data))]):
                        for i in range(start_index, len(data), batch_size):
                            batch_data = data.iloc[i:i + batch_size]
                            if batch_data:
                                target_connection, error = target_DB_connection(target_DB_details)
                                target_cursor = target_connection.cursor()
                                try:
                                    batch_statement = '\n'.join(batch_data['Statement'].values.tolist())
                                    target_cursor.execute(batch_statement)
                                    target_connection.commit()

                                    with open(load_files_tracker, "w") as f:
                                        f.write(f"{data_file.split('/')[-1]}, {i + batch_size}")
                                    print(f'Data Loaded into Target for batch {i}:{i + batch_size}')

                                except Exception as batch_error:
                                    for index, record in batch_data.iterrows():
                                        statement = record['Statement']
                                        try:
                                            target_cursor.execute(statement)
                                            target_connection.commit()
                                        except Exception as bi_error:
                                            print("Error occurred at statement: " + str(
                                                record[0]) + " loading to target: " + str(error))

                                            error_tuple = (
                                                data_file, tuple(record.values), str(bi_error), datetime.now())
                                            batch_error_list.append(error_tuple)

                                            error_tuple = (
                                                config_id, int(data_file.split('batch_')[1].split('_')[0]),
                                                tuple(record.values), str(bi_error), datetime.now())
                                            batch_error_list.append(error_tuple)

                                        with open(load_files_tracker, "w") as f:
                                            f.write(f"{data_file.split('/')[-1]},{index}")
                            else:
                                with open(load_files_tracker, "w") as f:
                                    f.write(f"{data_file.split('/')[-1]},-1")
                        load_end_time = datetime.now()
                    else:
                        with open(load_files_tracker, "w") as f:
                            f.write(f"{data_file.split('/')[-1]},-1")

                    destination = archive_folder + '/' + data_file.split('/')[-1]
                    shutil.move(data_file, destination)
                    print(f"{data_file.split('/')[-1]} is being archived as it is processed")

                    load_time = (load_end_time - load_start_time).total_seconds() / 60

                    load_file_id = load_files_tracking_insert(project_connection, data_file, load_start_time, load_time,
                                                              load_end_time, task_id)

                    if batch_error_list:
                        batch_error_list = [t + (load_file_id,) for t in batch_error_list]
                        load_files_errors_insert(project_connection, batch_error_list)
                else:
                    print('File is not ready to process as it is still in extraction phase')
    except Exception as error:
        print(f"Load files task failed for {file_name}")
        project_connection = connect_database(project_DB_details)

        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        if ti.try_number == 3:
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
        ti.UP_FOR_RETRY


def cpu_memory_insert(connection, connection_id, connection_type, cpu_cores, cpu_utilization_percentage,
                      memory_utilization, memory_utilization_percentage, error):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_cpu_memory_insert(%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (connection_id, connection_type, cpu_cores, cpu_utilization_percentage, memory_utilization,
             memory_utilization_percentage, error, 'dataset'))
        data = cursor.fetchall()
    except Exception as error:
        print(f"Error occurred at cpu memory insert: {error}")
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def cpu_memory_utilization_trigger(ti, **kwargs):
    project_id = kwargs['project_id']
    connection_id = kwargs['connection_id']
    connection_type = kwargs['connection_type']
    token_data = kwargs['token_data']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    DB_details = decrypt_database_details(token_data, project_id, connection_type, str(connection_id))

    try:
        connection, query = '', ''
        if connection_type == 'Source':
            query = """
                    WITH
                    -- CPU Calculations
                    cpu_cores AS (
                        SELECT value AS num_cores
                        FROM v$osstat
                        WHERE stat_name = 'NUM_CPUS'
                    ),
                    cpu_usage AS (
                        SELECT SUM(value) AS total_cpu_usage
                        FROM v$sys_time_model
                        WHERE stat_name IN ('DB CPU', 'background cpu time')
                    ),
                    elapsed_time AS (
                        SELECT value AS elapsed_seconds
                        FROM v$sys_time_model
                        WHERE stat_name = 'DB time'
                    ),
                    -- Memory Calculations
                    sga_memory AS (
                        SELECT SUM(bytes) AS total_sga
                        FROM v$sgastat
                    ),
                    pga_memory AS (
                        SELECT SUM(value) AS total_pga
                        FROM v$pgastat
                        WHERE name = 'total PGA allocated'
                    ),
                    total_physical_memory AS (
                        SELECT value AS total_memory
                        FROM v$osstat
                        WHERE stat_name = 'PHYSICAL_MEMORY_BYTES'
                    ),
                    sga_parameters AS (
                        SELECT
                            name,
                            value/(1024*1024) AS value
                        FROM
                            v$parameter
                        WHERE
                            name IN ('sga_target', 'sga_max_size')
                    )
                SELECT
                    -- CPU Utilization
                    (SELECT num_cores FROM cpu_cores) AS cpu_cores,
                    ROUND(
                        (SELECT total_cpu_usage FROM cpu_usage) /
                        ((SELECT num_cores FROM cpu_cores) * (SELECT elapsed_seconds FROM elapsed_time)) * 100,
                        2
                    ) AS cpu_utilization_percentage,
                    -- Memory Utilization
                    ROUND(((SELECT total_sga FROM sga_memory) + (SELECT total_pga FROM pga_memory)) / (1024 * 1024), 2) AS db_total_utlizn_mem_mb,    
                    ROUND((((SELECT total_sga FROM sga_memory)/(1024 * 1024) + (SELECT total_pga FROM pga_memory)/(1024 * 1024)) / ((SELECT value FROM sga_parameters WHERE name = 'sga_max_size') + (SELECT value FROM sga_parameters WHERE name = 'sga_target'))) * 100, 2) AS db_vs_total_db_mem_util_perctg
                FROM
                    dual
            """
            connection, error = DB_connection(DB_details)

        if connection_type == 'Target':
            query = ''
            connection, error = target_DB_connection(DB_details)

        cpu_memory_data = execute_query(connection, query)
        if cpu_memory_data:
            cpu_cores, cpu_utilization_percentage, memory_utilization, memory_utilization_percentage = \
                cpu_memory_data[0][0], cpu_memory_data[0][1], cpu_memory_data[0][2], cpu_memory_data[0][3]
            project_connection = connect_database(project_DB_details)
            cpu_memory_insert(project_connection, connection_id, connection_type, cpu_cores, cpu_utilization_percentage,
                              memory_utilization, memory_utilization_percentage, None)
    except Exception as err:
        project_connection = connect_database(project_DB_details)
        cpu_memory_insert(project_connection, connection_id, connection_type, None, None,
                          None, None, str(err))
