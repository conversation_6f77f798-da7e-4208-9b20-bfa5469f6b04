import os, sys, re, shutil, json, math, time
from import_file import import_file
import pandas as pd
from common_modules.api import api_authentication, decrypt_database_details
from common_modules.stored_procedures import insert_config_name

def copy_data_compare_dag_files(file_name, config_files_path, local_migration_path, dag_path):
    source_file = local_migration_path + '/' + 'Dag_Files' + '/' + 'data_compare_dag_file.py'
    target_file = dag_path + '/' + f'data_compare_dag_file_{file_name}.py'

    with open(source_file, 'r') as file:
        file_content = file.read()
        file_replaced_content = re.sub(r'@Config_File_Path',
                                       rf'{config_files_path}/Data_Compare/{file_name}.xlsx'.replace('\\', '/'),
                                       file_content)
        file_replaced_content = re.sub(r'@Config_File_Folder',
                                       config_files_path.replace('\\', '/'),
                                       file_replaced_content)
    with open(target_file, 'w') as f:
        f.write(file_replaced_content)

    dag_triggers_file = local_migration_path + '/' + 'Dag_Files' + '/' + 'validation_triggers.py'
    trigger_destination_file = dag_path + '/' + 'validation_triggers.py'
    with open(dag_triggers_file, 'rb') as f:
        file_content = f.read()
    with open(trigger_destination_file, 'wb') as f:
        f.write(file_content)


def data_compare_trigger(task_name, project_id, migration_name, source_connection_id, target_connection_id, schema_name,
                         target_schema, table_name, compare_type, request_cpu, limit_cpu, file_name, cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')
        dag_path = getattr(import_object, 'Dag_Path')

        working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')
        local_migration_path = local_root_path + '/' + 'Migrations' + '/' + migration_name

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)

            token_data = api_authentication()

            source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
            source_function_call = getattr(import_object, 'DB_connection')

            project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
            project_function_call = getattr(import_object, 'connect_database')

            execute_query_function_call = getattr(import_object, 'execute_query')

            config_files_path = root_path + '/' + str(source_connection_id) + '/' + str(
                target_connection_id) + '/' + 'Config_Files'

            if not os.path.exists(config_files_path + '/' + 'Data_Compare'):
                os.makedirs(config_files_path + '/' + 'Data_Compare')

            if not os.path.exists(working_directory_path):
                os.makedirs(working_directory_path)

            if not os.path.isfile(config_files_path + '/' + 'chunk_configuration.json'):
                shutil.copyfile(local_migration_path + '/' + 'chunk_configuration.json', config_files_path + '/' + 'chunk_configuration.json')

            with open(config_files_path + '/' + 'chunk_configuration.json', 'r') as f:
                chunk_configuration = json.loads(f.read())
                chunk_rows = chunk_configuration['Chunk_rows']

            schema_list_query = """
            SELECT u.username FROM DBA_USERS u
            LEFT JOIN
                DBA_SEGMENTS s ON u.username = s.owner
            WHERE
                u.username NOT IN (
                    'SYSTEM', 'SYS', 'APPOQSYS', 'REMOTE_SCHEDULER_AGENT', 'DBSFWMUSER',
                    'CTXSYS', 'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS',
                    'DVSYS', 'GSMADMIN_INTERNAL', 'ORDPLUGINS', 'ORDDATA', 'MDSYS',
                    'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM', 'XDB', 'WMSYS',
                    'ORDSYS', 'DBSNMP', 'DVF', 'APEX_030200', 'EXFSYS', 'OWBSYS',
                    'ONBSYS_AUDIT', 'SYSMAN', 'SCOTT'
                )
                AND u.account_status = 'OPEN'
            GROUP BY u.username ORDER BY u.username
            """

            table_list_query = """
            select DISTINCT OBJECT_NAME from dba_objects a where NOT exists (select 1 from dba_mviews MV
            WHERE MV.MVIEW_NAME=a.object_name AND MV.OWNER=upper('@schemaname')) AND A.OWNER=upper('@schemaname') and
            OBJECT_NAME not like '%$%' and OBJECT_NAME not like 'SYS_%' AND A.OBJECT_TYPE='TABLE' 
            AND a.STATUS = 'VALID' AND TEMPORARY='N' ORDER BY 1
            """

            tables_list = []
            source_connection, error = source_function_call(source_DB_details)
            if table_name != '':
                tables_list = [(schema_name.upper(), i.upper()) for i in table_name.split(',') if i.strip()]
            elif schema_name != '' and table_name == '':
                schema_list = [i for i in schema_name.split(',') if i.strip()]
                if schema_list:
                    for schema in schema_list:
                        table_list_query = table_list_query.replace('@schemaname', schema.upper())
                        schema_table_list = execute_query_function_call(source_connection, table_list_query)
                        if schema_table_list:
                            tables_list.extend([(schema.upper(), i[0]) for i in schema_table_list])
            elif schema_name == '' and table_name == '':
                schema_list = execute_query_function_call(source_connection, schema_list_query)
                if schema_list:
                    for schema in schema_list:
                        table_list_query = table_list_query.replace('@schemaname', schema.upper())
                        schema_table_list = execute_query_function_call(source_connection, table_list_query)
                        if schema_table_list:
                            tables_list.extend([(schema.upper(), i[0]) for i in schema_table_list])

            compare_file_name = 'data_compare_' + file_name.replace('.xlsx', '')
            compare_config_file_path = config_files_path + '/' + 'Data_Compare' + '/' + compare_file_name + '.xlsx'
            temp_compare_config_path = working_directory_path + '/' + compare_file_name + '.xlsx'

            dag_tasks_list = []
            no_data_tables_list = []
            with pd.ExcelWriter(temp_compare_config_path, engine="xlsxwriter") as writer:
                for table_tuple in tables_list:
                    data_query = """
                        WITH cte AS (
                        SELECT /*+ PARALLEL({1}) */  l.owner AS owner, l.table_name AS table_name,
                        ROUND(s.bytes / (1024 * 1024) , 2) AS size_mb
                        FROM dba_segments s
                        JOIN dba_lobs l ON s.segment_name = l.segment_name
                        WHERE l.owner = '@schemaname' AND l.table_name IN ('@tablename')
                        UNION ALL
                        SELECT s.OWNER AS owner ,s.SEGMENT_NAME AS table_name,ROUND(SUM(s.bytes) / (1024 * 1024), 2) AS size_mb
                        FROM
                            DBA_segments s  JOIN
                        DBA_TABLES d ON s.owner = d.owner AND s.segment_name = d.table_name
                        WHERE
                        segment_type like 'TABLE%'
                        AND s.OWNER = '@schemaname' AND s.segment_name IN ('@tablename')
                        GROUP BY s.OWNER,SEGMENT_NAME
                        ) SELECT /*+ PARALLEL(@parallelsize) */  OWNER,TABLE_NAME,to_number(extractvalue(xmltype(dbms_xmlgen.getxml('select /*+ PARALLEL(@parallelsize) */ count(*) c from '||owner||'.'||TABLE_NAME)),'/ROWSET/ROW/C')) as num_rows,round(SUM(size_mb),2) AS MB
                        FROM cte
                        GROUP BY OWNER,TABLE_NAME
                        """

                    data_query = data_query.replace('@schemaname', table_tuple[0]).replace(
                        '@tablename', table_tuple[1]).replace('@parallelsize',
                                                              str(source_DB_details['parallelprocess']))
                    table_data = execute_query_function_call(source_connection, data_query)[0]

                    if table_data and int(table_data[0][2]) != 0:
                        table_data = table_data[0]
                        if compare_type == 'Detailed_Compare':
                            source_primary_key_string = ''
                            datatypes_query = "SELECT COLUMN_NAME,DATA_TYPE FROM ALL_TAB_COLUMNS WHERE OWNER = '@schemaname' AND table_name = '@tablename' ORDER BY COLUMN_ID"
                            datatypes_query = datatypes_query.replace('@schemaname', table_tuple[0]).replace('@tablename',
                                                                                                             table_tuple[1])
                            datatypes_data = execute_query_function_call(source_connection, datatypes_query)

                            if datatypes_data:
                                datatypes_list = [i[1] for i in datatypes_data if i]
                                columns_list = [i[0] for i in datatypes_data if i]

                                target_columns_list = []
                                for column_tuple in datatypes_data:
                                    if column_tuple[1] == 'NUMBER':
                                        target_columns_list.append(column_tuple[0] + "::text")
                                    elif column_tuple[1] == 'TIMESTAMP(6)':
                                        target_columns_list.append(
                                            "coalesce(to_char(" + column_tuple[0] + ",'DD-MON-YY HH:MI:SS.US AM'),'')")
                                    elif 'TIMESTAMP' in column_tuple[1].upper():
                                        target_columns_list.append("coalesce(" + column_tuple[0] + ",'')")
                                    else:
                                        target_columns_list.append(column_tuple[0])

                                if not any(
                                        element in datatypes_list for element in ['CLOB', 'BLOB', 'XMLTYPE', 'NCLOB', 'BFILE', 'RAW']):

                                    if table_data:
                                        primary_key_query = """
                                            SELECT DISTINCT cols.column_name, atc.data_type FROM all_cons_columns cols JOIN all_constraints cons 
                                            ON cons.constraint_name = cols.constraint_name AND cons.owner = cols.owner JOIN all_tab_columns atc
                                            ON cols.owner = atc.owner AND cols.table_name = atc.table_name AND cols.column_name = atc.column_name 
                                            WHERE cons.constraint_type = 'P'AND cons.owner = '@schemaname' AND cons.table_name = '@tablename'
                                            """

                                        primary_key_query = primary_key_query.replace('@schemaname', table_tuple[0]).replace(
                                            '@tablename', table_tuple[1])
                                        primary_key_data = execute_query_function_call(source_connection, primary_key_query)

                                        if primary_key_data:
                                            source_primary_key_list = [i[0] for i in primary_key_data]
                                            source_primary_key_string = ','.join(source_primary_key_list)

                                            target_primary_key_list = []
                                            for primary_tuple in primary_key_data:
                                                if primary_tuple[1] == 'TIMESTAMP(6)':
                                                    target_primary_key_list.append("coalesce(to_char(" + primary_tuple[
                                                        0] + ",'DD-MON-YY HH:MI:SS.US AM'),'')")
                                                elif 'TIMESTAMP' in primary_tuple[1].upper():
                                                    target_primary_key_list.append("coalesce(" + primary_tuple[0] + ",'')")
                                                else:
                                                    target_primary_key_list.append(primary_tuple[0])
                                            target_primary_key_string = ','.join(target_primary_key_list)


                                        if source_primary_key_string == '':
                                            chunk_value = 1
                                            chunk_size = math.ceil(table_data[3])
                                            chunk_query = ''
                                        else:
                                            chunk_value = math.ceil(table_data[2] / int(chunk_rows))
                                            chunk_size = math.ceil(table_data[3] / chunk_value)
                                            chunk_query = f"""select part,min(concat),max(concat),count(1)
                                                    from (select concat,ntile({chunk_value}) over(order by concat) part
                                                    from (select {source_primary_key_string.replace(',', "||','||")} concat from {table_tuple[0].upper()}.{table_data[1].upper()}
                                                    )) group by part order by part"""

                                            cursor = source_connection.cursor()
                                            cursor.execute("alter session set nls_date_format='YYYY-MM-DD HH24:MI:SS'")
                                            chunk_data = execute_query_function_call(source_connection, chunk_query)

                                        source_queries_list = []
                                        target_queries_list = []

                                        if target_schema in ['', None]:
                                            target_schema = schema_name

                                        if source_primary_key_string == '':
                                            source_query = f""" select {','.join(columns_list)} from {table_tuple[0].upper()}.{table_tuple[1].upper()}"""
                                            source_queries_list.append(source_query)

                                            target_query = f"""select * from {target_schema}.{table_tuple[1]}"""
                                            target_queries_list.append(target_query)
                                        else:
                                            for value in chunk_data:
                                                source_query = f""" select {','.join(columns_list)} from {table_tuple[0].upper()}.{table_tuple[1].upper()}  where {source_primary_key_string.replace(',', "||','||")} between '{value[1]}' and '{value[2]}' order by {source_primary_key_string} """
                                                source_queries_list.append(source_query)

                                                target_query = f""" select * from {target_schema}.{table_tuple[1]}  where ({target_primary_key_string}) between ('{str(value[1]).replace(",", "','")}') and ('{str(value[2]).replace(",", "','")}') order by {source_primary_key_string} """
                                                target_queries_list.append(target_query)

                                        dag_name = f'Detailed_Compare_{table_tuple[0]}_{table_tuple[1]}_{file_name.upper()}'
                                        for count in range(len(source_queries_list)):
                                            created_tuple = (table_tuple[0],
                                                             table_tuple[1], dag_name, 'Part_' + str(count + 1), chunk_size,
                                                             source_queries_list[count], target_queries_list[count],
                                                             source_primary_key_string)
                                            dag_tasks_list.append(created_tuple)
                                    else:
                                        no_data_tables_list.append(table_tuple)
                        elif compare_type == 'Sample_Compare':
                            dag_name = f'Sample_Compare_{file_name.upper()}'
                            created_tuple = (table_tuple[0], table_tuple[1], dag_name, 'Part_1', '', '', '','')
                            dag_tasks_list.append(created_tuple)
                    else:
                        no_data_tables_list.append(table_tuple)

                dag_names_list = []
                if dag_tasks_list:
                    tasks_df = pd.DataFrame(dag_tasks_list,
                                            columns=['Schema_Name', 'Table_Name', 'Dag Name', 'Part_Name',
                                                     'Chunk_Size', 'Source_Query', 'Target_Query', 'Primary_Key'])
                    tasks_df.fillna('', inplace=True)
                    tasks_df.to_excel(writer, sheet_name='Dags', index=False)
                    dag_names_list = tasks_df['Dag Name'].drop_duplicates().to_list()

                if no_data_tables_list:
                    no_data_table_df = pd.DataFrame(no_data_tables_list,
                                                    columns=['Schema_Name', 'Table_Name'])
                    no_data_table_df.to_excel(writer, sheet_name='Lob_No_Data_Tables', index=False)

                if dag_names_list:
                    priority_list = []
                    for dag_name in dag_names_list:
                        epoch_time = time.time()
                        priority_tuple = (dag_name, int(epoch_time))
                        priority_list.append(priority_tuple)
                        time.sleep(1)
                    priority_df = pd.DataFrame(priority_list, columns=['Dag Name', 'Priority_Weight'])
                    priority_df.to_excel(writer, sheet_name="Priority_Weights", index=False)

                config_list = [
                    (task_name, int(project_id), migration_name, str(source_connection_id), str(target_connection_id),
                     schema_name, target_schema, table_name, compare_type, request_cpu, limit_cpu, compare_file_name)]
                config_df = pd.DataFrame(config_list,
                                         columns=['Process_Type', 'Project_Id', 'Migration_Name',
                                                  'Source_Connection_Id', 'Target_Connection_Id', 'Schema',
                                                  'Target_Schema', 'Table_Name', 'Compare_Type',
                                                  'Request_CPU', 'Limit_CPU', 'File_Name'])
                config_df = config_df.transpose()
                key_value_pairs = config_df.to_dict()[0]
                config_df = pd.DataFrame(list(key_value_pairs.items()), columns=['Parameter', 'Value'])
                config_df.fillna('', inplace=True)
                config_df.to_excel(writer, sheet_name="Configuration", index=False)

            shutil.copyfile(temp_compare_config_path, compare_config_file_path)
            project_connection = project_function_call(project_DB_details)
            insert_config_name(project_connection, compare_file_name, task_name, source_connection_id,
                               target_connection_id, request_cpu, limit_cpu, 'Created')
            copy_data_compare_dag_files(compare_file_name, config_files_path.replace(root_path + '/', ''), local_migration_path, dag_path)
        else:
            print(f'Module path {db_module_path} not found')
    else:
        print(f'Config path {config_path} not found')
