import re, os, sys, time, shutil, json
from import_file import import_file
import pandas as pd
from common_modules.api import api_authentication, decrypt_database_details
from common_modules.stored_procedures import insert_config_name


def copy_dag_files(file_name, config_files_path, local_migration_path,dag_path, root_path):
    source_file = local_migration_path + '/' + 'Dag_Files' + '/' + 'dag_file.py'
    target_file = dag_path + '/' + f'dag_file_{file_name}.py'

    with open(source_file, 'r') as file:
        file_content = file.read()
        file_replaced_content = re.sub(r'@Config_File_Path',
                                       rf'{config_files_path}/Data_Migration/{file_name}.xlsx'.replace('\\', '/'),
                                       file_content)
        file_replaced_content = re.sub(r'@Config_File_Folder',
                                       config_files_path.replace('\\', '/'),
                                       file_replaced_content)
    with open(target_file, 'w') as f:
        f.write(file_replaced_content)

    dag_triggers_file = local_migration_path + '/' + 'Dag_Files' + '/' + 'dag_triggers.py'
    trigger_destination_file = dag_path + '/' + 'dag_triggers.py'
    with open(dag_triggers_file, 'rb') as f:
        file_content = f.read()
    with open(trigger_destination_file, 'wb') as f:
        f.write(file_content)

    if not os.path.exists(root_path + '/Validation_Queries/'):
        os.makedirs(root_path + '/Validation_Queries/')

    xml_files = local_migration_path + '/' + 'validation_queries.xml'
    xml_destination_file = root_path + '/' + 'Validation_Queries' + '/' + 'validation_queries.xml'
    shutil.copyfile(xml_files, xml_destination_file)


def data_load_trigger(task_name, project_id, migration_name, source_connection_id, target_connection_id, schema_name,
                      target_schema, table_name, request_cpu, limit_cpu, data_load_type, cdc_load_type, file_name,
                      cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')
        dag_path = getattr(import_object, 'Dag_Path')

        working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')
        local_migration_path = local_root_path + '/' + 'Migrations' + '/' + migration_name

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)

            token_data = api_authentication()

            source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
            source_function_call = getattr(import_object, 'DB_connection')

            project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
            project_function_call = getattr(import_object, 'connect_database')

            execute_query_function_call = getattr(import_object, 'execute_query')

            if target_connection_id in ['', None]:
                config_files_path = root_path + '/' + str(source_connection_id) + '/' + 'Config_Files'
            else:
                config_files_path = root_path + '/' + str(source_connection_id) + '/' + str(
                    target_connection_id) + '/' + 'Config_Files'

            if not os.path.exists(config_files_path + '/' + 'Data_Migration'):
                os.makedirs(config_files_path + '/' + 'Data_Migration')

            if not os.path.isfile(config_files_path + '/' + 'chunk_configuration.json'):
                configuration_json_file = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'chunk_configuration.json'
                destination_json_file = config_files_path + '/' + 'chunk_configuration.json'
                shutil.copyfile(configuration_json_file, destination_json_file)

            if not os.path.exists(working_directory_path):
                os.makedirs(working_directory_path)

            if os.path.isfile(config_files_path + '/' + 'chunk_configuration.json'):
                with open(config_files_path + '/' + 'chunk_configuration.json', 'r') as f:
                    chunk_configuration = json.loads(f.read())
                    chunk_size = chunk_configuration['Chunk_Size']

            schema_list_query = """
            SELECT u.username FROM DBA_USERS u
            LEFT JOIN
                DBA_SEGMENTS s ON u.username = s.owner
            WHERE
                u.username NOT IN (
                    'SYSTEM', 'SYS', 'APPOQSYS', 'REMOTE_SCHEDULER_AGENT', 'DBSFWMUSER',
                    'CTXSYS', 'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS',
                    'DVSYS', 'GSMADMIN_INTERNAL', 'ORDPLUGINS', 'ORDDATA', 'MDSYS',
                    'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM', 'XDB', 'WMSYS',
                    'ORDSYS', 'DBSNMP', 'DVF', 'APEX_030200', 'EXFSYS', 'OWBSYS',
                    'ONBSYS_AUDIT', 'SYSMAN', 'SCOTT'
                )
                AND u.account_status = 'OPEN'
            GROUP BY u.username ORDER BY u.username
            """

            table_list_query = """
            select DISTINCT OBJECT_NAME from dba_objects a where NOT exists (select 1 from dba_mviews MV
            WHERE MV.MVIEW_NAME=a.object_name AND MV.OWNER=upper('@schemaname')) AND A.OWNER=upper('@schemaname') and
            OBJECT_NAME not like '%$%' and OBJECT_NAME not like 'SYS_%' AND A.OBJECT_TYPE='TABLE' 
            AND a.STATUS = 'VALID' AND TEMPORARY='N' ORDER BY 1
            """

            tables_list = []
            source_connection, error = source_function_call(source_DB_details)
            if table_name != '':
                tables_list = [(schema_name.upper(), i.upper()) for i in table_name.split(',') if i.strip()]
            elif schema_name != '' and table_name == '':
                schema_list = [i for i in schema_name.split(',') if i.strip()]
                if schema_list:
                    for schema in schema_list:
                        table_list_query = table_list_query.replace('@schemaname', schema.upper())
                        schema_table_list = execute_query_function_call(source_connection, table_list_query)
                        if schema_table_list:
                            tables_list.extend([(schema.upper(), i[0]) for i in schema_table_list])
            elif schema_name == '' and table_name == '':
                schema_list = execute_query_function_call(source_connection, schema_list_query)
                if schema_list:
                    for schema in schema_list:
                        table_list_query = table_list_query.replace('@schemaname', schema.upper())
                        schema_table_list = execute_query_function_call(source_connection, table_list_query)
                        if schema_table_list:
                            tables_list.extend([(schema.upper(), i[0]) for i in schema_table_list])
            print(tables_list)

            tag = ''
            if task_name == 'Initial_Data_Load':
                tag = 'init_'
            elif task_name == 'E2E_Data_Load':
                tag = 'e2e_'

            config_path = config_files_path + '/' + 'Data_Migration' + '/' + tag + str(
                file_name).lower().strip() + '.xlsx'
            temp_config_path = working_directory_path + '/' + tag + str(file_name).lower().strip() + '.xlsx'
            excel_file_name = tag + str(file_name).lower().strip()

            dag_tasks_list = []
            no_data_tables_list = []
            with pd.ExcelWriter(temp_config_path, engine="openpyxl") as writer:
                for table_tuple in tables_list:
                    datatypes_query = "SELECT DATA_TYPE FROM ALL_TAB_COLUMNS WHERE OWNER = '@schemaname' AND table_name = '@tablename' ORDER BY COLUMN_ID"

                    datatypes_query = datatypes_query.replace('@schemaname', table_tuple[0]).replace('@tablename',
                                                                                                     table_tuple[1])
                    datatypes_list = execute_query_function_call(source_connection, datatypes_query)

                    chunks_query = """
                        SELECT CEIL(NVL(SUM(bytes)/1024/1024, 0) / @chunk_size)
                        FROM (
                            SELECT bytes FROM dba_segments
                            WHERE (owner, SEGMENT_NAME) IN (
                                SELECT OWNER, SEGMENT_NAME FROM dba_lobs 
                                WHERE owner = '@schemaname' AND TABLE_NAME = '@tablename'
                            )
                            UNION ALL
                            SELECT bytes FROM dba_segments 
                            WHERE owner = '@schemaname' AND SEGMENT_NAME = '@tablename'
                        )
                    """

                    chunks_query = chunks_query.replace('@schemaname', table_tuple[0]).replace('@tablename',
                                                                                               table_tuple[1]).replace(
                        '@chunk_size', str(chunk_size))
                    chunk_number = execute_query_function_call(source_connection, chunks_query)[0][0]

                    row_id_query = """
                        SELECT DBMS_ROWID.ROWID_CREATE(1, oid1, fid1, bid1, 0) AS rowid1,
                               DBMS_ROWID.ROWID_CREATE(1, oid2, fid2, bid2, 9999) AS rowid2,
                               ROUND((chunk_size_in_blocks * 8192) / (1024 * 1024), 2) AS chunk_size
                        FROM (
                            SELECT a.*, ROWNUM rn FROM (
                                SELECT chunk_no,
                                       MIN(oid1) AS oid1,
                                       MAX(oid2) AS oid2,
                                       MIN(fid1) AS fid1,
                                       MAX(fid2) AS fid2,
                                       MIN(bid1) AS bid1,
                                       MAX(bid2) AS bid2,
                                       SUM(blocks) AS chunk_size_in_blocks
                                FROM (
                                    SELECT chunk_no,
                                           FIRST_VALUE(data_object_id) OVER(PARTITION BY chunk_no ORDER BY data_object_id, relative_fno, block_id ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS oid1,
                                           LAST_VALUE(data_object_id) OVER(PARTITION BY chunk_no ORDER BY data_object_id, relative_fno, block_id ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS oid2,
                                           FIRST_VALUE(relative_fno) OVER(PARTITION BY chunk_no ORDER BY data_object_id, relative_fno, block_id ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS fid1,
                                           LAST_VALUE(relative_fno) OVER(PARTITION BY chunk_no ORDER BY data_object_id, relative_fno, block_id ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS fid2,
                                           FIRST_VALUE(block_id) OVER(PARTITION BY chunk_no ORDER BY data_object_id, relative_fno, block_id ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS bid1,
                                           LAST_VALUE(block_id + blocks - 1) OVER(PARTITION BY chunk_no ORDER BY data_object_id, relative_fno, block_id ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS bid2,
                                           blocks
                                    FROM (
                                        SELECT data_object_id, relative_fno, block_id, blocks,
                                               CEIL(SUM2 / chunk_size) AS chunk_no
                                        FROM (
                                            SELECT b.data_object_id, a.relative_fno, a.block_id, a.blocks,
                                                   SUM(a.blocks) OVER(ORDER BY b.data_object_id, a.relative_fno, a.block_id) AS SUM2,
                                                   CEIL(SUM(a.blocks) OVER() / @chunk_number) AS chunk_size
                                            FROM dba_extents a
                                            JOIN dba_objects b ON a.owner = b.owner
                                            AND a.segment_name = b.object_name
                                            AND NVL(a.partition_name, '-1') = NVL(b.subobject_name, '-1')
                                            WHERE b.data_object_id IS NOT NULL
                                            AND a.owner = '@schemaname'
                                            AND a.segment_name = '@tablename'
                                        )
                                    )
                                )
                                GROUP BY chunk_no
                                ORDER BY chunk_no
                            ) a
                        )
                    """

                    row_id_query = row_id_query.replace('@schemaname', table_tuple[0]).replace('@tablename',
                                                                                               table_tuple[1]).replace(
                        '@chunk_number', str(chunk_number))
                    row_id_list = execute_query_function_call(source_connection, row_id_query)

                    if any(element in datatypes_list for element in ['CLOB', 'BLOB', 'XMLTYPE', 'NCLOB', 'BFILE']):
                        table_category = 'Lob'
                    else:
                        table_category = 'Non_Lob'
                    dag_name = f'{table_tuple[0]}_{table_tuple[1]}_{file_name.upper()}'

                    count = 1
                    if row_id_list:
                        for row_id_tuple in row_id_list:
                            dag_tuple = (
                                table_tuple[0], table_tuple[1], table_category, dag_name, f'Part_{count}',
                                row_id_tuple[2], row_id_tuple[0],row_id_tuple[1])
                            dag_tasks_list.append(dag_tuple)
                            count = count + 1
                    else:
                        no_data_tables_list.append(table_tuple)


                if dag_tasks_list:
                    if task_name in ['E2E_Data_Load']:
                        tables_str = ','.join([f'{table_tuple[0]}.{table_tuple[1]}' for table_tuple in tables_list])
                        cdc_dag_tuple = (
                            schema_name, tables_str, 'CDC', 'CDC_' + file_name.replace('.xlsx', ''),
                            'CDC_' + file_name.replace('.xlsx', ''),
                            None, None, None)
                        dag_tasks_list.append(cdc_dag_tuple)
                        if cdc_load_type == 'File':
                            load_files_dag_tuple = (
                                schema_name,tables_str, 'Load_Files',
                                'Load_Files_' + file_name.replace('.xlsx', ''),
                                'Load_Files_' + file_name.replace('.xlsx', ''),
                                None, None, None)
                            dag_tasks_list.append(load_files_dag_tuple)

                    tasks_df = pd.DataFrame(dag_tasks_list,
                                            columns=['Schema_Name', 'Table_Name', 'Table_Category', 'Dag Name',
                                                     'Part_Name', 'Chunk_Size', 'Lower_Bound', 'Upper_Bound'])
                    tasks_df.fillna('', inplace=True)
                    tasks_df.to_excel(writer, sheet_name='Dags', index=False)
                    dag_names_list = tasks_df['Dag Name'].drop_duplicates().to_list()

                config_list = [
                    (task_name, int(project_id), migration_name, schema_name, target_schema, table_name,
                     str(source_connection_id), str(target_connection_id), data_load_type, cdc_load_type, request_cpu,
                     limit_cpu, excel_file_name)]
                config_df = pd.DataFrame(config_list,
                                         columns=['Process_Type', 'Project_Id', 'Migration_Name', 'Schema',
                                                  'Target_Schema', 'Table_Name', 'Source_Connection_Id',
                                                  'Target_Connection_Id', 'Data_Load_Type', 'CDC_Load_Type',
                                                  'Request_CPU', 'Limit_CPU', 'File_Name'])
                config_df = config_df.transpose()
                key_value_pairs = config_df.to_dict()[0]
                config_df = pd.DataFrame(list(key_value_pairs.items()), columns=['Parameter', 'Value'])
                config_df.fillna('', inplace=True)
                config_df.to_excel(writer, sheet_name="Configuration", index=False)

                if no_data_tables_list:
                    no_data_table_df = pd.DataFrame(no_data_tables_list,
                                                    columns=['Schema_Name', 'Table_Name'])
                    no_data_table_df.to_excel(writer, sheet_name='No_Data_Tables', index=False)
                print(dag_names_list)
                if dag_names_list:
                    priority_list = []
                    for dag_name in dag_names_list:
                        epoch_time = time.time()
                        priority_tuple = (dag_name, int(epoch_time))
                        priority_list.append(priority_tuple)
                        time.sleep(1)
                    priority_df = pd.DataFrame(priority_list, columns=['Dag Name', 'Priority_Weight'])
                    priority_df.to_excel(writer, sheet_name="Priority_Weights", index=False)

            shutil.copyfile(temp_config_path, config_path)

            project_connection = project_function_call(project_DB_details)
            insert_config_name(project_connection, excel_file_name, task_name, source_connection_id,
                               target_connection_id, request_cpu, limit_cpu, 'Created')
            copy_dag_files(excel_file_name, config_files_path.replace(root_path + '/', ''), local_migration_path,dag_path, root_path)
        else:
            print(f'Module path {db_module_path} not found')
    else:
        print(f'Config path {config_path} not found')
