import os, re, requests, json, psycopg2, warnings
import pandas as pd
import xml.etree.ElementTree as ET

warnings.filterwarnings('ignore')

connection_url = os.getenv("API_HOST", 'http://qmig-eng.qmig-ns.svc.cluster.local:8080')
connection_url = connection_url + '/api/v1/'


def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection



def DB_connection(database_data):
    host_name = database_data['host']
    port = database_data['port']
    db_name = database_data['db_name']
    user_name = database_data['name']
    password = database_data['password']
    try:
        connection = psycopg2.connect(user=user_name, password=password,
                                      host=host_name, database=db_name,
                                      port=port)
        error = ''
    except psycopg2.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near database connection", e)
    return connection,error


def target_DB_connection(database_data):
    host_name = database_data['host']
    port = database_data['port']
    db_name = database_data['db_name']
    user_name = database_data['name']
    password = database_data['password']
    try:
        connection = psycopg2.connect(user=user_name, password=password,
                                      host=host_name, database=db_name,
                                      port=port)
        error = ''
    except psycopg2.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near database connection", e)
    return connection,error


def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        data = cursor.fetchall()
    except psycopg2.DatabaseError as e:
        print("Issue found near Target database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data


def api_authentication():
    url = connection_url + 'Common/Security/VerifyPython'
    project_id = os.environ['PROJECT_ID']
    params = {
        "projectId": str(project_id),
        "content": "8/2DlAH8q1R+untey0ZuqMuD89WX4m2XoSCWhxQ+UOk="}
    response = requests.get(url, params=params)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = json.loads(status_response['text'])
        token_data = data['token']
        return token_data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def get_project_DB_info(token_data, project_id):
    url = connection_url + 'Common/Master/GetProjectMigDetailSelect'
    payload = {
        "projectId": str(project_id),
        "migsrcType": "D"
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.post(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = data_text['jsonResponseData']
        data_text = [dict for dict in data_text['Table1'] if dict['dbconname'] == 'PRJDB' + str(project_id)][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def get_source_DB_info(token_data, project_id, source_connection_id):
    url = connection_url + 'Common/Master/GetDBConnections'
    payload = {
        "projectId": str(project_id),
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.get(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = [dict for dict in data_text['Table1'] if
                     dict['migsrctgt'] == 'S' and dict['Connection_ID'] == source_connection_id][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def get_target_DB_info(token_data, project_id, target_connection_id):
    url = connection_url + 'Common/Master/GetDBConnections'
    payload = {
        "projectId": str(project_id),
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.get(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = [dict for dict in data_text['Table1'] if
                     dict['migsrctgt'] == 'T' and dict['Connection_ID'] == target_connection_id][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def decrypt_string(encrypted_str, token_data):
    application_url = connection_url + 'Common/Cryptography/DecryptData'
    payload = {
        "NeedToDecrypt": encrypted_str
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response = requests.post(application_url, data=json.dumps(payload), headers=headers)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = response.text
        return data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def decrypt_database_details(token_data, project_id, category, connection_id):
    prj_data = {}
    prj_db_data = {}
    if category == 'Project':
        prj_db_data = get_project_DB_info(token_data, project_id)
    elif category == 'Source':
        prj_db_data = get_source_DB_info(token_data, project_id, connection_id)
    elif category == 'Target':
        prj_db_data = get_target_DB_info(token_data, project_id, connection_id)
    password = decrypt_string(prj_db_data['dbpassword'], token_data)
    userid = decrypt_string(prj_db_data['dbuserid'], token_data)
    userid = re.split('\s+', re.sub(r"[\x00-\x1F\x7F]", ' ', userid))
    userid = ' '.join(userid).strip()
    password = re.split('\s+', re.sub(r"[\x00-\x1F\x7F]", ' ', password))
    password = ' '.join(password).strip()
    port = prj_db_data['dbport']
    host = prj_db_data['dbhost']
    if category == 'Project':
        service_name = ''
        parallelprocess = ''
    else:
        service_name = prj_db_data['service_name']
        parallelprocess = prj_db_data['parallelprocess']
    dbname = prj_db_data['dbname']
    prj_data['password'] = password
    prj_data['name'] = userid
    prj_data['port'] = port
    prj_data['host'] = host
    prj_data['db_name'] = dbname
    prj_data['service_name'] = service_name
    prj_data['parallelprocess'] = parallelprocess
    return prj_data


def pre_validation_trigger(**kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    file_name = kwargs['file_name']
    token_data = kwargs['token_data']

    source_name = 'Postgres'
    target_name = 'Postgres'

    extra_path = os.environ['EXTRA_FOLDER']
    root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

    validation_reports_path = root_folder + '/' + 'Validation_Reports' + '/' + file_name.lower()
    if not os.path.exists(validation_reports_path):
        os.makedirs(validation_reports_path)

    xml_path = extra_path + '/' + 'Validation_Queries' + '/' + 'validation_queries.xml'
    tree = ET.parse(xml_path)
    root = tree.getroot()

    objects_list = ['Table', 'Datatype']

    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
    source_connection, error = DB_connection(source_DB_details)

    target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
    target_connection, error = target_DB_connection(target_DB_details)

    file_path = f'{validation_reports_path}/{schema_name}_pre_validation_report.xlsx'
    with pd.ExcelWriter(file_path) as writer:
        for object_type in objects_list:
            source_query_tag = 'Validation_Queries/Source/' + '/' + object_type
            object_source_query = list(root.iterfind(source_query_tag))[0].text
            object_source_query = object_source_query.replace('@schemaname', schema_name.upper()).replace('@order',
                                                                                                          '').replace(
                '@degree', str(source_DB_details['parallelprocess']))
            source_object_output = execute_query(source_connection, object_source_query)

            target_query_tag = 'Validation_Queries/Target/' + '/' + object_type
            object_target_query = list(root.iterfind(target_query_tag))[0].text
            object_target_query = object_target_query.replace('@schemaname', target_schema.upper()).replace('@order',
                                                                                                            '').replace(
                '@degree', str(target_DB_details['parallelprocess']))
            target_object_output = execute_query(target_connection, object_target_query)

            if object_type == "Table":
                source_data_df = pd.DataFrame(source_object_output,
                                              columns=['Source_Schema_Name', 'Table_Name', 'Table_Type'])
                target_data_df = pd.DataFrame(target_object_output,
                                              columns=['Target_Schema_Name', 'Table_Name', 'Table_Type'])
                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                source_data_df['Table_Type'] = source_data_df['Table_Type'].replace({'base table': 'table'})
                target_data_df['Table_Type'] = target_data_df['Table_Type'].replace({'base table': 'table'})

                matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Table_Type'],
                                        suffixes=('_Source', '_Target'), how='inner')
                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                source_data = source_data_df[
                    ~source_data_df['Table_Name'].isin(matched_data['Table_Name'])]
                target_data = target_data_df[
                    ~target_data_df['Table_Name'].isin(matched_data['Table_Name'])]

                source_only_data = pd.merge(source_data, target_data,
                                            on=['Table_Name', 'Table_Type'], suffixes=('_Source', '_Target'),
                                            how='left', indicator=True)
                source_only_data = source_only_data[source_only_data['_merge'] == 'left_only'].drop(columns=['_merge'])
                source_only_data['Status'] = f"Available in {source_name} not in {target_name}"

                target_only_data = pd.merge(source_data, target_data,
                                            on=['Table_Name', 'Table_Type'], suffixes=('_Source', '_Target'),
                                            how='right', indicator=True)
                target_only_data = target_only_data[target_only_data['_merge'] == 'right_only'].drop(columns=['_merge'])
                target_only_data['Status'] = f"Available in {target_name} not in {source_name}"

                result_df = pd.concat([matched_data, source_only_data, target_only_data], ignore_index=True)
                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                matched_tables_list = matched_data['Table_Name'].values.tolist()
                table_counts_list = []
                for table_name in matched_tables_list:
                    source_column_count_query = f"select count(*) from information_schema.columns where upper(table_schema) = '{schema_name.upper()}' and UPPER(table_name) = '{table_name.upper()}'"
                    target_column_count_query = f"select count(*) from information_schema.columns where upper(table_schema) = '{target_schema.upper()}' and UPPER(table_name) = '{table_name.upper()}'"

                    source_column_data = execute_query(source_connection, source_column_count_query)
                    target_column_data = execute_query(target_connection, target_column_count_query)
                    difference_count = abs(len(source_column_data) - len(target_column_data))

                    created_tuple = (
                        schema_name, target_schema, table_name, len(source_column_data), len(target_column_data),
                        difference_count)
                    table_counts_list.append(created_tuple)

                table_count_df = pd.DataFrame(table_counts_list,
                                              columns=['Source_Schema', 'Target_Schema', 'Table_Name',
                                                       'Source_Column_Count', 'Target_Column_Count',
                                                       'Difference_Count'])
                table_count_df.to_excel(writer, sheet_name=f"{object_type}_Columns_Counts", index=False)

                summary_table_count_list = []
                source_table_count = len(result_df[(result_df['Table_Type'] == 'table') &
                                                   ((result_df[
                                                         'Status'] == f"Available in both {source_name} and {target_name}") | (
                                                            result_df[
                                                                'Status'] == f"Available in {source_name} not in {target_name}"))])

                target_table_count = len(result_df[(result_df['Table_Type'] == 'table') &
                                                   ((result_df[
                                                         'Status'] == f"Available in both {source_name} and {target_name}") | (
                                                            result_df[
                                                                'Status'] == f"Available in {target_name} not in {source_name}"))])
                summary_table_count_list.append(
                    (schema_name, target_schema, 'Table', source_table_count, target_table_count))

                source_temp_table_count = len(result_df[(result_df['Table_Type'] == 'temporary table') &
                                                        ((result_df[
                                                              'Status'] == f"Available in both {source_name} and {target_name}") | (
                                                                 result_df[
                                                                     'Status'] == f"Available in {source_name} not in {target_name}"))])
                target_temp_table_count = len(result_df[(result_df['Table_Type'] == 'temporary table') &
                                                        ((result_df[
                                                              'Status'] == f"Available in both {source_name} and {target_name}") | (
                                                                 result_df[
                                                                     'Status'] == f"Available in {target_name} not in {source_name}"))])
                summary_table_count_list.append(
                    (schema_name, target_schema, 'Temporary Table', source_temp_table_count, target_temp_table_count))

                source_mview_count = len(result_df[(result_df['Table_Type'] == 'materialized view') &
                                                   ((result_df[
                                                         'Status'] == f"Available in both {source_name} and {target_name}") | (
                                                            result_df[
                                                                'Status'] == f"Available in {source_name} not in {target_name}"))])
                target_mview_count = len(result_df[(result_df['Table_Type'] == 'materialized view') &
                                                   ((result_df[
                                                         'Status'] == f"Available in both {source_name} and {target_name}") | (
                                                            result_df[
                                                                'Status'] == f"Available in {target_name} not in {source_name}"))])
                summary_table_count_list.append(
                    (schema_name, target_schema, 'Materialized View', source_mview_count, target_mview_count))

                objects_match_df = pd.DataFrame(summary_table_count_list,
                                                columns=['Schema_Name', 'Target_Schema', 'Object_Type',
                                                         'Source_Object_Count',
                                                         'Target_Object_Count'])
                objects_match_df.to_excel(writer, sheet_name=f"Objects_Summary", index=False)

            elif object_type == 'Partition':
                source_data_df = pd.DataFrame(source_object_output,
                                              columns=['Schema_name', 'Table_Name', 'Partition_Name'])
                target_data_df = pd.DataFrame(target_object_output,
                                              columns=['Schema_name', 'Table_Name', 'Partition_Name'])

                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                if not source_data_df.empty or not target_data_df.empty:
                    matched_data = pd.merge(source_data_df, target_data_df,
                                            on=['Table_Name', 'Partition_Name'],
                                            suffixes=('_Source', '_Target'), how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                    source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Partition_Name'],
                                           suffixes=('_Source', '_Target'), how='left', indicator=True)
                    source_data = source_data[source_data['_merge'] == 'left_only']
                    source_data['Status'] = f"Available in {source_name} not in {target_name}"

                    target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Partition_Name'],
                                           suffixes=('_Source', '_Target'), how='right', indicator=True)
                    target_data = target_data[target_data['_merge'] == 'right_only']
                    target_data['Status'] = f"Available in {target_name} not in {source_name}"

                    result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                    result_df = result_df.drop(columns=['_merge'], errors='ignore')
                    result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

            if object_type == 'Datatype':
                source_data_df = pd.DataFrame(source_object_output,
                                              columns=['Source_Schema', 'Table_Name', 'Column_Name',
                                                       'Source_Datatype',
                                                       'Source_Datatype_Length', 'Source_Index',
                                                       'Table_Type'])
                target_data_df = pd.DataFrame(target_object_output,
                                              columns=['Target_Schema', 'Table_Name', 'Column_Name',
                                                       'Target_Datatype',
                                                       'Target_Datatype_Length', 'Target_Index',
                                                       'Table_Type'])

                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                source_data_df['Source_Modified_Datatype'] = source_data_df['Source_Datatype']
                target_data_df['Target_Modified_Datatype'] = target_data_df['Target_Datatype']
                target_data_df['Target_Datatype_Length'] = target_data_df['Target_Datatype_Length'].str.replace('(',
                                                                                                                '',
                                                                                                                regex=False).str.replace(
                    ')', '', regex=False)

                merged_df = pd.merge(source_data_df, target_data_df,
                                     on=['Table_Name', 'Column_Name', 'Table_Type'], how='inner')

                result_match = []
                for index, row in merged_df.iterrows():
                    if row['Source_Modified_Datatype'] == row['Target_Modified_Datatype'] and row[
                        'Source_Index'] == \
                            row[
                                'Target_Index'] and row['Source_Datatype_Length'] == row[
                        'Target_Datatype_Length']:
                        status = 'Matched'
                    else:
                        if (row['Source_Modified_Datatype'] == row['Target_Modified_Datatype']) and (
                                row['Source_Index'] == row['Target_Index']) and (
                                row['Source_Datatype_Length'] in ['', None, 'None', 'none'] or row[
                            'Target_Datatype_Length'] in ['', None, 'None', 'none']):
                            status = 'Matched'
                        else:
                            status = 'Not_Matched'

                    result_match.append(
                        (row['Source_Schema'], row['Target_Schema'], row['Table_Name'], row['Column_Name'],
                         row['Source_Datatype'],
                         row['Source_Datatype_Length'], row['Target_Datatype'],
                         row['Target_Datatype_Length'], row['Source_Index'], row['Target_Index'],
                         row['Table_Type'], status))

                result_match_df = pd.DataFrame(result_match,
                                               columns=['Schema_Name', 'Target_Schema', 'Table_Name', 'Column_Name',
                                                        'Source_Datatype',
                                                        'Source_Datatype_Length', 'Target_Datatype',
                                                        'Target_Datatype_Length',
                                                        'Source_Index', 'Target_Index', 'Table_Type', 'Status'])
                result_match_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                summary_datatype_count_list = []
                table_match_count = len(result_match_df[
                                            (result_match_df['Table_Type'] == 'table') & (
                                                    result_match_df['Status'] == 'Matched')])
                table_unmatch_count = len(result_match_df[
                                              (result_match_df['Table_Type'] == 'table') & (
                                                      result_match_df['Status'] == 'Unmatched')])
                summary_datatype_count_list.append(
                    (schema_name, target_schema, 'Table', table_match_count, table_unmatch_count))

                temp_table_match_count = len(result_match_df[
                                                 (result_match_df['Table_Type'] == 'temporary table') & (
                                                         result_match_df['Status'] == 'Matched')])
                temp_table_unmatch_count = len(result_match_df[
                                                   (result_match_df['Table_Type'] == 'temporary table') & (
                                                           result_match_df['Status'] == 'Unmatched')])
                summary_datatype_count_list.append(
                    (schema_name, target_schema, 'Temporary Table', temp_table_match_count, temp_table_unmatch_count))

                mview_match_count = len(result_match_df[
                                            (result_match_df['Table_Type'] == 'materialized view') & (
                                                    result_match_df['Status'] == 'Matched')])
                mview_unmatch_count = len(result_match_df[
                                              (result_match_df['Table_Type'] == 'materialized view') & (
                                                      result_match_df['Status'] == 'Unmatched')])
                summary_datatype_count_list.append(
                    (schema_name, target_schema, 'Materialized View', mview_match_count, mview_unmatch_count))

                datatype_match_df = pd.DataFrame(summary_datatype_count_list,
                                                 columns=['Schema_Name', 'Target_Schema', 'Object_Type',
                                                          'Datatype_Matched_Count',
                                                          'Datatype_Unmatched_Count'])
                datatype_match_df.to_excel(writer, sheet_name=f"{object_type}_Summary", index=False)


def post_validation_trigger(**kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    file_name = kwargs['file_name']
    token_data = kwargs['token_data']

    extra_path = os.environ['EXTRA_FOLDER']
    root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

    validation_reports_path = root_folder + '/' + 'Validation_Reports' + '/' + file_name.lower()
    if not os.path.exists(validation_reports_path):
        os.makedirs(validation_reports_path)

    xml_path = extra_path + '/' + 'Validation_Queries' + '/' + 'validation_queries.xml'
    tree = ET.parse(xml_path)
    root = tree.getroot()

    objects_list = ['Table']

    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
    source_connection, error = DB_connection(source_DB_details)

    target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
    target_connection, error = target_DB_connection(target_DB_details)

    file_path = f'{validation_reports_path}/{schema_name}_post_validation_report.xlsx'
    with pd.ExcelWriter(file_path) as writer:
        for object_type in objects_list:
            source_query_tag = 'Validation_Queries/Source/' + '/' + object_type
            object_source_query = list(root.iterfind(source_query_tag))[0].text
            object_source_query = object_source_query.replace('@schemaname', schema_name.upper()).replace('@order',
                                                                                                          '').replace(
                '@degree', str(source_DB_details['parallelprocess']))
            source_object_output = execute_query(source_connection, object_source_query)

            target_query_tag = 'Validation_Queries/Target/' + '/' + object_type
            object_target_query = list(root.iterfind(target_query_tag))[0].text
            object_target_query = object_target_query.replace('@schemaname', target_schema.upper()).replace('@order',
                                                                                                            '').replace(
                '@degree', str(target_DB_details['parallelprocess']))
            target_object_output = execute_query(target_connection, object_target_query)

            source_data_df = pd.DataFrame(source_object_output,
                                          columns=['Schema_name', 'Table_Name', 'Table_Type'])
            target_data_df = pd.DataFrame(target_object_output,
                                          columns=['Schema_name', 'Table_Name', 'Table_Type'])
            source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
            target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
            source_data_df['Table_Type'] = source_data_df['Table_Type'].replace({'base table': 'table'})
            target_data_df['Table_Type'] = target_data_df['Table_Type'].replace({'base table': 'table'})

            matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Table_Type'],
                                    suffixes=('_Source', '_Target'), how='inner')

            matched_tables_list = matched_data['Table_Name'].values.tolist()
            table_counts_list = []
            for table_name in matched_tables_list:
                source_row_count_query = f"select count(*) from {schema_name.upper()}.{table_name.upper()}"
                target_row_count_query = f"select count(*) from {target_schema.upper()}.{table_name.upper()}"

                source_row_count = execute_query(source_connection, source_row_count_query)[0][0]
                target_row_count = execute_query(target_connection, target_row_count_query)[0][0]
                difference_count = abs(source_row_count - target_row_count)

                created_tuple = (
                    schema_name, target_schema, table_name, source_row_count, target_row_count, difference_count)
                table_counts_list.append(created_tuple)

            table_count_df = pd.DataFrame(table_counts_list,
                                          columns=['Source_Schema', 'Target_Schema', 'Table_Name',
                                                   'Source_Row_Count', 'Target_Row_Count',
                                                   'Difference_Count'])
            table_count_df.to_excel(writer, sheet_name=f"{object_type}_Row_Counts", index=False)


def get_summary_objects_counts(result_df, object_name, schema_name, target_schema):
    source_name = 'Postgres'
    target_name = 'Postgres'

    summary_table_count_list = []
    if object_name != 'Datatype':
        source_table_count = len(result_df[(result_df[
                                                'Status'] == f"Available in both {source_name} and {target_name}") | (
                                                   result_df[
                                                       'Status'] == f"Available in {source_name} not in {target_name}")])
        target_table_count = len(result_df[(result_df[
                                                'Status'] == f"Available in both {source_name} and {target_name}") | (
                                                   result_df[
                                                       'Status'] == f"Available in {target_name} not in {source_name}")])
        difference_count = abs(source_table_count - target_table_count)
        summary_table_count_list.append(
            (schema_name, target_schema, object_name, source_table_count, target_table_count, difference_count))
    return summary_table_count_list


def complete_validation_trigger(**kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    file_name = kwargs['file_name']
    token_data = kwargs['token_data']

    source_name = 'Postgres'
    target_name = 'Postgres'

    extra_path = os.environ['EXTRA_FOLDER']
    root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

    validation_reports_path = root_folder + '/' + 'Validation_Reports' + '/' + file_name.lower()
    if not os.path.exists(validation_reports_path):
        os.makedirs(validation_reports_path)

    xml_path = extra_path + '/' + 'Validation_Queries' + '/' + 'validation_queries.xml'
    tree = ET.parse(xml_path)
    root = tree.getroot()

    objects_list = ['Sequence', 'Primary_Key', 'Unique_Constraint', 'Foreign_Key', 'Not_Null_Constraint',
                    'Default_Constraint', 'Check_Constraint', 'Index']

    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
    source_connection, error = DB_connection(source_DB_details)

    target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
    target_connection, error = target_DB_connection(target_DB_details)

    file_path = f'{validation_reports_path}/{schema_name}_complete_validation_report.xlsx'
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        final_summary_objects_list = []
        for object_type in objects_list:
            source_query_tag = 'Validation_Queries/Source/' + '/' + object_type
            object_source_query = list(root.iterfind(source_query_tag))[0].text
            object_source_query = object_source_query.replace('@schemaname', schema_name.upper()).replace('@order',
                                                                                                          '').replace(
                '@degree', str(source_DB_details['parallelprocess']))
            source_object_output = execute_query(source_connection, object_source_query)

            target_query_tag = 'Validation_Queries/Target/' + '/' + object_type
            object_target_query = list(root.iterfind(target_query_tag))[0].text
            object_target_query = object_target_query.replace('@schemaname', target_schema.upper()).replace('@order',
                                                                                                            '').replace(
                '@degree', str(target_DB_details['parallelprocess']))
            target_object_output = execute_query(target_connection, object_target_query)

            if (object_type == 'Primary_Key') or (object_type == 'Foreign_Key') or (
                    object_type == 'Check_Constraint') or (object_type == 'Unique_Constraint'):
                source_data_df = pd.DataFrame(source_object_output,
                                              columns=['Schema_Name', 'Table_Name', object_type,
                                                       'Source_Constraint_Name',
                                                       'Source_' + object_type + '_Status'])
                target_data_df = pd.DataFrame(target_object_output,
                                              columns=['Schema_Name', 'Table_Name', object_type,
                                                       'Target_Constraint_Name',
                                                       'Target_' + object_type + '_Status'])

                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                        suffixes=('_Source', '_Target'),
                                        how='inner')
                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                       suffixes=('_Source', '_Target'),
                                       how='left', indicator=True)
                source_data = source_data[source_data['_merge'] == 'left_only'].drop(
                    columns=['Target_Constraint_Name', 'Target_' + object_type + '_Status', '_merge'])
                source_data['Status'] = f"Available in {source_name} not in {target_name}"
                source_data['Target_Constraint_Name'] = 'Nan'
                source_data['Target_' + object_type + '_Status'] = 'Nan'

                target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                       suffixes=('_Source', '_Target'),
                                       how='right', indicator=True)
                target_data = target_data[target_data['_merge'] == 'right_only'].drop(
                    columns=['Source_Constraint_Name', 'Source_' + object_type + '_Status', '_merge'])
                target_data['Status'] = f"Available in {target_name} not in {source_name}"
                target_data['Source_Constraint_Name'] = 'Nan'
                target_data['Source_' + object_type + '_Status'] = 'Nan'
                result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                result_df.rename(columns={'_merge': 'Status'}, inplace=True, errors='ignore')
                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

            elif object_type == "Default_Constraint":
                source_data_df = pd.DataFrame(source_object_output,
                                              columns=['Schema_Name', 'Table_Name', 'Constraint_Name',
                                                       'Source_Column_Name'])
                target_data_df = pd.DataFrame(target_object_output,
                                              columns=['Schema_Name', 'Table_Name', 'Constraint_Name',
                                                       'Target_Column_Name'])
                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Constraint_Name'],
                                        suffixes=('_Source', '_Target'), how='inner')
                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Constraint_Name'],
                                       suffixes=('_Source', '_Target'), how='left', indicator=True)
                source_data = source_data[source_data['_merge'] == 'left_only']
                source_data['Status'] = f"Available in {source_name} not in {target_name}"
                source_data['Target_Column_Name'] = 'Nan'

                target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Constraint_Name'],
                                       suffixes=('_Source', '_Target'), how='right', indicator=True)
                target_data = target_data[target_data['_merge'] == 'right_only']
                target_data['Status'] = f"Available in {target_name} not in {source_name}"
                target_data['Source_Column_Name'] = 'Nan'

                result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                result_df = result_df.drop(columns=['_merge'], errors='ignore')
                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

            elif object_type == "Not_Null_Constraint":

                source_data_df = pd.DataFrame(source_object_output,
                                              columns=['Schema_name', 'Constraint_Name', 'Constraint_Value'])
                target_data_df = pd.DataFrame(target_object_output,
                                              columns=['Schema_name', 'Constraint_Name', 'Constraint_Value'])
                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                matched_data = pd.merge(source_data_df, target_data_df, on=['Constraint_Name', 'Constraint_Value'],
                                        suffixes=('_Source', '_Target'), how='inner')
                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                source_data = pd.merge(source_data_df, target_data_df, on=['Constraint_Name', 'Constraint_Value'],
                                       suffixes=('_Source', '_Target'), how='left', indicator=True)
                source_data = source_data[source_data['_merge'] == 'left_only'].drop(columns=['Schema_name_Target',
                                                                                              '_merge'])
                source_data['Status'] = f"Available in {source_name} not in {target_name}"

                target_data = pd.merge(source_data_df, target_data_df, on=['Constraint_Name', 'Constraint_Value'],
                                       suffixes=('_Source', '_Target'), how='right', indicator=True)
                target_data = target_data[target_data['_merge'] == 'right_only'].drop(
                    columns=['Schema_name_Source',
                             '_merge'])
                target_data['Status'] = f"Available in {target_name} not in {source_name}"
                result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

            elif object_type == 'View':
                source_data_df = pd.DataFrame(source_object_output,
                                              columns=['Schema_Name', 'Table_Name'])
                target_data_df = pd.DataFrame(target_object_output,
                                              columns=['Schema_Name', 'Table_Name'])
                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name'],
                                        suffixes=('_Source', '_Target'),
                                        how='inner')
                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name'],
                                       suffixes=('_Source', '_Target'),
                                       how='left', indicator=True)
                source_data = source_data[source_data['_merge'] == 'left_only'].drop(columns=['_merge'])
                source_data['Status'] = f"Available in {source_name} not in {target_name}"
                source_data['Schema_Name_Target'] = 'Nan'

                target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name'],
                                       suffixes=('_Source', '_Target'), how='right', indicator=True)
                target_data = target_data[target_data['_merge'] == 'right_only'].drop(columns=['_merge'])
                target_data['Status'] = f"Available in {target_name} not in {source_name}"
                target_data['Schema_Name_Source'] = 'Nan'

                result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                result_df = result_df.drop(columns=['_merge'], errors='ignore')
                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

            elif object_type == "Sequence":
                source_data_df = pd.DataFrame(source_object_output,
                                              columns=['Source_Schema_Name', 'Sequence_Name',
                                                       'Source_Sequence_Value'])
                target_data_df = pd.DataFrame(target_object_output,
                                              columns=['Target_Schema_Name', 'Sequence_Name',
                                                       'Target_Sequence_Value'])
                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                matched_data = pd.merge(source_data_df, target_data_df, on='Sequence_Name',
                                        suffixes=('_Source', '_Target'), how='inner')
                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                source_data = pd.merge(source_data_df, target_data_df, on='Sequence_Name',
                                       suffixes=('_Source', '_Target'), how='left', indicator=True)
                source_data = source_data[source_data['_merge'] == 'left_only']
                source_data['Status'] = f"Available in {source_name} not in {target_name}"

                target_data = pd.merge(source_data_df, target_data_df, on='Sequence_Name',
                                       suffixes=('_Source', '_Target'), how='right', indicator=True)
                target_data = target_data[target_data['_merge'] == 'right_only']
                target_data['Status'] = f"Available in {target_name} not in {source_name}"

                result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

            elif object_type == "Index":
                source_data_df = pd.DataFrame(source_object_output)
                if len(source_data_df):
                    source_data_df = source_data_df.loc[:, [0, 1, 2, 3, 6]]
                    source_data_df.columns = ['Schema_Name', 'Table_Name', 'Index_Name', 'Source_Index_Column',
                                              'Source_Index_DDL']
                else:
                    source_data_df = pd.DataFrame(
                        columns=['Schema_Name', 'Table_Name', 'Index_Name', 'Source_Index_Column',
                                 'Source_Index_DDL'])

                target_data_df = pd.DataFrame(target_object_output)
                if len(target_data_df):
                    target_data_df = target_data_df.loc[:, [0, 1, 2, 3, 6]]
                    target_data_df.columns = ['Schema_Name', 'Table_Name', 'Index_Name', 'Target_Index_Column',
                                              'Target_Index_DDL']
                else:
                    target_data_df = pd.DataFrame(
                        columns=['Schema_Name', 'Table_Name', 'Index_Name', 'Target_Index_Column',
                                 'Target_Index_DDL'])

                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                target_data_df['Target_Index_Column'] = target_data_df['Target_Index_Column'].str.replace("['", '',
                                                                                                          regex=False).replace(
                    "']", '', regex=False).replace('["', '', regex=False).replace('"]', '', regex=False)
                target_data_df['Index_Name'] = target_data_df['Index_Name'].str.replace('___idx1$', '', case=False,
                                                                                        regex=True)

                matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Index_Name'],
                                        suffixes=('_Source', '_Target'), how='inner')
                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Index_Name'],
                                       suffixes=('_Source', '_Target'), how='left', indicator=True)
                source_data = source_data[source_data['_merge'] == 'left_only'].drop(
                    columns=['Target_Index_Column', 'Target_Index_DDL', '_merge'])
                source_data['Status'] = f"Available in {source_name} not in {target_name}"
                source_data['Target_Index_Column'] = 'Nan'
                source_data['Target_Index_DDL'] = 'Nan'

                target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Index_Name'],
                                       suffixes=('_Source', '_Target'), how='right', indicator=True)
                target_data = target_data[target_data['_merge'] == 'right_only'].drop(
                    columns=['Source_Index_Column', 'Source_Index_DDL', '_merge'])
                target_data['Status'] = f"Available in {target_name} not in {source_name}"
                target_data['Source_Index_Column'] = 'Nan'
                target_data['Source_Index_DDL'] = 'Nan'

                result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                result_df = result_df.drop(columns=['_merge'], errors='ignore')
                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)
            else:
                result_df = pd.DataFrame()

            inter_summary_objects_list = get_summary_objects_counts(result_df, object_type, schema_name, target_schema)
            final_summary_objects_list.extend(inter_summary_objects_list)

        final_summary_objects_df = pd.DataFrame(final_summary_objects_list,
                                                columns=['Source_Schema', 'Target_Schema', 'Object_Name',
                                                         'Source_Object_Count', 'Target_Object_Count',
                                                         'Difference_Count'])

        final_summary_objects_df.to_excel(writer, sheet_name='Summary', index=False)

        workbook = writer.book
        sheets = workbook.sheetnames
        sheets.remove('Summary')
        sheets.insert(0, 'Summary')
        workbook._sheets = [workbook.get_sheet_by_name(sheet) for sheet in sheets]
