import os, sys, re, shutil
from import_file import import_file
import pandas as pd
from common_modules.api import api_authentication, decrypt_database_details
from common_modules.stored_procedures import insert_config_name

schema_list_query = """
SELECT u.username FROM DBA_USERS u
LEFT JOIN
    DBA_SEGMENTS s ON u.username = s.owner
WHERE
    u.username NOT IN (
        'SYSTEM', 'SYS', 'APPOQSYS', 'REMOTE_SCHEDULER_AGENT', 'DBSFWMUSER',
        'CTXSYS', 'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS',
        'DVSYS', 'G<PERSON>ADMIN_INTERNAL', 'OR<PERSON>LUGINS', 'ORDDATA', 'MDSYS',
        'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM', 'XDB', 'WMSYS',
        'ORDSYS', 'DBSN<PERSON>', 'DVF', 'APEX_030200', 'EXFSYS', 'OWBSYS',
        'ONBSYS_AUDIT', 'SYSM<PERSON>', 'SCOTT'
    )
    AND u.account_status = 'OPEN'
GROUP BY u.username ORDER BY u.username
"""


def copy_validation_dag_files(file_name, config_files_path, local_migration_path, dag_path, root_path):
    source_file = local_migration_path + '/' + 'Dag_Files' + '/' + 'validation_dag_file.py'
    target_file = dag_path + '/' + f'validation_dag_file_{file_name}.py'
    with open(source_file, 'r') as file:
        file_content = file.read()
        file_replaced_content = re.sub('@Config_File_Path',
                                       f'{config_files_path}/Validation/{file_name}.xlsx'.replace('\\', '/'),
                                       file_content)
        file_replaced_content = file_replaced_content.replace('@schedule_interval', 'None').replace("'@pause_flag'",
                                                                                                     'True')
    with open(target_file, 'w') as f:
        f.write(file_replaced_content)

    dag_triggers_file = local_migration_path + '/' + 'Dag_Files' + '/' + 'validation_triggers.py'
    trigger_destination_file = dag_path + '/' + 'validation_triggers.py'
    with open(dag_triggers_file, 'rb') as f:
        file_content = f.read()
    with open(trigger_destination_file, 'wb') as f:
        f.write(file_content)

    if not os.path.exists(root_path + '/Validation_Queries/'):
        os.makedirs(root_path + '/Validation_Queries/')

    xml_files = local_migration_path + '/' + 'validation_queries.xml'
    xml_destination_file = root_path + '/' + 'Validation_Queries' + '/' + 'validation_queries.xml'
    shutil.copyfile(xml_files, xml_destination_file)


def validation_load_trigger(task_name, project_id, migration_name, source_connection_id, target_connection_id, schema_name,
                     target_schema, file_name, cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')
        dag_path = getattr(import_object, 'Dag_Path')

        working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')
        local_migration_path = local_root_path + '/' + 'Migrations' + '/' + migration_name

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)

            token_data = api_authentication()

            source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
            source_function_call = getattr(import_object, 'DB_connection')

            project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
            project_function_call = getattr(import_object, 'connect_database')

            execute_query_function_call = getattr(import_object, 'execute_query')

            if target_connection_id in ['', None]:
                config_files_path = root_path + '/' + str(source_connection_id) + '/' + 'Config_Files'
            else:
                config_files_path = root_path + '/' + str(source_connection_id) + '/' + str(
                    target_connection_id) + '/' + 'Config_Files'

            if not os.path.exists(config_files_path + '/' + 'Validation'):
                os.makedirs(config_files_path + '/' + 'Validation')

            if not os.path.exists(working_directory_path):
                os.makedirs(working_directory_path)

            schema_list = []
            source_connection, error = source_function_call(source_DB_details)
            if schema_name != '':
                schema_list = [i for i in schema_name.split(',') if i.strip()]
            else:
                schema_list = execute_query_function_call(source_connection, schema_list_query)

            validation_file_name = 'validation_' + file_name.replace('.xlsx', '')
            cdc_config_file_path = config_files_path + '/' + 'Validation' + '/' + validation_file_name + '.xlsx'
            temp_cdc_config_path = working_directory_path + '/' + validation_file_name + '.xlsx'

            with pd.ExcelWriter(temp_cdc_config_path, engine="xlsxwriter") as writer:
                cdc_config_list = [
                    (task_name, project_id, migration_name, source_connection_id,
                     target_connection_id,
                     ','.join(schema_list), target_schema, validation_file_name)]
                cdc_config_df = pd.DataFrame(cdc_config_list,
                                             columns=['Process_Type', 'Project_Id',
                                                      'Migration_Name',
                                                      'Source_Connection_Id', 'Target_Connection_Id',
                                                      'Schema','Target_Schema', 'File_Name'])
                cdc_config_df = cdc_config_df.transpose()
                key_value_pairs = cdc_config_df.to_dict()[0]
                cdc_config_df = pd.DataFrame(list(key_value_pairs.items()),
                                             columns=['Parameter', 'Value'])
                cdc_config_df.fillna('', inplace=True)
                cdc_config_df.to_excel(writer, sheet_name="Configuration", index=False)

            shutil.copyfile(temp_cdc_config_path, cdc_config_file_path)
            project_connection = project_function_call(project_DB_details)
            insert_config_name(project_connection, validation_file_name, task_name, source_connection_id,
                               target_connection_id, None, None, 'Created')
            copy_validation_dag_files(validation_file_name, config_files_path.replace(root_path + '/', ''), local_migration_path, dag_path, root_path)
        else:
            print(f'Module path {db_module_path} not found')
    else:
        print(f'Config path {config_path} not found')
