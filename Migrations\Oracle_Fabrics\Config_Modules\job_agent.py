import os, sys, time, shutil, json, requests
from requests.auth import HTTPBasicAuth
import pandas as pd
from datetime import datetime
from import_file import import_file
from common_modules.api import api_authentication, decrypt_database_details
from common_modules.stored_procedures import get_config_files, get_running_dags, get_config_processed_dags, \
    insert_config_status, insert_job_agent_tracking, update_job_agent_tracking, fetch_agent_status


def api_dag_trigger(connection_url, dag_id, ):
    url = connection_url + 'dags/' + dag_id
    headers = {'content-type': 'application/json'}
    dag_response = requests.get(url, headers=headers,
                                    auth=HTTPBasicAuth('user', 'Quadrant@123'))
    dag_response_code = dag_response.status_code
    if dag_response_code == 200:
        dag_response_json = dag_response.json()
        if dag_response_json['is_paused']:
            payload = {
                "is_paused": False
            }
            pause_response = requests.patch(url, data=json.dumps(payload), headers=headers,
                                            auth=HTTPBasicAuth('user', 'Quadrant@123'))
            pause_status_code = pause_response.status_code
            pause_status_response = {'status_code': pause_status_code, 'text': pause_response.text}
            if pause_status_response['status_code'] == 200:
                url = connection_url + 'dags/' + dag_id + '/dagRuns'
                payload = {
                }
                headers = {'content-type': 'application/json'}
                response = requests.post(url, data=json.dumps(payload), headers=headers,
                                         auth=HTTPBasicAuth('user', 'Quadrant@123'))
                status_code = response.status_code
                response_json = response.json()
                status_response = {'status_code': status_code, 'dag_run_id': response_json['dag_run_id']}
            else:
                status_response = {'status_code': dag_response_code, 'dag_run_id': ''}
        else:
            status_response = {'status_code': dag_response_code, 'dag_run_id': 'Already_Unpaused'}
    else:
        status_response = {'status_code': dag_response_code, 'dag_run_id': ''}
    print(f"Status resposne: {status_response}")
    return status_response

def check_dag_status(connection_url, dag_id, dag_run_id):
    url = connection_url + 'dags/' + dag_id + '/dagRuns/' + dag_run_id
    print(url, '=====checkdagstatusurl====')
    headers = {'content-type': 'application/json'}
    status_response = requests.get(url, headers=headers,
                                   auth=HTTPBasicAuth('user', 'Quadrant@123'))
    response_json = status_response.json()
    status_response = {'status_code': status_response.status_code, 'dag_status': response_json['state']}
    print(status_response,'===statusresponse in dag status===')
    return status_response


def job_agent_trigger(task_name, project_id, migration_name, source_connection_id, target_connection_id,
                      cloud_category):
    print('Entered Job agent trigger')
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')
        connection_url = os.getenv("AIR_HOST", "http://qmig-airflow-webserver.ns-1245.svc.cluster.local:8080")
        connection_url = connection_url + '/airflow/api/v1/'

        local_migration_path = local_root_path + '/' + 'Migrations' + '/' + migration_name

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)

            token_data = api_authentication()

            project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
            project_function_call = getattr(import_object, 'connect_database')

            project_connection = project_function_call(project_DB_details)
            agent_start_time = datetime.now()

            agent_data = fetch_agent_status(project_connection, source_connection_id, target_connection_id)
            if not agent_data:
                print('No agentdata=')
                agent_id = insert_job_agent_tracking(project_connection, source_connection_id, target_connection_id,
                                                     agent_start_time,
                                                     'Running')

                if target_connection_id in ['', None]:
                    config_files_path = root_path + '/' + str(source_connection_id) + '/' + 'Config_Files'
                else:
                    config_files_path = root_path + '/' + str(source_connection_id) + '/' + str(
                        target_connection_id) + '/' + 'Config_Files'

                if not os.path.isfile(config_files_path + '/' + 'chunk_configuration.json'):
                    shutil.copyfile(local_migration_path + '/' + 'chunk_configuration.json',
                                    config_files_path + '/' + 'chunk_configuration.json')

                with open(config_files_path + '/' + 'chunk_configuration.json', 'r') as f:
                    chunk_configuration = json.loads(f.read())
                    dag_concurrency = chunk_configuration['Dag_Concurrency']

                try:
                    data_migration_config_path = f'{config_files_path}/Data_Migration'
                    while True:
                        project_connection = project_function_call(project_DB_details)
                        config_data = get_config_files(project_connection, source_connection_id, target_connection_id)

                        if config_data:
                            print('config data present')
                            for config_tuple in config_data:
                                file_path = data_migration_config_path + '/' + str(config_tuple[1]) + '.xlsx'

                                config_read = pd.read_excel(file_path, sheet_name='Configuration')
                                config_read.fillna('', inplace=True)
                                config_list = config_read.to_dict(orient='records')
                                config_dict = {}
                                for i in config_list: config_dict.update({i['Parameter']: i['Value']})

                                dags_df = pd.read_excel(file_path, sheet_name='Dags')
                                config_dags_list = dags_df['Dag Name'].values.tolist()
                                print(config_dags_list, '===config_dags_list')

                                while True:
                                    running_dags = get_running_dags(project_connection)
                                    config_running_dags = list(set([t[1] for t in running_dags if t[2] == config_tuple[0]]))
                                    print(config_running_dags, '==config running dags')

                                    config_processed_dags = get_config_processed_dags(project_connection,
                                                                                      config_tuple[0])
                                    print(config_processed_dags, '===config_processed_dags')
                                    
                                    failed_flag = any(t[2] == 'Fail' for t in config_processed_dags)
                                    dag_count_to_run = int(dag_concurrency) - len(running_dags)

                                    not_started_dags = [item for item in config_dags_list if
                                                        item not in config_running_dags + [t[1] for t in
                                                                                           config_processed_dags]]
                                    not_started_dags = list(set(not_started_dags))
                                    print(not_started_dags, '===not_started_dags')
                                    if dag_count_to_run != 0:
                                        print('dag_count_to_run is not zero')
                                        if config_tuple[4] == 'E2E_Data_Load':
                                            if (config_dict['CDC_Load_Type'] == 'File') and (
                                                    not any(item.startswith("CDC_") for item in config_running_dags)):
                                                if any(item.startswith("Load_Files_") for item in config_dags_list):
                                                    dag_count_to_run = dag_count_to_run - 1
                                                    if dag_count_to_run < 0:
                                                        break
                                                filtered_config_dags = [item for item in not_started_dags if
                                                                        not (item.startswith("CDC_") or item.startswith(
                                                                            "Load_Files_"))][:dag_count_to_run]
                                                if filtered_config_dags:
                                                    for loop_dag in filtered_config_dags:
                                                        while True:
                                                            status_response = api_dag_trigger(connection_url, loop_dag)
                                                            if status_response['status_code'] == 200:
                                                                print(f"Triggered dag {loop_dag}")
                                                                while True:
                                                                    status = check_dag_status(connection_url, loop_dag,
                                                                                              status_response[
                                                                                                  'dag_run_id'])
                                                                    if status['dag_status'] == 'running':
                                                                        break
                                                                    time.sleep(5)
                                                                break
                                                            else:
                                                                time.sleep(10)
                                                        time.sleep(45)
                                                cdc_extraction_dag = \
                                                [item for item in not_started_dags if item.startswith("CDC_")][0]
                                                while True:
                                                    status_response = api_dag_trigger(connection_url,
                                                                                      cdc_extraction_dag)
                                                    if status_response['status_code'] == 200:
                                                        print(f"Triggered dag {cdc_extraction_dag}")
                                                        while True:
                                                            status = check_dag_status(connection_url,
                                                                                      cdc_extraction_dag,
                                                                                      status_response[
                                                                                          'dag_run_id'])
                                                            if status['dag_status'] == 'running':
                                                                break
                                                            time.sleep(5)
                                                        break
                                                    else:
                                                        time.sleep(10)
                                                time.sleep(45)

                                            elif (config_dict['CDC_Load_Type'] == 'File') and (
                                            any(item.startswith("CDC_") for item in config_running_dags)):
                                                if len(not_started_dags) == 1 and not_started_dags[0].startswith(
                                                        'Load_Files_'):
                                                    if not failed_flag:
                                                        if set([item for item in config_dags_list if
                                                                not (item.startswith("CDC_") or item.startswith(
                                                                    'Load_Files'))]) == set(
                                                            [t[1] for t in config_processed_dags]):
                                                            load_files_dag = \
                                                                [item for item in not_started_dags if
                                                                 item.startswith("Load_Files_")][0]
                                                            while True:
                                                                status_response = api_dag_trigger(connection_url,
                                                                                                  load_files_dag)
                                                                if status_response['status_code'] == 200:
                                                                    print(f"Triggered dag {load_files_dag}")
                                                                    while True:
                                                                        status = check_dag_status(connection_url,
                                                                                                  load_files_dag,
                                                                                                  status_response[
                                                                                                      'dag_run_id'])
                                                                        if status['dag_status'] == 'running':
                                                                            break
                                                                        time.sleep(5)
                                                                    break
                                                                else:
                                                                    time.sleep(10)
                                                            time.sleep(45)
                                                            insert_config_status(project_connection, config_tuple[0],
                                                                                 'Load_Files')
                                                            break
                                                        else:
                                                            continue
                                                    else:
                                                        insert_config_status(project_connection, config_tuple[0],
                                                                             'Fail')
                                                        break
                                                else:
                                                    filtered_config_dags = [item for item in not_started_dags if
                                                                            not item.startswith("Load_Files_")][
                                                                           :dag_count_to_run]
                                                    if filtered_config_dags:
                                                        for loop_dag in filtered_config_dags:
                                                            while True:
                                                                status_response = api_dag_trigger(connection_url,
                                                                                                  loop_dag)
                                                                if status_response['status_code'] == 200:
                                                                    print(f"Triggered dag {loop_dag}")
                                                                    while True:
                                                                        status = check_dag_status(connection_url,
                                                                                                  loop_dag,
                                                                                                  status_response[
                                                                                                      'dag_run_id'])
                                                                        if status['dag_status'] == 'running':
                                                                            break
                                                                        time.sleep(5)
                                                                    break
                                                                else:
                                                                    time.sleep(10)
                                                            time.sleep(45)
                                            elif (config_dict['CDC_Load_Type'] == 'Database'):
                                                if len(not_started_dags) == 1 and not_started_dags[0].startswith(
                                                        'CDC_'):
                                                    if not failed_flag:
                                                        if set([item for item in config_dags_list if
                                                                not item.startswith("CDC_")]) == set([
                                                            t[1] for t in config_processed_dags]):
                                                            cdc_dag = \
                                                                [item for item in not_started_dags if
                                                                 item.startswith("CDC_")][0]
                                                            while True:
                                                                status_response = api_dag_trigger(connection_url,
                                                                                                  cdc_dag)
                                                                if status_response['status_code'] == 200:
                                                                    print(f"Triggered dag {cdc_dag}")
                                                                    while True:
                                                                        status = check_dag_status(connection_url,
                                                                                                  cdc_dag,
                                                                                                  status_response[
                                                                                                      'dag_run_id'])
                                                                        if status['dag_status'] == 'running':
                                                                            break
                                                                        time.sleep(5)
                                                                    break
                                                                else:
                                                                    time.sleep(10)
                                                            time.sleep(45)
                                                            insert_config_status(project_connection, config_tuple[0],
                                                                                 'CDC')
                                                            break
                                                        else:
                                                            continue
                                                    else:
                                                        insert_config_status(project_connection, config_tuple[0],
                                                                             'Fail')
                                                        break
                                                else:
                                                    filtered_config_dags = [item for item in not_started_dags if
                                                                            not item.startswith("CDC_")][
                                                                           :dag_count_to_run]
                                                    if filtered_config_dags:
                                                        for loop_dag in filtered_config_dags:
                                                            while True:
                                                                status_response = api_dag_trigger(connection_url,
                                                                                                  loop_dag)
                                                                if status_response['status_code'] == 200:
                                                                    print(f"Triggered dag {loop_dag}")
                                                                    while True:
                                                                        status = check_dag_status(connection_url,
                                                                                                  loop_dag,
                                                                                                  status_response[
                                                                                                      'dag_run_id'])
                                                                        if status['dag_status'] == 'running':
                                                                            break
                                                                        time.sleep(5)
                                                                    break
                                                                else:
                                                                    time.sleep(10)
                                                            time.sleep(45)
                                        else:
                                            print('entered initial data load')
                                            filtered_config_dags = not_started_dags[:dag_count_to_run]
                                            if filtered_config_dags:
                                                for loop_dag in filtered_config_dags:
                                                    while True:
                                                        status_response = api_dag_trigger(connection_url, loop_dag)
                                                        print(status_response,'====statusresponse of api_dag_trigger====')
                                                        if status_response['status_code'] == 200:
                                                            print(f"Triggered dag {loop_dag}")
                                                            while True:
                                                                status = check_dag_status(connection_url, loop_dag,
                                                                                          status_response['dag_run_id'])
                                                                print(status,'===check_dag_status===')
                                                                if status['dag_status'] == 'running':
                                                                    break
                                                                time.sleep(5)
                                                            break
                                                        else:
                                                            time.sleep(10)
                                                    time.sleep(45)
                                            else:
                                                if set([item for item in config_dags_list]) == set([
                                                    t[1] for t in config_processed_dags]):
                                                    if failed_flag:
                                                        insert_config_status(project_connection,
                                                                             config_tuple[0], 'Fail')
                                                        break
                                                    else:
                                                        insert_config_status(project_connection,
                                                                             config_tuple[0], 'Success')
                                                        break
                                    else:
                                        print("Dag count is zero, cannot start any new dags")
                        else:
                            print('No Config files found at the moment')
                except Exception as error:
                    print(f"Error occurred at agent process: {error}")
                    agent_end_time = datetime.now()
                    update_job_agent_tracking(project_connection, agent_id, 'Error', str(error), agent_end_time)
            else:
                print(f'Agent already running for this {source_connection_id} and {target_connection_id}')
        else:
            print(f'Module path {db_module_path} not found')
    else:
        print(f'Config path {config_path} not found')
