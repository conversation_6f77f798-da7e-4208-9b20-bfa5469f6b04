import psycopg2, pymssql

def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


def DB_connection(database_data):
    host_name = database_data['host']
    service_name = database_data['service_name']
    user_name = database_data['name']
    password = database_data['password']
    port = database_data['port']
    try:
        connection = pymssql.connect(user=user_name, password=password,
                                     server=host_name,
                                     database=service_name, port=port)
        error = ''
    except pymssql.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near database connection", e)
    return connection,error


def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        data = cursor.fetchall()
        data = [[str(value) for value in row] for row in data]
    except Exception as e:
        print("Issue found near database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data
