import os, re, requests, json, psycopg2, cx_Oracle, logging, csv, shutil, sys, pymssql, math
from datetime import datetime
import pandas as pd
import numpy as np
import xml.etree.ElementTree as ET
from sqlalchemy import create_engine, types
from azure.identity import ClientSecretCredential
from azure.storage.blob import BlobServiceClient, ContentSettings
from deltalake import write_deltalake, DeltaTable, Schema, Field
from azure.storage.filedatalake import DataLakeServiceClient
from itertools import chain, repeat
from pyspark.sql import SparkSession
import pyarrow
from io import BytesIO
from databricks import sql
import ast


connection_url = os.getenv("API_HOST", 'http://qmig-eng.qmig-ns.svc.cluster.local:8080')
connection_url = connection_url + '/api/v1/'


def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


def DB_connection(database_data):
    host_name = database_data['host']
    service_name = database_data['service_name']
    user_name = database_data['name']
    password = database_data['password']
    port = database_data['port']
    try:
        connection = pymssql.connect(user=user_name, password=password,
                                     server=host_name,
                                     database=service_name, port=port)
        error = ''
    except pymssql.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near database connection", e)
    return connection, error


def target_DB_connection(database_data):
    host_name = database_data['host']
    port = database_data['port']
    service_name = database_data['db_name']
    user_name = database_data['name']
    password = database_data['password']
    try:
        dsn = cx_Oracle.makedsn(host=host_name, port=port, service_name=service_name)
        connection = cx_Oracle.Connection(user=user_name, password=password, dsn=dsn)
        error = ''
    except cx_Oracle.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near database connection", e)
    return connection, error


def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        if query.startswith('USE') or query.startswith('EXEC'):
            cursor.execute(query)
            data=None
        else:
            cursor.execute(query)
            data = cursor.fetchall()
            data = [[str(value) if isinstance(value, cx_Oracle.LOB) else value for value in row] for row in data]
    except cx_Oracle.DatabaseError as e:
        print("Issue found near database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data


def api_authentication():
    url = connection_url + 'Common/Security/VerifyPython'
    project_id = os.environ['PROJECT_ID']
    params = {
        "projectId": str(project_id),
        "content": "8/2DlAH8q1R+untey0ZuqMuD89WX4m2XoSCWhxQ+UOk="}
    response = requests.get(url, params=params)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = json.loads(status_response['text'])
        token_data = data['token']
        return token_data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def get_project_DB_info(token_data, project_id):
    url = connection_url + 'Common/Master/GetProjectMigDetailSelect'
    payload = {
        "projectId": str(project_id),
        "migsrcType": "D"
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.post(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = data_text['jsonResponseData']
        data_text = [dict for dict in data_text['Table1'] if dict['dbconname'] == 'PRJDB' + str(project_id)][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def get_source_DB_info(token_data, project_id, source_connection_id):
    url = connection_url + 'Common/Master/GetDBConnections'
    payload = {
        "projectId": str(project_id),
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.get(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = [dict for dict in data_text['Table1'] if
                     dict['migsrctgt'] == 'S' and dict['Connection_ID'] == source_connection_id][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def get_target_DB_info(token_data, project_id, target_connection_id):
    url = connection_url + 'Common/Master/GetDBConnections'
    payload = {
        "projectId": str(project_id),
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.get(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = [dict for dict in data_text['Table1'] if
                     dict['migsrctgt'] == 'T' and dict['Connection_ID'] == target_connection_id][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def decrypt_string(encrypted_str, token_data):
    application_url = connection_url + 'Common/Cryptography/DecryptData'
    payload = {
        "NeedToDecrypt": encrypted_str
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response = requests.post(application_url, data=json.dumps(payload), headers=headers)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = response.text
        return data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def decrypt_database_details(token_data, project_id, category, connection_id):
    prj_data = {}
    prj_db_data = {}
    if category == 'Project':
        prj_db_data = get_project_DB_info(token_data, project_id)
    elif category == 'Source':
        prj_db_data = get_source_DB_info(token_data, project_id, connection_id)
    elif category == 'Target':
        prj_db_data = get_target_DB_info(token_data, project_id, connection_id)
    password = decrypt_string(prj_db_data['dbpassword'], token_data)
    userid = decrypt_string(prj_db_data['dbuserid'], token_data)
    userid = re.split('\s+', re.sub(r"[\x00-\x1F\x7F]", ' ', userid))
    userid = ' '.join(userid).strip()
    password = re.split('\s+', re.sub(r"[\x00-\x1F\x7F]", ' ', password))
    password = ' '.join(password).strip()
    port = prj_db_data['dbport']
    host = prj_db_data['dbhost']
    if category == 'Project':
        service_name = ''
        parallelprocess = ''
    else:
        service_name = prj_db_data['service_name']
        parallelprocess = prj_db_data['parallelprocess']
    dbname = prj_db_data['dbname']
    prj_data['password'] = password
    prj_data['name'] = userid
    prj_data['port'] = port
    prj_data['host'] = host
    prj_data['db_name'] = dbname
    prj_data['service_name'] = service_name
    prj_data['parallelprocess'] = parallelprocess
    return prj_data


def get_config_id(connection, file_name, process_type):
    cursor = connection.cursor()
    try:
        cursor.execute(
            f"select config_id, transaction_number, transaction_file, config_status from audit_config where config_name = '{file_name}' and process_type = '{process_type}'")
            
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def transaction_insert(connection, config_id, transaction_number, transaction_file, config_status):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_transaction_insert(%s,%s,%s,%s,%s);fetch all in "dataset";',
                       (config_id, transaction_number, transaction_file,config_status, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data

def dag_insert(connection, dag_name, dag_type, schema_name, target_schema, table_name,
               table_size, concurrency, chunk_size, chunk_parts, dag_status, config_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_dags_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (dag_name, dag_type, schema_name, target_schema, table_name, table_size, concurrency, chunk_size,
             chunk_parts, dag_status, config_id, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data

def dag_update(connection, dag_id, dag_status, dag_end_time):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_audit_dags_update(%s,%s,%s,%s);fetch all in "dataset";',
                       (dag_id, dag_status,dag_end_time, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def task_insert(connection, task_name, task_type, attempt, lower_bound, upper_bound, request_memory, limit_memory, task_start_time,
                task_status, dag_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_task_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (task_name, task_type, attempt, str(lower_bound), str(upper_bound), request_memory, limit_memory, str(task_start_time),
             task_status, dag_id,'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def task_update(connection, task_id, task_row_count, task_status, task_error, task_end_time, extraction_time, transform_time, load_time):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_task_update(%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (task_id, task_row_count, task_status, task_error, task_end_time, extraction_time, transform_time, load_time,'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data



def cdc_data_insert(connection, batch_number, batch_length, batch_size, load_type, data_file_name, batch_start_time,
                    batch_extraction_time, batch_transform_time, batch_load_time, batch_end_time, task_id):
    cursor = connection.cursor()
    try:
        print(batch_number,"===", batch_length,"===", batch_size,"===", load_type,"===", data_file_name,"===", batch_start_time,"===", batch_extraction_time,"===",
             batch_transform_time,"===", batch_load_time,"===", batch_end_time,"===", task_id, 'dataset')
        cursor.execute(
            'call public.sp_audit_cdc_data_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (batch_number, batch_length, str(batch_size), load_type, data_file_name, batch_start_time, batch_extraction_time,
             batch_transform_time, batch_load_time, batch_end_time, task_id, 'dataset'))
        data = cursor.fetchall()
    except Exception as error:
        data = None
        print(f"Error occurred at cdc data insert: {str(error)}")
        pass
    finally:
        connection.commit()
        cursor.close()
    return data




def cdc_errors_insert(connection, batch_error_list):
    cdc_error_insert_query = "insert into public.audit_cdc_error_tracking (transaction, transaction_error, transaction_error_time, data_id) values (%s,%s,%s,%s,%s)"

    cursor = connection.cursor()
    try:
        cursor.executemany(cdc_error_insert_query, batch_error_list)
    except Exception as error:
        print(f"Error occurred at cdc data insert: {str(error)}")
        pass
    finally:
        connection.commit()
        cursor.close()




def pre_validation_trigger(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    table_name = kwargs['table_name']
    file_name = kwargs['file_name']
    token_data = kwargs['token_data']
    dag_name = kwargs['dag_id']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
    source_connection, error = DB_connection(source_DB_details)

    process_type = 'Initial_Data_Load' if file_name.startswith('init_') else 'E2E_Data_Load'
    config_data = get_config_id(project_connection, file_name, process_type)
    print(config_data)
    config_id = config_data[0][0]

    if config_data[0][1] in ['', None]:
        print(source_DB_details,"==source_DB_details")
        execute_query(source_connection, f"USE {source_DB_details['db_name']}")
        execute_query(source_connection,"EXEC sys.sp_cdc_enable_db;")
        try:
            execute_query(source_connection,f"""EXEC sys.sp_cdc_enable_table
            @source_schema =   '{schema_name}', --> schema_name
            @source_name =  '{table_name}', --> table_name
            @role_name = NULL,
            @capture_instance =  '{schema_name}_{table_name}' ;  """)
        except pymssql.exceptions.OperationalError as operational_error:
            print(operational_error)
        except Exception as e:
            print(e)
        scn_fetch_query = "SELECT GETDATE() AS CurrentDateTime;"
        scn_data = execute_query(source_connection, scn_fetch_query)
        scn_number = scn_data[0][0]

        transaction_insert(project_connection, config_id, scn_number, None, 'Running')
    else:
        scn_number = config_data[0][1]

    dag_id = dag_insert(project_connection, dag_name, 'Data_Migration', schema_name, target_schema, table_name, kwargs['table_size'],
                        kwargs['concurrency'], kwargs['chunk_size'], kwargs['chunk_parts'],
                        'Running', config_id)
    dag_id = dag_id[0][0]
    ti.xcom_push(key='dag_config', value={'dag_id': dag_id, 'config_id': config_id, 'scn_number': scn_number})

    task_start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_id = task_insert(project_connection, 'pre_validation_task', 'Pre_Validation', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    extra_path = os.environ['EXTRA_FOLDER']
    if target_connection_id in ['', None]:
        root_folder = extra_path + '/' + str(source_connection_id)
    else:
        root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

    scn_files_folder = root_folder + '/' + 'SCN_Files' + '/' + file_name.lower()
    if not os.path.exists(scn_files_folder):
        os.makedirs(scn_files_folder)

    current_scn_file = scn_files_folder + '/' + 'current_scn_file.txt'
    with open(current_scn_file, 'w') as cscn:
        cscn.write(str(scn_number))

    open_scn_file = scn_files_folder + '/' + 'open_scn_file.txt'
    with open(open_scn_file, 'w') as file_openscn:
        file_openscn.write('')

    commit_scn_file = scn_files_folder + '/' + 'commit_scn_file.txt'
    with open(commit_scn_file, 'w') as file_csn:
        file_csn.write('')

    task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_update(project_connection, task_id, None, 'Success', str(error), task_end_time, None, None, None)
    print(f"Pre validation completed for {table_name}")
    return False
	
	
def map_datatype(dtype, *args):
    sqlserver_to_databricks = {
        'INT': lambda *_: 'INT',
        'INTEGER': lambda *_: 'INT',
        'BIGINT': lambda *_: 'BIGINT',
        'SMALLINT': lambda *_: 'SMALLINT',
        'TINYINT': lambda *_: 'TINYINT',
        'BIT': lambda *_: 'BOOLEAN',
        'DECIMAL': lambda *_: 'DECIMAL(38,18)',  # Default high precision
        'NUMERIC': lambda *_: 'DECIMAL(38,18)',
        'FLOAT': lambda *_: 'DOUBLE',
        'REAL': lambda *_: 'FLOAT',
        'MONEY': lambda *_: 'DECIMAL(19,4)',
        'SMALLMONEY': lambda *_: 'DECIMAL(10,4)',
        'CHAR': lambda *_: 'STRING',
        'NCHAR': lambda *_: 'STRING',
        'VARCHAR': lambda *_: 'STRING',
        'NVARCHAR': lambda *_: 'STRING',
        'TEXT': lambda *_: 'STRING',
        'NTEXT': lambda *_: 'STRING',
        'DATE': lambda *_: 'DATE',
        'DATETIME': lambda *_: 'TIMESTAMP',
        'DATETIME2': lambda *_: 'TIMESTAMP',
        'SMALLDATETIME': lambda *_: 'TIMESTAMP',
        'TIME': lambda *_: 'STRING',  # Spark has no TIME type
        'DATETIMEOFFSET': lambda *_: 'TIMESTAMP',
        'BINARY': lambda *_: 'BINARY',
        'VARBINARY': lambda *_: 'BINARY',
        'IMAGE': lambda *_: 'BINARY',
        'UNIQUEIDENTIFIER': lambda *_: 'STRING',
        'SQL_VARIANT': lambda *_: 'STRING',
        'XML': lambda *_: 'STRING',
        'GEOGRAPHY': lambda *_: 'STRING',
        'GEOMETRY': lambda *_: 'STRING',
        'HIERARCHYID': lambda *_: 'STRING'
    }

    mapper = sqlserver_to_databricks.get(dtype.upper(), lambda *_: 'STRING')
    return mapper()



def table_migration(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    table_name = kwargs['table_name']
    target_schema = kwargs['target_schema']
    part_name = kwargs['task_name']
    token_data = kwargs['token_data']
    lower_bound = kwargs['lower_bound']
    upper_bound = kwargs['upper_bound']
    request_memory = kwargs['request_memory']
    limit_memory = kwargs['limit_memory']
    table_category = kwargs['table_category']
    data_load_type = kwargs['data_load_type']

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']
    attempt = ti.try_number

    extraction_time = None
    transform_time = None
    load_time = None

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)
    task_start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_id = task_insert(project_connection, str(table_name).lower() + '_' + part_name.lower(), 'Data_Migration',
                          attempt,
                          lower_bound, upper_bound, request_memory, limit_memory, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    try:
        extra_path = os.environ['EXTRA_FOLDER']
        if target_connection_id in ['', None]:
            root_folder = extra_path + '/' + str(source_connection_id)
        else:
            root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

        log_path = root_folder + '/' + 'Data_Migration_Logs'
        if not os.path.exists(log_path):
            os.makedirs(log_path)
        task_logger = logging.getLogger(__name__)
        task_logger.setLevel(logging.DEBUG)
        log_handler = logging.FileHandler(log_path + '/' + str(table_name).lower() + '_' + part_name.lower() + '.log')
        log_handler.setFormatter(logging.Formatter("%(asctime)s [%(levelname)s] - %(message)s"))
        log_handler.addFilter(lambda record: record.levelno != logging.WARNING)
        task_logger.addHandler(log_handler)

        task_logger.debug(table_name)
        task_logger.debug('Table execution started')

        source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
        source_connection, error = DB_connection(source_DB_details)

        datatypes_fetch_query = f"""SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE UPPER(TABLE_SCHEMA) = '{schema_name.upper()}' AND UPPER(TABLE_NAME) = '{table_name.upper()}'"""
        datatypes_data = execute_query(source_connection, datatypes_fetch_query)

        column_names_list = []
        date_columns_list = []
        bit_columns_list = []
        text_columns_list = []
        float_columns_list = []
        for row in datatypes_data:
            column_names_list.append(row[0].upper())
            if row[1].lower() in ['datetime']:
                date_columns_list.append(row[0].upper())
            if row[1].lower() in ['bit', 'bigint', 'decimal', 'money', 'int', 'numeric', 'smallint', 'tinyint','smallmoney']:
                bit_columns_list.append(row[0].upper())
            if row[1].lower() in ['nvarchar', 'varchar', 'text', 'sysname', 'uniqueidentifier', 'nvarchar(max)','varchar(max)']:
                text_columns_list.append(row[0].upper())
            if row[1].lower() in ['float', 'real']:
                float_columns_list.append(row[0].upper())
        column_names_string = ','.join(column_names_list)

        source_data_query = "select {4} from(select *, ROW_NUMBER() OVER(ORDER BY(SELECT NULL)) AS ROWNUM FROM {0}.{1}) a where a.ROWNUM >= {2} and a.ROWNUM < {3}".format(
            schema_name, table_name, lower_bound, upper_bound,column_names_string)
        task_logger.debug(source_data_query)

        extraction_start_time = datetime.now()
        source_cursor = source_connection.cursor()
        
        source_cursor.execute(f"""SELECT column_name as PRIMARYKEYCOLUMN FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS AS TC INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE AS KU ON TC.CONSTRAINT_TYPE = 'PRIMARY KEY' AND TC.CONSTRAINT_NAME = KU.CONSTRAINT_NAME and KU.table_schema = '{schema_name.upper()}' AND KU.table_name='{table_name.upper()}' ORDER BY KU.TABLE_NAME,KU.ORDINAL_POSITION;""")
        unqiue_columns = source_cursor.fetchall()
        unqiue_columns_string = unqiue_columns[0][0]

        source_cursor.execute(source_data_query)
        table_data_list = source_cursor.fetchall()
        source_cursor.close()
        extraction_end_time = datetime.now()
        extraction_time = (extraction_end_time - extraction_start_time).total_seconds() / 60
        print(f"Extracted records: {len(table_data_list)}")

        if table_data_list:
            transform_start_time = datetime.now()
            df = pd.DataFrame(table_data_list, columns=column_names_list)
            transform_end_time = datetime.now()
            transform_time = (transform_end_time - transform_start_time).total_seconds() / 60

            target_DB_details = decrypt_database_details(token_data, project_id, 'Target',
                                                         str(target_connection_id))
            credential = ClientSecretCredential(target_DB_details['service_name'], target_DB_details['name'],
                                                target_DB_details['password'])
            if 'abfss://' in target_DB_details['host'] and 'Tables' in target_DB_details['host']:
                delta_token = credential.get_token("https://storage.azure.com/.default").token
                storage_options = {"bearer_token": delta_token, "use_fabric_endpoint": "true"}
                delta_table_path = target_DB_details['host']

                fields = [Field(name, data_type) for name, data_type in target_datatypes_data]
                schema_object = Schema(fields)

                load_start_time = datetime.now()
                DeltaTable.create(table_uri=f"{delta_table_path}/{schema_name}/{table_name}",
                                  schema=schema_object.to_pyarrow(),
                                  storage_options=storage_options,
                                  mode='ignore')
                print(f"{delta_table_path}/{schema_name}/{table_name}")
                write_deltalake(f"{delta_table_path}/{schema_name}/{table_name}", df, schema=schema_object.to_pyarrow(),
                                mode='append',
                                storage_options=storage_options)
                load_end_time = datetime.now()

            elif 'https://' in target_DB_details['host'] and 'Files' in target_DB_details['host']:
                extra_path = os.environ['EXTRA_FOLDER']
                file_path = extra_path + f"/Data_Migration_Files/{table_name}"
                file_name = f"{str(part_name.split('Part_')[1]).zfill(20)}.parquet"
                metadata_file_name = file_path+"/_metadata.json"
                final_sheet_name = file_path +"/"+file_name

                onelake_account_url = "https://onelake.dfs.fabric.microsoft.com"
                file_system_name = target_DB_details['host'].split('onelake.dfs.fabric.microsoft.com/')[1]
                oneLake_file_name = f"{table_name}/{str(part_name.split('Part_')[1]).zfill(20)}.parquet"
                load_start_time = datetime.now()
                service_client = DataLakeServiceClient(account_url=onelake_account_url, credential=credential)
                file_system_client = service_client.get_file_system_client(file_system_name)
                file_client = file_system_client.get_file_client(oneLake_file_name)
                oneLake_metadata_file_name = f"{table_name}/_metadata.json"
                metadata_file_client = file_system_client.get_file_client(oneLake_metadata_file_name)


                if not os.path.exists(file_path):
                    os.makedirs(file_path)
                print(metadata_file_name)
                if not os.path.isfile(metadata_file_name):
                    print(metadata_file_name)
                    metadata_content = {"keyColumns": [unqiue_columns_string]}
                    json_object = json.dumps(metadata_content)
                    with open(metadata_file_name, "w") as outfile:
                        outfile.write(json_object)
                    with open(metadata_file_name,"rb") as data:
                        metadata_file_client.upload_data(data, overwrite=True)
                    
                df.to_parquet(final_sheet_name)
                
                
                with open(final_sheet_name,"rb") as data:
                    file_client.upload_data(data, overwrite=True)
               
                load_end_time = datetime.now()
				
            elif 'azuredatabricks.net' in target_DB_details['host']:
                print('Inside elif bolk of databricks=======630',datatypes_data)
                ddl = f"CREATE TABLE IF NOT EXISTS {target_DB_details['name']}.{schema_name}_{table_name} (\n"
                ddl += ",\n".join([
                    f"  {col[0].lower()} {map_datatype(col[1])}" for col in datatypes_data
                ])
                ddl += "\n) USING DELTA;"
                transform_start_time = datetime.now()
                df = pd.DataFrame(table_data_list, columns=column_names_list)
                column_names_string = ",".join(column_names_list)
                
                table_data = df.to_csv(index=False, encoding="utf-8",header=False)
                
                transform_end_time = datetime.now()
                transform_time = (transform_end_time - transform_start_time).total_seconds() / 60

                target_DB_details = decrypt_database_details(token_data, project_id, 'Target',
                                                         str(target_connection_id))

                connection_string = "DefaultEndpointsProtocol=https;AccountName=qmigstg1137;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
                container_name = 'databricks'
                blob_service_client = BlobServiceClient.from_connection_string(connection_string)
                container_client = blob_service_client.get_container_client(container_name)
                if not container_client.exists():
                    container_client.create_container()
                blob_client = container_client.get_blob_client(f"{schema_name}_{table_name}.csv")
                blob_client.upload_blob(table_data, overwrite=True,
                                        content_settings=ContentSettings(content_type="text/csv"))

                target_connection = sql.connect(
                        server_hostname = f"{target_DB_details['host']}", 
                        http_path = f"{target_DB_details['db_name']}", 
                        access_token = f"{target_DB_details['service_name']}") 
                        

                target_cursor = target_connection.cursor()
                data_load_query = f"""COPY INTO {target_DB_details['name']}.{schema_name}_{table_name} ({column_names_string})
                        FROM '/mnt/datamigration/{schema_name}_{table_name}.csv'
                        FILEFORMAT = CSV
                       FORMAT_OPTIONS ('header' = 'false')
                        """


                load_start_time = datetime.now()
                target_cursor.execute(ddl)
                target_cursor.execute(data_load_query)
                target_connection.commit()
                target_cursor.close()
                target_connection.close()
                blob_client.delete_blob()
                load_end_time = datetime.now()

            else:
                if target_connection_id in ['', None]:
                    root_folder = str(source_connection_id)
                else:
                    root_folder = str(source_connection_id) + '/' + str(target_connection_id)
                data_files_path = f"{root_folder}/Data_Files/{schema_name.lower()}/{table_name.lower()}"
                file_name = data_files_path + '/' + table_name.lower() + '_' + part_name.lower() + '.csv'

                csv_data = df.to_csv(index=False, encoding="utf-8")

                connection_string = "DefaultEndpointsProtocol=https;AccountName=qmigstg1137;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
                container_name = 'qmigrator'
                blob_service_client = BlobServiceClient.from_connection_string(connection_string)
                container_client = blob_service_client.get_container_client(container_name)
                if not container_client.exists():
                    container_client.create_container()
                blob_client = container_client.get_blob_client(file_name)
                blob_client.upload_blob(csv_data, overwrite=True,
                                        content_settings=ContentSettings(content_type="text/csv"))

                connection_string = f"Driver={{ODBC Driver 18 for SQL Server}};Server={target_DB_details['host']},1433;Database={target_DB_details['db_name']};Encrypt=Yes;TrustServerCertificate=No"
                token_object = credential.get_token("https://database.windows.net//.default")
                token_as_bytes = bytes(token_object.token, "UTF-8")
                encoded_bytes = bytes(chain.from_iterable(zip(token_as_bytes, repeat(0))))
                token_bytes = struct.pack("<i", len(encoded_bytes)) + encoded_bytes
                attrs_before = {1256: token_bytes}

                if 'datawarehouse.fabric.microsoft.com' in target_DB_details['host']:
                    query = f"""COPY INTO {schema_name.upper()}.{table_name.upper()}
                        FROM 'https://qmigstg1137.blob.core.windows.net/{container_name}/{file_name}'
                        WITH (
                        FILE_TYPE = 'CSV',
                        CREDENTIAL=(IDENTITY= 'Storage Account Key', SECRET='****************************************************************************************'),
                        FIRSTROW =2
                        )
                        """
                else:
                    query = f"""BULK INSERT {schema_name.upper()}.{table_name.upper()}
                        FROM '{file_name}'
                        WITH (
                            DATA_SOURCE = 'MyBlobStorage',
                            FIELDTERMINATOR = ',', 
                            ROWTERMINATOR = '\n', 
                            FIRSTROW = 2  
                        );
                        """

                load_start_time = datetime.now()
                target_connection = pyodbc.connect(connection_string, attrs_before=attrs_before)
                target_cursor = target_connection.cursor()
                target_cursor.execute(query)
                target_connection.commit()
                target_cursor.close()
                target_connection.close()
                load_end_time = datetime.now()

            load_time = (load_end_time - load_start_time).total_seconds() / 60

            task_status = 'Success'
            task_logger.debug('Table execution ended')

            task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')

            project_connection = connect_database(project_DB_details)
            task_update(project_connection, task_id, len(table_data_list), task_status, None, task_end_time,
                        extraction_time, transform_time, load_time)
    except Exception as e:
        task_status = 'Fail'
        task_error = str(e)

        task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        project_connection = connect_database(project_DB_details)
        task_update(project_connection, task_id, None, task_status, task_error, task_end_time, extraction_time, transform_time,
                    load_time)
        ti.UP_FOR_RETRY


def post_validation_trigger(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    table_name = kwargs['table_name']
    token_data = kwargs['token_data']

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']
    scn_number = dag_config['scn_number']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, 'post_validation_task', 'Post_Validation', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    validation_flag = False
    try:

        source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
        source_connection, error = DB_connection(source_DB_details)

        source_row_count_query = f"select count(*) from {schema_name.upper()}.{table_name.upper()}"
        source_row_count = execute_query(source_connection, source_row_count_query)[0][0]

        target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
        target_connection, error = target_DB_connection(target_DB_details)

        target_row_count_query = f"select /*+ PARALLEL({target_DB_details['parallelprocess']}) */ count(*) from {target_schema.lower()}.{table_name.lower()}"
        target_row_count = execute_query(target_connection, target_row_count_query)[0][0]

        difference_count = abs(target_row_count - source_row_count)
        if difference_count != 0:
            validation_flag = True
            print(
                f"Row count not matching between source {source_row_count} and target {target_row_count}")

        if validation_flag:
            # insert into project db
            print(f"Post validation failed for {table_name}. Please check data migration ")

            task_end_time = datetime.now()
            task_update(project_connection, task_id, None,'Fail', str(error), task_end_time, None, None, None)

            dag_end_time = datetime.now()
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
        else:
            print(f"Post validation completed for {table_name}")

            task_end_time = datetime.now()
            task_update(project_connection, task_id, None, 'Success', None, task_end_time, None, None, None)
    except Exception as error:
        task_end_time = datetime.now()
        task_update(project_connection, task_id, None,'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        if ti.try_number == 3:
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
        ti.UP_FOR_RETRY
    return validation_flag


def get_constraints_list(connection, source_connection_id, schema_name, table_name):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_get_constraints_list(%s,%s,%s,%s);fetch all in "dataset";',
                       (source_connection_id, schema_name, table_name, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        print("Error at fetching constraints list: " + str(err))
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def deploy_table_constraints_trigger(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    target_schema = kwargs['target_schema']
    schema_name = kwargs['schema']
    table_name = kwargs['table_name']
    token_data = kwargs['token_data']

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, 'deploy_constraints_task', 'Deploy_Constraints', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]
    try:
        tables_data_list = get_constraints_list(project_connection, source_connection_id, schema_name, table_name)

        target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
        table_ddl_code_list = [i[0] for i in tables_data_list]
        print("length of table_ddl_code_list==", len(table_ddl_code_list))

        if len(table_ddl_code_list) > 0:
            target_connection, error = target_DB_connection(target_DB_details)
            for query in table_ddl_code_list:
                query = re.sub(rf'{schema_name}\.', target_schema + '.', query,
                               flags=re.IGNORECASE | re.DOTALL)
                cursor = target_connection.cursor()
                try:
                    cursor.execute(query)
                except psycopg2.DatabaseError as e:
                    print(f"Issue found near deploying constraint '{query}': {str(e)}")
                finally:
                    cursor.close()
                    target_connection.commit()
        print("Deployed the constraints and indexes for the table: " + table_name)

        task_end_time = datetime.now()
        task_update(project_connection, task_id, None,'Success', None, task_end_time, None, None, None)
    except Exception as error:
        task_end_time = datetime.now()
        task_update(project_connection, task_id, None,'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        if ti.try_number == 3:
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)

        ti.UP_FOR_RETRY


def complete_validation_trigger(ti, **kwargs):
    process_type = kwargs['process_type']
    project_id = kwargs['project_id']
    token_data = kwargs['token_data']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    table_name = kwargs['table_name']
    schema_name = kwargs['schema']

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)
    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
    source_connection, error = DB_connection(source_DB_details)
    source_row_cnt_query = "SELECT count(*) from {1}.{0}".format(
        table_name.upper(),
        schema_name.upper())
    source_row_cnt1 = execute_query(source_connection, source_row_cnt_query)
    print(source_row_cnt1)
    source_row_cnt = source_row_cnt1[0][0]
    target_row_cnt = source_row_cnt1[0][0]
    diff_count = target_row_cnt - source_row_cnt
    table_dict = {"Schema_name_Source":schema_name,"Table_Name": table_name,"Table_Type" : "Table","Schema_name_Target": schema_name,"Status" : "Available in both Oracle and Fabric"}
    table_df = pd.DataFrame([table_dict])
    table_row_count_dict ={"Source_Schema":schema_name,"Target_Schema": schema_name,"Table_Name": table_name,"Source_Row_Count":source_row_cnt,"Target_Row_Count": target_row_cnt,"Difference_Count": diff_count }
    table_row_count_df = pd.DataFrame([table_row_count_dict])
    extra_path = os.environ['EXTRA_FOLDER']
    file_path = extra_path +"/Validation_Reports"
    file_cretion_time =datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    file_name = f"Post_Validation_report_{file_cretion_time}.xlsx"
    final_sheet_name = file_path +"/"+file_name
    table_df.to_excel(final_sheet_name, sheet_name="Table")
    table_row_count_df.to_excel(final_sheet_name, sheet_name="Table_Row_Count")


    task_start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_id = task_insert(project_connection, 'complete_validation_task', 'Complete_Validation', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]
    
   
    task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_update(project_connection, task_id, None, 'Success', None, task_end_time, None, None, None)

    if process_type in ('Initial_Data_Load','E2E_Data_Load'):
        dag_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        dag_update(project_connection, dag_id, 'Success', dag_end_time)
    return False

def cdc_trigger(ti,**kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    table_name = kwargs['table_name']
    batch_size = 1000
    config_file_name = kwargs['file_name']
    cdc_load_type = kwargs['cdc_load_type']
    token_data = kwargs['token_data']
    file_name = kwargs['file_name']
    dag_name = kwargs['dag_id']
    process_type = kwargs['process_type']
    table_name = table_name.split('.')[1]
    print(table_name)

    log_file_list = []

    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
    target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    extra_path = os.environ['EXTRA_FOLDER']
    root_folder = extra_path  + '/' + str(source_connection_id) + '/' + str(target_connection_id)
    scn_files_folder = root_folder + '/' + 'SCN_Files' + '/' + file_name.lower()
    cdc_data_files_folder = root_folder + '/' + 'CDC_Data_Files' 
    
        
    if table_name in ['', None]:
        scn_files_folder = extra_path + '/' + 'CDC_SCN_Files' + '/' + str(source_DB_details['db_name']).capitalize()
        if not os.path.exists(scn_files_folder):
            os.makedirs(scn_files_folder)
        
        cdc_data_files_folder = extra_path + '/' + 'CDC_Data_Files' + '/' + str(
            source_DB_details['db_name']).capitalize()
        
        source_connection, error = DB_connection(source_DB_details)
        source_cursor = source_connection.cursor()
        scn_fetch_query = "SELECT GETDATE() AS CurrentDateTime;"
        scn_data = execute_query(source_connection, scn_fetch_query)
        scn_number = scn_data[0][0]
        
        current_scn_file = scn_files_folder + '/' + 'current_scn_file.txt'
        with open(current_scn_file, 'w') as f_csn:
            f_csn.write(str(scn_number))
        print(scn_number, 'SCN Number as current scn')
        
        open_scn_file = scn_files_folder + '/' + 'open_scn_file.txt'
        with open(open_scn_file, 'w') as file_openscn:
            file_openscn.write(str(''))

        commit_scn_file = scn_files_folder + '/' + 'commit_scn_file.txt'
        with open(commit_scn_file, 'w') as file_commitscn:
            file_commitscn.write(str(''))
    else:
        current_scn_file = scn_files_folder + '/' + 'current_scn_file.txt'
        with open(current_scn_file, 'r') as cscn:
            current_scn = cscn.readline()

        open_scn_file = scn_files_folder + '/' + 'open_scn_file.txt'
        with open(open_scn_file, 'r') as file_openscn:
            open_scn = file_openscn.readline()

        commit_scn_file = scn_files_folder + '/' + 'commit_scn_file.txt'
        with open(commit_scn_file, 'r') as file_csn:
            commit_scn = file_csn.readline()


    if not os.path.exists(scn_files_folder):
        os.makedirs(scn_files_folder)

    if not os.path.exists(cdc_data_files_folder):
        os.makedirs(cdc_data_files_folder)

    current_scn_file = scn_files_folder + '/' + 'current_scn_file.txt'
    with open(current_scn_file, 'r') as file_openscn:
        current_scn = file_openscn.readline()

    open_scn_file = scn_files_folder + '/' + 'open_scn_file.txt'
    with open(open_scn_file, 'r') as file_openscn:
        open_scn = file_openscn.readline()
    print(open_scn, "==open_scn")

    commit_scn_file = scn_files_folder + '/' + 'commit_scn_file.txt'
    with open(commit_scn_file, 'r') as file_csn:
        commit_scn = file_csn.readline()
    print(commit_scn, "==commit_scn")

    config_data = get_config_id(project_connection, file_name, process_type)
    config_id = config_data[0][0]
    dag_id = dag_insert(project_connection, dag_name, 'CDC', schema_name, target_schema, table_name,
                        None,
                         None, None, None,
                        'Running', config_id)
    dag_id = dag_id[0][0]

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, 'cdc_' + str(file_name), 'CDC', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    
    source_connection, error = DB_connection(source_DB_details)
    source_cursor = source_connection.cursor()
    unqiue_query = f"""SELECT upper(COLUMN_NAME) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE TABLE_SCHEMA = '{schema_name}' AND TABLE_NAME = '{table_name}' AND CONSTRAINT_NAME = (
              SELECT CONSTRAINT_NAME FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE TABLE_SCHEMA = '{schema_name}' AND TABLE_NAME = '{table_name}' AND CONSTRAINT_TYPE = 'PRIMARY KEY');"""
    
    
    source_cursor.execute(unqiue_query)

    uk_columns = [row[0] for row in source_cursor.fetchall()]
    uk_columns = ', '.join(uk_columns)

    query = f"""
        SELECT upper(COLUMN_NAME), DATA_TYPE  FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '{schema_name}' AND TABLE_NAME = '{table_name}' ORDER BY ORDINAL_POSITION;
        """
    source_cursor.execute(query)

        # Fetch all results
    columns_data = source_cursor.fetchall()

    # Initialize empty lists to store column names and data types
    column_names = []
    data_types = []

    # Iterate over the fetched results and populate the lists
    for row in columns_data:
        column_names.append(row[0])  # Column name
        data_types.append(row[1])    # Data type

    # Create the dictionary
    columns_info = {
        'COLUMN_NAME': column_names,
        'DATA_TYPE': data_types
    }

    columns = ['__$start_lsn', '__$seqval', '__$operation as __rowMarker__'] + column_names ## lower added
    columns_df = pd.DataFrame(columns_info)

    table_name_cdc = schema_name +'_'+ table_name +'_CT'
    if commit_scn == '':
        start_lsn= current_scn
    else:
        start_lsn = commit_scn
    print('before select')

    query = f"""
                SELECT TOP ({batch_size})
               sys.fn_cdc_map_lsn_to_time(__$start_lsn) AS 'Time',
               {', '.join(columns)}
                FROM cdc.{table_name_cdc}
                where sys.fn_cdc_map_lsn_to_time(__$start_lsn)  > '{start_lsn.rstrip('0')}'
            and __$operation != 3
            ORDER BY __$start_lsn;
            """
        
    print('after select')

    print(query,'query')
    df = pd.read_sql(query, source_connection)
    print(' i am here')

    fetch_start_time ,fetch_end_time= datetime.now(),datetime.now()
    transformation_start_time , transformation_end_time = datetime.now(),datetime.now()
    load_start_time, load_end_time = datetime.now(),datetime.now()
    #if not df.empty:
    if 'https://' in target_DB_details['host'] and 'Files' in target_DB_details['host']:
    #if 10==10:
        batch_number = 1
        while True:
            print('inside while loop')
            print(batch_number)
            fetch_start_time = datetime.now()
            fetch_end_time = datetime.now()
            batch_start_time = datetime.now()
            if not df.empty:
                print(' i am here')
                batch_length = len(df)
                #batch_memory = sum(sys.getsizeof(batch_data) for statement in batch_data)
                batch_error_list=[]

                transformation_start_time = datetime.now()
                try:
                    extra_path = os.environ.get('EXTRA_FOLDER','C:\\Vamsi\\extract')
                    file_path = extra_path + f"/CDC_Data_Files/{table_name}"
                    target_DB_details = decrypt_database_details(token_data, project_id, 'Target',str(target_connection_id))


                    # Constants
                    onelake_account_url = "https://onelake.dfs.fabric.microsoft.com"
                    file_system_name = target_DB_details['host'].split('onelake.dfs.fabric.microsoft.com/')[1] 
                    #file_system_name = 'https://onelake.dfs.fabric.microsoft.com/885e2486-6d16-466a-ba1a-5b13ec774281/18514a3d-8fa1-4e74-96eb-387daef1587f/Files/LandingZone'.split('onelake.dfs.fabric.microsoft.com/')[1]


                    credential = ClientSecretCredential(target_DB_details['service_name'], target_DB_details['name'], target_DB_details['password']) 
                    #credential = ClientSecretCredential("0eadb77e-42dc-47f8-bbe3-ec2395e0712c", # tenant_id
                    #                     "760f1c32-86a9-4eab-b088-2fa1f7bfde9c", #client_id
                    #                     "****************************************")
                    service_client = DataLakeServiceClient(account_url=onelake_account_url, credential=credential)
                    file_system_client = service_client.get_file_system_client(file_system_name)
                    onelake_file_name = table_name.upper()
                    table_file_list = []
                    
                    if not os.path.isdir(file_path):
                        os.makedirs(file_path)
                        
                    # Step 2: Decide where to read the files from
                    if os.listdir(file_path):
                        # If file_path is not empty, use it
                        final_path = file_path
                    else:
                        newfile_path = extra_path + f"/Data_Migration_Files/{table_name}"
                        # If file_path is empty, use newfile_path
                        final_path = newfile_path
                        
                        # Step 3: List files from final_path
                    file_dir_list = os.listdir(final_path)

                    print("==5.1==")
                    for files in file_dir_list:
                        if '.parquet' in files:
                            base_name = str(files.split('.')[0])
                            try:
                                table_file_list.append(int(str(base_name)))
                            except Exception as e:
                                print(e)
                        
                    new_seq = str(max(table_file_list)+1).zfill(20)
                    onelake_file_name= f"{table_name}/{new_seq}.parquet"
                    storage_file_name = f"{file_path}/{new_seq}.parquet"
                    file_client = file_system_client.get_file_client(onelake_file_name)
                    column_names.append('__rowMarker__')
                    df['__rowMarker__'] = df['__rowMarker__'].replace(2, 0)#'INSERT'
                    df['__rowMarker__'] = df['__rowMarker__'].replace(1, 2)#'DELETE'
                    df['__rowMarker__'] = df['__rowMarker__'].replace(4, 1)#'UPDATE'
                    last_start_lsn = df['Time'].iloc[-1]
                    df = df[column_names]
                    df = df.reindex(columns=['__rowMarker__'] + df.columns.drop('__rowMarker__').tolist())
                    df.to_parquet(storage_file_name, engine='pyarrow')
                    print(df)
                    with open(storage_file_name,"rb") as data:
                        file_client.upload_data(data, overwrite=True)
                    
                
                    print(last_start_lsn,'last_start_lsn',type(last_start_lsn))
                    formatted_timestamp = last_start_lsn.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]

                    with open(commit_scn_file, 'w') as file:
                        if formatted_timestamp != 0:
                            file.write(str(formatted_timestamp))
                    
                 
                except Exception as e:
                    error_tuple = (config_id, batch_number, None, str(e), datetime.now())
                    batch_error_list.append(error_tuple)
                    print('exception')
                
                
                
                load_end_time = datetime.now()
                batch_end_time = datetime.now()
                extraction_time = (fetch_end_time- fetch_start_time).total_seconds() * 10**3
                transform_time = (transformation_end_time - transformation_start_time).total_seconds() * 10**3
                load_time = (load_end_time - load_start_time).total_seconds() * 10**3
                time_taken_by_batch = batch_end_time - batch_start_time
                time_diff_seconds = time_taken_by_batch.total_seconds()
                time_diff_minutes = round((time_diff_seconds / 60), 2)
    
                project_connection = connect_database(project_DB_details)
                project_cursor = project_connection.cursor()
                print(batch_error_list)
                    
                project_connection = connect_database(project_DB_details)
                cdc_data_id = cdc_data_insert(project_connection, batch_number, batch_length, batch_size, cdc_load_type,
                                                      None, batch_start_time, extraction_time, transform_time, load_time,
                                                      batch_end_time, task_id)
                if batch_error_list:
                    batch_error_list = [t + (cdc_data_id,) for t in batch_error_list]
                    cdc_errors_insert(project_connection, batch_error_list)

                batch_number = batch_number+1
                print('batch_number',batch_number)
                df = pd.DataFrame()
                print('empty dataframe')
            else:
                print('data frame is empty')
                source_connection, error = DB_connection(source_DB_details)
                #source_connection, error = DB_connection()
                source_cursor = source_connection.cursor()
                unqiue_query = f"""SELECT upper(COLUMN_NAME) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE TABLE_SCHEMA = '{schema_name}' AND TABLE_NAME = '{table_name}' AND CONSTRAINT_NAME = (
                 SELECT CONSTRAINT_NAME FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE TABLE_SCHEMA = '{schema_name}' AND TABLE_NAME = '{table_name}' AND CONSTRAINT_TYPE = 'PRIMARY KEY');"""
                source_cursor.execute(unqiue_query)
                uk_columns = [row[0] for row in source_cursor.fetchall()]
                uk_columns = ', '.join(uk_columns)

                query = f"""
                        SELECT upper(COLUMN_NAME), DATA_TYPE  FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '{schema_name}' AND TABLE_NAME = '{table_name}' ORDER BY ORDINAL_POSITION;
                    """
                source_cursor.execute(query)
                # Fetch all results
                columns_data = source_cursor.fetchall()

                # Initialize empty lists to store column names and data types
                column_names = []
                data_types = []

                # Iterate over the fetched results and populate the lists
                for row in columns_data:
                    column_names.append(row[0])  # Column name
                    data_types.append(row[1])    # Data type

                # Create the dictionary
                columns_info = {
                    'COLUMN_NAME': column_names,
                    'DATA_TYPE': data_types
                }

                columns = ['__$start_lsn', '__$seqval', '__$operation as __rowMarker__'] + column_names ## lower added
                columns_df = pd.DataFrame(columns_info)

                table_name_cdc = schema_name +'_'+ table_name +'_CT'
                current_scn_file = scn_files_folder + '/' + 'current_scn_file.txt'
                with open(current_scn_file, 'r') as file_openscn:
                    current_scn = file_openscn.readline()

            
                commit_scn_file = scn_files_folder + '/' + 'commit_scn_file.txt'
                with open(commit_scn_file, 'r') as file_csn:
                    commit_scn = file_csn.readline()
                print(commit_scn, "==commit_scn")

                if commit_scn == '':
                    start_lsn = current_scn
                else:
                    start_lsn = commit_scn
                print('before select')

                query = f"""
                    SELECT TOP ({batch_size})
                    sys.fn_cdc_map_lsn_to_time(__$start_lsn) AS 'Time',
                    {', '.join(columns)}
                    FROM cdc.{table_name_cdc}
                    where sys.fn_cdc_map_lsn_to_time(__$start_lsn)  > '{start_lsn.rstrip('0')}'
                    and __$operation != 3
                    ORDER BY __$start_lsn;
                    """
        

                df = pd.read_sql(query, source_connection)
               


    