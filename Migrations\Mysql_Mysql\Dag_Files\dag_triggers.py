import os, re, requests, json, psycopg2, logging, csv, shutil, sys, mysql.connector, subprocess
from datetime import datetime
import pandas as pd
import numpy as np
import xml.etree.ElementTree as ET

connection_url = os.getenv("API_HOST", 'http://qmig-eng.qmig-ns.svc.cluster.local:8080')
connection_url = connection_url + '/api/v1/'


def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


def DB_connection(database_data):
    host_name = database_data['host']
    port = database_data['port']
    database = database_data['db_name']
    user_name = database_data['name']
    password = database_data['password']
    try:
        connection = mysql.connector.connect(
            host=host_name,
            port=port,
            database=database,
            user=user_name,
            password=password
        )
        error = ''
    except Exception as e:
        connection = None
        error = str(e)
        print("Issue found near Target database connection", e)
    return connection, error


def target_DB_connection(database_data):
    host_name = database_data['host']
    port = database_data['port']
    database = database_data['db_name']
    user_name = database_data['name']
    password = database_data['password']
    try:
        connection = mysql.connector.connect(
            host=host_name,
            port=port,
            database=database,
            user=user_name,
            password=password
        )
        error = ''
    except Exception as e:
        connection = None
        error = str(e)
        print("Issue found near Target database connection", e)
    return connection, error


def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        data = cursor.fetchall()
    except mysql.connector.DatabaseError as e:
        print("Issue found near Target database query", e)
        data = None
    except Exception as e:
        print("Issue found near database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data


def api_authentication():
    url = connection_url + 'Common/Security/VerifyPython'
    project_id = os.environ['PROJECT_ID']
    params = {
        "projectId": str(project_id),
        "content": "8/2DlAH8q1R+untey0ZuqMuD89WX4m2XoSCWhxQ+UOk="}
    response = requests.get(url, params=params)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = json.loads(status_response['text'])
        token_data = data['token']
        return token_data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def get_project_DB_info(token_data, project_id):
    url = connection_url + 'Common/Master/GetProjectMigDetailSelect'
    payload = {
        "projectId": str(project_id),
        "migsrcType": "D"
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.post(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = data_text['jsonResponseData']
        data_text = [dict for dict in data_text['Table1'] if dict['dbconname'] == 'PRJDB' + str(project_id)][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def get_source_DB_info(token_data, project_id, source_connection_id):
    url = connection_url + 'Common/Master/GetDBConnections'
    payload = {
        "projectId": str(project_id),
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.get(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = [dict for dict in data_text['Table1'] if
                     dict['migsrctgt'] == 'S' and dict['Connection_ID'] == source_connection_id][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def get_target_DB_info(token_data, project_id, target_connection_id):
    url = connection_url + 'Common/Master/GetDBConnections'
    payload = {
        "projectId": str(project_id),
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.get(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = [dict for dict in data_text['Table1'] if
                     dict['migsrctgt'] == 'T' and dict['Connection_ID'] == target_connection_id][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def decrypt_string(encrypted_str, token_data):
    application_url = connection_url + 'Common/Cryptography/DecryptData'
    payload = {
        "NeedToDecrypt": encrypted_str
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response = requests.post(application_url, data=json.dumps(payload), headers=headers)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = response.text
        return data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def decrypt_database_details(token_data, project_id, category, connection_id):
    prj_data = {}
    prj_db_data = {}
    if category == 'Project':
        prj_db_data = get_project_DB_info(token_data, project_id)
    elif category == 'Source':
        prj_db_data = get_source_DB_info(token_data, project_id, connection_id)
    elif category == 'Target':
        prj_db_data = get_target_DB_info(token_data, project_id, connection_id)
    password = decrypt_string(prj_db_data['dbpassword'], token_data)
    userid = decrypt_string(prj_db_data['dbuserid'], token_data)
    userid = re.split('\s+', re.sub(r"[\x00-\x1F\x7F]", ' ', userid))
    userid = ' '.join(userid).strip()
    password = re.split('\s+', re.sub(r"[\x00-\x1F\x7F]", ' ', password))
    password = ' '.join(password).strip()
    port = prj_db_data['dbport']
    host = prj_db_data['dbhost']
    if category == 'Project':
        service_name = ''
        parallelprocess = ''
    else:
        service_name = prj_db_data['service_name']
        parallelprocess = prj_db_data['parallelprocess']
    dbname = prj_db_data['dbname']
    prj_data['password'] = password
    prj_data['name'] = userid
    prj_data['port'] = port
    prj_data['host'] = host
    prj_data['db_name'] = dbname
    prj_data['service_name'] = service_name
    prj_data['parallelprocess'] = parallelprocess
    return prj_data


def get_config_id(connection, file_name, process_type):
    cursor = connection.cursor()
    try:
        cursor.execute(
            f"select config_id, transaction_number, transaction_file, config_status from audit_config where config_name = '{file_name}' and process_type = '{process_type}'")
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def transaction_insert(connection, config_id, transaction_number, transaction_file, config_status):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_transaction_insert(%s,%s,%s,%s,%s);fetch all in "dataset";',
                       (config_id, transaction_number, transaction_file, config_status, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def dag_insert(connection, dag_name, dag_type, schema_name, target_schema, table_name,
               table_size, concurrency, chunk_size, chunk_parts, dag_status, config_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_dags_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (dag_name, dag_type, schema_name, target_schema, table_name, table_size, concurrency, chunk_size,
             chunk_parts, dag_status, config_id, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def dag_update(connection, dag_id, dag_status, dag_end_time):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_audit_dags_update(%s,%s,%s,%s);fetch all in "dataset";',
                       (dag_id, dag_status, dag_end_time, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def task_insert(connection, task_name, task_type, attempt, lower_bound, upper_bound, request_memory, limit_memory,
                task_start_time,
                task_status, dag_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_task_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (task_name, task_type, attempt, str(lower_bound), str(upper_bound), request_memory, limit_memory,
             str(task_start_time),
             task_status, dag_id, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def task_update(connection, task_id, task_row_count, task_status, task_error, task_end_time, extraction_time,
                transform_time, load_time):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_task_update(%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (
            task_id, task_row_count, task_status, task_error, task_end_time, extraction_time, transform_time, load_time,
            'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def pre_validation_trigger(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    table_name = kwargs['table_name']
    file_name = kwargs['file_name']
    process_type = kwargs['process_type']
    data_load_type = kwargs['data_load_type']
    token_data = kwargs['token_data']
    dag_name = kwargs['dag_id']

    source_name = 'MariaDB'
    target_name = 'MySQL'

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
    source_connection, error = DB_connection(source_DB_details)

    config_data = get_config_id(project_connection, file_name, process_type)
    config_id = config_data[0][0]

    if data_load_type != 'Data_Dumper':
        transaction_insert(project_connection, config_id, config_data[0][1], config_data[0][2], 'Running')
    else:
        transaction_insert(project_connection, config_id, None, None, 'Running')

    dag_id = dag_insert(project_connection, dag_name, 'Data_Migration', schema_name, target_schema, table_name,
                        kwargs['table_size'],
                        kwargs['concurrency'], kwargs['chunk_size'], kwargs['chunk_parts'],
                        'Running', config_id)
    dag_id = dag_id[0][0]
    ti.xcom_push(key='dag_config',value={'dag_id': dag_id, 'config_id': config_id})

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, 'pre_validation_task', 'Pre_Validation', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]
    try:
        extra_path = os.environ['EXTRA_FOLDER']
        if target_connection_id in ['', None]:
            root_folder = extra_path + '/' + str(source_connection_id)
        else:
            root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

        if data_load_type != 'Data_Dumper':
            position_file_folder = root_folder + '/' + 'Position_Files' + '/' + file_name.lower()
            if not os.path.exists(position_file_folder):
                os.makedirs(position_file_folder)
            with open(position_file_folder + '/' + 'position.txt', "w") as f:
                f.write(f"{config_data[0][2]},{config_data[0][1]}")

        validation_flag = False

        column_result_df, partition_result_df, datatypes_result_df = pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

        if target_connection_id not in ['', None] and data_load_type != 'Data_Dumper':
            target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
            target_connection, error = target_DB_connection(target_DB_details)

            xml_path = extra_path + '/' + 'Validation_Queries' + '/' + 'validation_queries.xml'
            tree = ET.parse(xml_path)
            root = tree.getroot()

            objects_list = ['Table', 'Datatype']

            for object_type in objects_list:
                source_query_tag = 'Table_Validation_Queries/Source/' + '/' + object_type
                object_source_query = list(root.iterfind(source_query_tag))[0].text
                object_source_query = object_source_query.replace('@schemaname', schema_name.upper()).replace('@order',
                                                                                                              '').replace(
                    '@degree', str(source_DB_details['parallelprocess'])).replace('@tablename', table_name.upper())

                source_object_output = execute_query(source_connection, object_source_query)

                target_query_tag = 'Table_Validation_Queries/Target/' + '/' + object_type
                object_target_query = list(root.iterfind(target_query_tag))[0].text
                object_target_query = object_target_query.replace('@schemaname', target_schema.lower()).replace(
                    '@order',
                    '').replace(
                    '@degree', str(target_DB_details['parallelprocess'])).replace('@tablename', table_name.lower())
                target_object_output = execute_query(target_connection, object_target_query)

                if object_type == "Table":
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Source_Schema_Name', 'Table_Name', 'Table_Type'])

                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Target_Schema_Name', 'Table_Name', 'Table_Type'])
                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    source_data_df['Table_Type'] = source_data_df['Table_Type'].replace({'base table': 'table'})
                    target_data_df['Table_Type'] = target_data_df['Table_Type'].replace({'base table': 'table'})
                    matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Table_Type'],
                                            suffixes=('_Source', '_Target'), how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                    matched_tables_list = matched_data['Table_Name'].values.tolist()

                    if table_name.lower() in matched_tables_list:
                        source_column_count_query = f"select count(*) from information_schema.columns where upper(table_schema) = '{schema_name.upper()}' and UPPER(table_name) = '{table_name.upper()}'"
                        target_column_count_query = f"select count(*) from information_schema.columns where upper(table_schema) = '{target_schema.upper()}' and upper(TABLE_NAME) = '{table_name.upper()}'"

                        source_column_data = execute_query(source_connection, source_column_count_query)
                        target_column_data = execute_query(target_connection, target_column_count_query)

                        source_column_df = pd.DataFrame(source_column_data,
                                                        columns=['Column_Name', 'Data_Type'])
                        target_column_df = pd.DataFrame(target_column_data,
                                                        columns=['Column_Name', 'Data_Type'])
                        source_column_df = source_column_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                        target_column_df = target_column_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                        matched_column_data = pd.merge(source_column_df, target_column_df, on=['Column_Name'],
                                                       suffixes=('_Source', '_Target'), how='inner')
                        matched_column_data['Status'] = f"Available in both {source_name} and {target_name}"

                        source_data = pd.merge(source_column_df, target_column_df, on=['Column_Name'],
                                               suffixes=('_Source', '_Target'), how='left', indicator=True)
                        source_data = source_data[source_data['_merge'] == 'left_only']
                        source_data['Status'] = f"Available in {source_name} not in {target_name}"

                        target_data = pd.merge(source_column_df, target_column_df, on=['Column_Name'],
                                               suffixes=('_Source', '_Target'), how='right', indicator=True)
                        target_data = target_data[target_data['_merge'] == 'right_only']
                        target_data['Status'] = f"Available in {target_name} not in {source_name}"

                        column_result_df = pd.concat([matched_column_data, source_data, target_data], ignore_index=True)
                        column_result_df = column_result_df.drop(columns=['_merge'], errors='ignore')

                        if not source_data.empty or not target_data.empty:
                            validation_flag = True
                            print(
                                f"Column count not matching between source {len(source_column_df)} and target {len(target_column_df)}, please check the report for detailed information")
                    else:
                        validation_flag = True
                        print(
                            f"{table_name} not present in both sides, please check the report for detailed information")

                if object_type == 'Datatype':
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Source_Schema', 'Table_Name', 'Column_Name',
                                                           'Source_Datatype',
                                                           'Source_Datatype_Length', 'Source_Index', 'Table_Type'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Target_Schema', 'Table_Name', 'Column_Name',
                                                           'Target_Datatype',
                                                           'Target_Datatype_Length', 'Target_Index', 'Table_Type'])
                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                    target_data_df['Target_Modified_Datatype'] = target_data_df['Target_Datatype']
                    target_data_df['Target_Datatype_Length'] = target_data_df['Target_Datatype_Length'].str.replace('(',
                                                                                                                    '',
                                                                                                                    regex=False).str.replace(
                        ')', '', regex=False)

                    merged_df = pd.merge(source_data_df, target_data_df,
                                         on=['Table_Name', 'Column_Name', 'Table_Type'], how='inner')

                    datatypes_result = []
                    for index, row in merged_df.iterrows():
                        if row['Source_Modified_Datatype'] == row['Target_Modified_Datatype'] and row['Source_Index'] == \
                                row[
                                    'Target_Index'] and row['Source_Datatype_Length'] == row['Target_Datatype_Length']:
                            status = 'Matched'
                        else:
                            status = 'Not_Matched'
                            break
                        datatypes_result.append(
                            (row['Source_Schema'], row['Target_Schema'], row['Table_Name'], row['Column_Name'],
                             row['Source_Datatype'],
                             row['Source_Datatype_Length'], row['Target_Datatype'],
                             row['Target_Datatype_Length'], row['Source_Index'], row['Target_Index'],
                             row['Table_Type'], status))
                    datatypes_result_df = pd.DataFrame(datatypes_result,
                                                       columns=['Schema_Name', 'Target_Schema', 'Table_Name',
                                                                'Column_Name',
                                                                'Source_Datatype',
                                                                'Source_Datatype_Length', 'Target_Datatype',
                                                                'Target_Datatype_Length',
                                                                'Source_Index', 'Target_Index', 'Table_Type', 'Status'])
                    if 'Not_Matched' in datatypes_result_df['Status'].values.tolist():
                        validation_flag = True
                        print(
                            "Datatypes not matching between source and target, please check the report for detailed information")

        if validation_flag:
            pre_validation_path = root_folder + '/' + 'Dag_Validation_Reports' + '/' + file_name.lower() + '/' + dag_name
            if not os.path.exists(pre_validation_path):
                os.makedirs(pre_validation_path)

            with pd.ExcelWriter(pre_validation_path + '/' + 'pre_validation.xlsx') as writer:
                column_result_df.to_excel(writer, sheet_name='Columns', index=False)
                partition_result_df.to_excel(writer, sheet_name='Partitions', index=False)
                datatypes_result_df.to_excel(writer, sheet_name='Datatype_Matching', index=False)
            print(f"Pre validation failed for {table_name}. Please check report for detailed information")
            task_end_time = datetime.now()
            task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

            dag_end_time = datetime.now()
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
        else:
            print(f"Pre validation completed for {table_name}")
            task_end_time = datetime.now()
            task_update(project_connection, task_id, None, 'Success', None, task_end_time, None, None, None)
    except Exception as error:
        validation_flag = True
        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        if ti.try_number == 3:
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
    return validation_flag


def transform_blob(data):
    if data is not None:
        return bytes.fromhex(data.hex())


def table_migration(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    table_name = kwargs['table_name']
    target_schema = kwargs['target_schema']
    part_name = kwargs['task_name']
    token_data = kwargs['token_data']
    lower_bound = kwargs['lower_bound']
    upper_bound = kwargs['upper_bound']
    request_memory = kwargs['request_memory']
    limit_memory = kwargs['limit_memory']
    table_category = kwargs['table_category']
    data_load_type = kwargs['data_load_type']

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']
    attempt = ti.try_number

    extraction_time = None
    transform_time = None
    load_time = None

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)
    task_start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_id = task_insert(project_connection, str(table_name).lower() + '_' + part_name.lower(), 'Data_Migration',
                          attempt,
                          lower_bound, upper_bound, request_memory, limit_memory, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    try:
        extra_path = os.environ['EXTRA_FOLDER']
        if target_connection_id in ['', None]:
            root_folder = extra_path + '/' + str(source_connection_id)
        else:
            root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

        log_path = root_folder + '/' + 'Data_Migration_Logs'
        if not os.path.exists(log_path):
            os.makedirs(log_path)
        task_logger = logging.getLogger(__name__)
        task_logger.setLevel(logging.DEBUG)
        log_handler = logging.FileHandler(log_path + '/' + str(table_name).lower() + '_' + part_name.lower() + '.log')
        log_handler.setFormatter(logging.Formatter("%(asctime)s [%(levelname)s] - %(message)s"))
        log_handler.addFilter(lambda record: record.levelno != logging.WARNING)
        task_logger.addHandler(log_handler)

        task_logger.debug(table_name)
        task_logger.debug('Table execution started')

        source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
        source_connection, error = DB_connection(source_DB_details)

        blob_columns_list = []
        if table_category == 'Lob':
            datatypes_fetch_query = f"""SELECT COLUMN_NAME, DATA_TYPE FROM information_schema.COLUMNS WHERE UPPER(TABLE_SCHEMA) = '{table_name.upper()}' AND UPPER(TABLE_NAME) = '{schema_name.upper()}"""
            datatypes_data = execute_query(source_connection, datatypes_fetch_query)

            blob_columns_list = [column_name for column_name, data_type in datatypes_data if
                            data_type.upper().strip() in ['BLOB', 'LONGBLOB', 'BIT']]

        source_data_query = f"SELECT * FROM {schema_name}.{table_name} ORDER BY (SELECT NULL) LIMIT {upper_bound} OFFSET {lower_bound}"
        task_logger.debug(source_data_query)

        extraction_start_time = datetime.now()
        source_cursor = source_connection.cursor()
        source_cursor.execute(source_data_query)
        table_data_list = source_cursor.fetchall()
        column_names_list = [desc[0] for desc in source_cursor.description]
        source_cursor.close()
        extraction_end_time = datetime.now()
        extraction_time = (extraction_end_time - extraction_start_time).total_seconds() / 60
        print(f"Extracted records: {len(table_data_list)}")

        if table_data_list:
            transform_start_time = datetime.now()
            df = pd.DataFrame(table_data_list, columns=column_names_list)
            if table_category == 'Lob':
                for column_name in blob_columns_list:
                    df[column_name] = df[column_name].apply(transform_blob)
            else:
                transform_dict = {r'\\0': r'\\\\0', r'\t': r'\\t', r'\n': r'\\n', r'\r\n': r'\\r\\n', r'\r': r'\\r'}

                null_columns = df.columns[df.isnull().all()]
                columns_to_replace = df.columns.difference(null_columns)
                df.loc[:, columns_to_replace].replace(transform_dict, regex=True, inplace=True)

                df.replace({'NaT': np.nan}, inplace=True)
            transform_end_time = datetime.now()
            transform_time = (transform_end_time - transform_start_time).total_seconds() / 60

            print(f"Transformed records: {len(table_data_list)}")
            if data_load_type == 'File':
                data_files_path = f"{root_folder}/Data_Files/{schema_name.lower()}/{table_name.lower()}"
                if not os.path.exists(data_files_path):
                    os.makedirs(data_files_path)
                load_start_time = datetime.now()
                if os.path.isfile(root_folder + '/' + 'Config_Files' + '/' + 'chunk_configuration.json'):
                    with open(root_folder + '/' + 'Config_Files' + '/' + 'chunk_configuration.json', 'r') as f:
                        chunk_configuration_dict = json.loads(f.read())

                column_delimiter = chunk_configuration_dict['Data_File_Column_Delimiter']
                file_name = data_files_path + '/' + table_name.lower() + '_' + part_name.lower() + '.csv'
                df.to_csv(file_name, sep=column_delimiter, header=False, index=None)
                print(f"Data saved to {file_name}")
                load_end_time = datetime.now()
            else:
                columns_mod = ', '.join(column_names_list)
                values_mod = ', '.join(['%s'] * len(column_names_list))
                insert_sql = f"insert into {target_schema}.{table_name} ({columns_mod}) VALUES ({values_mod})"

                target_DB_details = decrypt_database_details(token_data, project_id, 'Target',
                                                             str(target_connection_id))
                target_connection, error = target_DB_connection(target_DB_details)

                load_start_time = datetime.now()
                target_cursor = target_connection.cursor()
                source_data_list = df.values.tolist()
                for i in range(0, len(source_data_list), 1000):
                    batch_data = source_data_list[i:i + 1000]
                    target_cursor.executemany(insert_sql, batch_data)
                    target_connection.commit()
                target_cursor.close()
                target_connection.close()
                load_end_time = datetime.now()
            load_time = (load_end_time - load_start_time).total_seconds() / 60

            task_status = 'Success'
            task_logger.debug('Table execution ended')

            task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')

            project_connection = connect_database(project_DB_details)
            task_update(project_connection, task_id, len(table_data_list), task_status, None, task_end_time,
                        extraction_time, transform_time, load_time)
    except Exception as e:
        task_status = 'Fail'
        task_error = str(e)

        task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        project_connection = connect_database(project_DB_details)
        task_update(project_connection, task_id, None, task_status, task_error, task_end_time, extraction_time,
                    transform_time,
                    load_time)
        ti.UP_FOR_RETRY


def e2e_migration(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    table_name = kwargs['table_name']
    target_schema = kwargs['target_schema']
    part_name = kwargs['task_name']
    token_data = kwargs['token_data']
    request_memory = kwargs['request_memory']
    limit_memory = kwargs['limit_memory']
    data_load_type = kwargs['data_load_type']
    file_name = kwargs['file_name']

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']
    attempt = ti.try_number

    extraction_time = None
    transform_time = None
    load_time = None

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)
    task_start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_id = task_insert(project_connection, part_name.lower(), 'Data_Migration',
                          attempt, None, None, request_memory, limit_memory, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    try:
        extra_path = os.environ['EXTRA_FOLDER']
        if target_connection_id in ['', None]:
            root_folder = extra_path + '/' + str(source_connection_id)
        else:
            root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

        log_path = root_folder + '/' + 'Data_Migration_Logs'
        if not os.path.exists(log_path):
            os.makedirs(log_path)
        task_logger = logging.getLogger(__name__)
        task_logger.setLevel(logging.DEBUG)
        log_handler = logging.FileHandler(log_path + '/' + part_name.lower() + '.log')
        log_handler.setFormatter(logging.Formatter("%(asctime)s [%(levelname)s] - %(message)s"))
        log_handler.addFilter(lambda record: record.levelno != logging.WARNING)
        task_logger.addHandler(log_handler)

        task_logger.debug('E2E Migration started')

        source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
        target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))

        mydumper_path = root_folder + '/' + 'Mydumper_Files'
        if not os.path.exists(mydumper_path):
            os.makedirs(mydumper_path)

        time_stamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S-%f")

        dump_folder = mydumper_path + '/' + f'E2E_Migration_{file_name}_{time_stamp}'
        command = f"""mydumper -h {source_DB_details['host']} -P {source_DB_details['port']} -u {source_DB_details['name']} -p '{source_DB_details['password']}' -B {source_DB_details['db_name']} -t 5 -o {dump_folder} --triggers --events --routines"""

        extraction_start_time = datetime.now()
        try:
            result = subprocess.run(command, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            definer_rm = f"sed -i 's/DEFINER=[^ ]*//' {dump_folder}/*.sql"
            print(definer_rm,
                  "To remove definer from all the .sql files because we are using 0.10 but skip definer available after 0.11")
            definer_rm_result = subprocess.run(definer_rm, shell=True, check=True, stdout=subprocess.PIPE,
                                               stderr=subprocess.PIPE)
        except subprocess.CalledProcessError as e:
            print(f"Command failed with return code {e.returncode}")
            print(f"Output: {e.output}")
            print(f"Error: {e.stderr}")
            ti.UP_FOR_RETRY

        extraction_end_time = datetime.now()
        extraction_time = (extraction_end_time - extraction_start_time).total_seconds() / 60

        task_logger.debug('Export completed and starting import to target database')

        load_command = f"""myloader -h {target_DB_details['host']} -P {target_DB_details['port']} -u {target_DB_details['name']} -p '{target_DB_details['password']}' -B {target_DB_details['db_name']} -t 5 -d {dump_folder} --overwrite-tables"""
        load_start_time = datetime.now()

        try:
            target_result = subprocess.run(load_command, shell=True, check=True, stdout=subprocess.PIPE,
                                           stderr=subprocess.PIPE)
        except subprocess.CalledProcessError as e:
            print(f"Command failed with return code {e.returncode}")
            print(f"Output: {e.output}")
            print(f"Error: {e.stderr}")
            ti.UP_FOR_RETRY
        load_end_time = datetime.now()
        load_time = (load_end_time - load_start_time).total_seconds() / 60

        task_status = 'Success'
        task_logger.debug('E2E Migration ended')

        metadata_file = dump_folder + '/' + 'metadata'
        position, position_file = '', ''
        if os.path.isfile(metadata_file):
            with open(metadata_file, 'r') as f:
                data = f.read()
                position_file = data.split('Log:')[1].split('Pos:')[0].strip()
                position = data.split('Pos:')[1].split('GTID:')[0].strip()

        position_file_folder = root_folder + '/' + 'Position_Files' + '/' + file_name.lower()
        if not os.path.exists(position_file_folder):
            os.makedirs(position_file_folder)
        with open(position_file_folder + '/' + 'position.txt', "w") as f:
            f.write(f"{position_file},{position}")

        config_id = dag_config['config_id']
        project_connection = connect_database(project_DB_details)
        transaction_insert(project_connection, config_id, position, position_file, 'Running')

        task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        task_update(project_connection, task_id, None, task_status, None, task_end_time,
                    extraction_time, None, load_time)

    except Exception as e:
        task_status = 'Fail'
        task_error = str(e)

        task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        project_connection = connect_database(project_DB_details)
        task_update(project_connection, task_id, None, task_status, task_error, task_end_time, extraction_time,
                    None,
                    load_time)
        ti.UP_FOR_RETRY


def post_validation_trigger(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    table_name = kwargs['table_name']
    token_data = kwargs['token_data']

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, 'post_validation_task', 'Post_Validation', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    validation_flag = False
    try:

        source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
        source_connection, error = DB_connection(source_DB_details)

        source_row_count_query = f"select count(*) from {schema_name.upper()}.{table_name.upper()}"
        source_row_count = execute_query(source_connection, source_row_count_query)[0][0]

        target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
        target_connection, error = target_DB_connection(target_DB_details)

        target_row_count_query = f"select count(*) from {target_schema.lower()}.{table_name.lower()}"
        target_row_count = execute_query(target_connection, target_row_count_query)[0][0]

        difference_count = abs(target_row_count - source_row_count)
        if difference_count != 0:
            validation_flag = True
            print(
                f"Row count not matching between source {source_row_count} and target {target_row_count}")

        if validation_flag:
            # insert into project db
            print(f"Post validation failed for {table_name}. Please check data migration ")

            task_end_time = datetime.now()
            task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

            dag_end_time = datetime.now()
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
        else:
            print(f"Post validation completed for {table_name}")

            task_end_time = datetime.now()
            task_update(project_connection, task_id, None, 'Success', None, task_end_time, None, None, None)
    except Exception as error:
        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        if ti.try_number == 3:
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
        ti.UP_FOR_RETRY
    return validation_flag


def get_constraints_list(connection, source_connection_id, schema_name, table_name):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_get_constraints_list(%s,%s,%s,%s);fetch all in "dataset";',
                       (source_connection_id, schema_name, table_name, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        print("Error at fetching constraints list: " + str(err))
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def deploy_table_constraints_trigger(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    target_schema = kwargs['target_schema']
    schema_name = kwargs['schema']
    table_name = kwargs['table_name']
    token_data = kwargs['token_data']

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, 'deploy_constraints_task', 'Deploy_Constraints', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]
    try:
        tables_data_list = get_constraints_list(project_connection, source_connection_id, schema_name, table_name)

        target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
        table_ddl_code_list = [i[0] for i in tables_data_list]
        print("length of table_ddl_code_list==", len(table_ddl_code_list))

        if len(table_ddl_code_list) > 0:
            target_connection, error = target_DB_connection(target_DB_details)
            for query in table_ddl_code_list:
                query = re.sub(rf'{schema_name}\.', target_schema + '.', query,
                               flags=re.IGNORECASE | re.DOTALL)
                cursor = target_connection.cursor()
                try:
                    cursor.execute(query)
                except mysql.connector.DatabaseError as e:
                    print(f"Issue found near deploying constraint '{query}': {str(e)}")
                finally:
                    cursor.close()
                    target_connection.commit()
        print("Deployed the constraints and indexes for the table: " + table_name)

        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Success', None, task_end_time, None, None, None)
    except Exception as error:
        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        if ti.try_number == 3:
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)

        ti.UP_FOR_RETRY


def complete_validation_trigger(ti, **kwargs):
    process_type = kwargs['process_type']
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    table_name = kwargs['table_name']
    file_name = kwargs['file_name']
    token_data = kwargs['token_data']
    dag_name = kwargs['dag_id']

    source_name = 'MySQL'
    target_name = 'MySQL'

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, 'complete_validation_task', 'Complete_Validation', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    validation_flag = False

    try:
        extra_path = os.environ['EXTRA_FOLDER']
        primary_key_result_df = foreign_key_result_df = check_constraint_result_df = unique_constraint_result_df = default_constraint_result_df = not_null_constraint_result_df = index_result_df = pd.DataFrame()

        if target_connection_id in ['', None]:
            root_folder = extra_path + '/' + str(source_connection_id)
        else:
            root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)
        if target_connection_id not in ['', None]:
            xml_path = extra_path + '/' + 'Validation_Queries' + '/validation_queries.xml'
            tree = ET.parse(xml_path)
            root = tree.getroot()
            objects_list = ['Primary_Key', 'Unique_Constraint', 'Foreign_Key', 'Not_Null_Constraint',
                            'Default_Constraint', 'Check_Constraint', 'Index']

            source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
            source_connection, error = DB_connection(source_DB_details)

            target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
            target_connection, error = target_DB_connection(target_DB_details)

            for object_type in objects_list:
                source_query_tag = 'Table_Validation_Queries/Source/' + '/' + object_type
                object_source_query = list(root.iterfind(source_query_tag))[0].text
                object_source_query = object_source_query.replace('@schemaname', schema_name.upper()).replace('@order',
                                                                                                              '').replace(
                    '@degree', str(source_DB_details['parallelprocess'])).replace('@tablename', table_name.upper())
                source_object_output = execute_query(source_connection, object_source_query)

                target_query_tag = 'Table_Validation_Queries/Target/' + '/' + object_type
                object_target_query = list(root.iterfind(target_query_tag))[0].text
                object_target_query = object_target_query.replace('@schemaname', target_schema.lower()).replace(
                    '@order',
                    '').replace(
                    '@degree', str(target_DB_details['parallelprocess'])).replace('@tablename', table_name.lower())
                target_object_output = execute_query(target_connection, object_target_query)

                if (object_type == 'Primary_Key') or (object_type == 'Foreign_Key') or (
                        object_type == 'Check_Constraint') or (
                        object_type == 'Unique_Constraint'):
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Schema_Name', 'Table_Name', object_type,
                                                           'Source_Constraint_Name',
                                                           'Source_' + object_type + '_Status'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Schema_Name', 'Table_Name', object_type,
                                                           'Target_Constraint_Name',
                                                           'Target_' + object_type + '_Status'])

                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                    matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                            suffixes=('_Source', '_Target'),
                                            how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                    source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                           suffixes=('_Source', '_Target'),
                                           how='left', indicator=True)
                    source_data = source_data[source_data['_merge'] == 'left_only'].drop(
                        columns=['Target_Constraint_Name', 'Target_' + object_type + '_Status', '_merge'])
                    source_data['Status'] = f"Available in {source_name} not in {target_name}"
                    source_data['Target_Constraint_Name'] = 'Nan'
                    source_data['Target_' + object_type + '_Status'] = 'Nan'

                    target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                           suffixes=('_Source', '_Target'),
                                           how='right', indicator=True)
                    target_data = target_data[target_data['_merge'] == 'right_only'].drop(
                        columns=['Source_Constraint_Name', 'Source_' + object_type + '_Status', '_merge'])
                    target_data['Status'] = f"Available in {target_name} not in {source_name}"
                    target_data['Source_Constraint_Name'] = 'Nan'
                    target_data['Source_' + object_type + '_Status'] = 'Nan'
                    result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                    result_df.rename(columns={'_merge': 'Status'}, inplace=True, errors='ignore')

                elif object_type == "Default_Constraint":
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Schema_Name', 'Table_Name', 'Constraint_Name',
                                                           'Source_Column_Name'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Schema_Name', 'Table_Name', 'Constraint_Name',
                                                           'Target_Column_Name'])
                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Constraint_Name'],
                                            suffixes=('_Source', '_Target'), how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"
                    source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Constraint_Name'],
                                           suffixes=('_Source', '_Target'), how='left', indicator=True)
                    source_data = source_data[source_data['_merge'] == 'left_only']
                    source_data['Status'] = f"Available in {source_name} not in {target_name}"
                    source_data['Target_Column_Name'] = 'Nan'
                    target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Constraint_Name'],
                                           suffixes=('_Source', '_Target'), how='right', indicator=True)
                    target_data = target_data[target_data['_merge'] == 'right_only']
                    target_data['Status'] = f"Available in {target_name} not in {source_name}"
                    target_data['Source_Column_Name'] = 'Nan'
                    result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                    result_df = result_df.drop(columns=['_merge'], errors='ignore')

                elif object_type == "Not_Null_Constraint":
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Schema_name', 'Constraint_Name', 'Constraint_Value'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Schema_name', 'Constraint_Name', 'Constraint_Value'])
                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    matched_data = pd.merge(source_data_df, target_data_df, on=['Constraint_Name', 'Constraint_Value'],
                                            suffixes=('_Source', '_Target'), how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"
                    source_data = pd.merge(source_data_df, target_data_df, on=['Constraint_Name', 'Constraint_Value'],
                                           suffixes=('_Source', '_Target'), how='left', indicator=True)
                    source_data = source_data[source_data['_merge'] == 'left_only'].drop(columns=['Schema_name_Target',
                                                                                                  '_merge'])
                    source_data['Status'] = f"Available in {source_name} not in {target_name}"
                    target_data = pd.merge(source_data_df, target_data_df, on=['Constraint_Name', 'Constraint_Value'],
                                           suffixes=('_Source', '_Target'), how='right', indicator=True)
                    target_data = target_data[target_data['_merge'] == 'right_only'].drop(
                        columns=['Schema_name_Source', '_merge'])
                    target_data['Status'] = f"Available in {target_name} not in {source_name}"
                    result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)

                elif object_type == "Index":
                    source_data_df = pd.DataFrame(source_object_output)
                    if len(source_data_df):
                        source_data_df = source_data_df.loc[:, [0, 1, 2, 3, 6]]
                        source_data_df.columns = ['Schema_Name', 'Table_Name', 'Index_Name', 'Source_Index_Column',
                                                  'Source_Index_DDL']
                    else:
                        source_data_df = pd.DataFrame(
                            columns=['Schema_Name', 'Table_Name', 'Index_Name', 'Source_Index_Column',
                                     'Source_Index_DDL'])

                    target_data_df = pd.DataFrame(target_object_output)
                    if len(target_data_df):
                        target_data_df = target_data_df.loc[:, [0, 1, 2, 3, 6]]
                        target_data_df.columns = ['Schema_Name', 'Table_Name', 'Index_Name', 'Target_Index_Column',
                                                  'Target_Index_DDL']
                    else:
                        target_data_df = pd.DataFrame(
                            columns=['Schema_Name', 'Table_Name', 'Index_Name', 'Target_Index_Column',
                                     'Target_Index_DDL'])

                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df['Target_Index_Column'] = target_data_df['Target_Index_Column'].str.replace("['", '',
                                                                                                              regex=False).replace(
                        "']", '', regex=False).replace('["', '', regex=False).replace('"]', '', regex=False)
                    target_data_df['Index_Name'] = target_data_df['Index_Name'].str.replace('___idx1$', '', case=False,
                                                                                            regex=True)

                    matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Index_Name'],
                                            suffixes=('_Source', '_Target'), how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                    source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Index_Name'],
                                           suffixes=('_Source', '_Target'), how='left', indicator=True)
                    source_data = source_data[source_data['_merge'] == 'left_only'].drop(
                        columns=['Target_Index_Column', 'Target_Index_DDL', '_merge'])
                    source_data['Status'] = f"Available in {source_name} not in {target_name}"
                    source_data['Target_Index_Column'] = 'Nan'
                    source_data['Target_Index_DDL'] = 'Nan'

                    target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Index_Name'],
                                           suffixes=('_Source', '_Target'), how='right', indicator=True)
                    target_data = target_data[target_data['_merge'] == 'right_only'].drop(
                        columns=['Source_Index_Column', 'Source_Index_DDL', '_merge'])
                    target_data['Status'] = f"Available in {target_name} not in {source_name}"
                    target_data['Source_Index_Column'] = 'Nan'
                    target_data['Source_Index_DDL'] = 'Nan'

                    result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                    result_df = result_df.drop(columns=['_merge'], errors='ignore')
                else:
                    result_df = source_data = target_data = pd.DataFrame()

                if not result_df.empty and object_type == 'Primary_Key':
                    primary_key_result_df = result_df
                elif not result_df.empty and object_type == 'Foreign_Key':
                    foreign_key_result_df = result_df
                elif not result_df.empty and object_type == 'Check_Constraint':
                    check_constraint_result_df = result_df
                elif not result_df.empty and object_type == 'Unique_Constraint':
                    unique_constraint_result_df = result_df
                elif not result_df.empty and object_type == 'Default_Constraint':
                    default_constraint_result_df = result_df
                elif not result_df.empty and object_type == 'Not_Null_Constraint':
                    not_null_constraint_result_df = result_df
                elif not result_df.empty and object_type == 'Index':
                    index_result_df = result_df

                if not source_data.empty or not target_data.empty:
                    validation_flag = True
                    print(
                        f"{object_type}s not matching between source and target for {table_name}, please check the report for detailed information")
        if validation_flag:
            complete_validation_path = root_folder + '/' + 'Dag_Validation_Reports' + '/' + file_name.lower() + '/' + dag_name
            if not os.path.exists(complete_validation_path):
                os.makedirs(complete_validation_path)

            with pd.ExcelWriter(complete_validation_path + '/' + 'complete_validation.xlsx') as writer:
                primary_key_result_df.to_excel(writer, sheet_name='Primary_Key', index=False)
                foreign_key_result_df.to_excel(writer, sheet_name='Foreign_Key', index=False)
                check_constraint_result_df.to_excel(writer, sheet_name='Check_Constraint', index=False)
                unique_constraint_result_df.to_excel(writer, sheet_name='Unique_Constraint', index=False)
                default_constraint_result_df.to_excel(writer, sheet_name='Default_Constraint', index=False)
                not_null_constraint_result_df.to_excel(writer, sheet_name='Not_Null_Constraint', index=False)
                index_result_df.to_excel(writer, sheet_name='Index', index=False)
            print(f"Complete validation failed for {table_name}. Please check report for detailed information")
            task_end_time = datetime.now()
            task_update(project_connection, task_id, None, 'Fail', 'Validation Flag True', task_end_time, None, None,
                        None)

            dag_end_time = datetime.now()
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
        else:
            print(f"Complete validation completed for {table_name}")
            task_end_time = datetime.now()
            task_update(project_connection, task_id, None, 'Success', None, task_end_time, None, None, None)

            if process_type == 'Initial_Data_Load':
                dag_end_time = datetime.now()
                dag_update(project_connection, dag_id, 'Success', dag_end_time)
    except Exception as error:
        print('entered exception')
        print(f"Complete validation failed for {table_name}. Please check report for detailed information")
        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        if ti.try_number == 3:
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
        ti.UP_FOR_RETRY
    return validation_flag


def open_new_csv_file(file_name):
    print(f'Creating CSV file {file_name}')
    csv_file = open(file_name, mode='w', newline='')
    writer = csv.writer(csv_file)
    writer.writerow(['Statement', 'Log_File', 'Position'])
    csv_file.flush()
    return csv_file, writer

def cdc_data_insert(connection, batch_number, batch_length, batch_size, load_type, data_file_name, batch_start_time,
                    batch_extraction_time, batch_transform_time, batch_load_time, batch_end_time, task_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_cdc_data_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (batch_number, batch_length, batch_size, load_type, data_file_name, batch_start_time, batch_extraction_time,
             batch_transform_time, batch_load_time, batch_end_time, task_id, 'dataset'))
        data = cursor.fetchall()
    except Exception as error:
        data = None
        print(f"Error occurred at cdc data insert: {str(error)}")
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def cdc_errors_insert(connection, batch_error_list):
    cdc_error_insert_query = "insert into public.audit_cdc_error_tracking (transaction, transaction_error, transaction_error_time, data_id) values (%s,%s,%s,%s,%s)"

    cursor = connection.cursor()
    try:
        cursor.executemany(cdc_error_insert_query, batch_error_list)
    except Exception as error:
        print(f"Error occurred at cdc data insert: {str(error)}")
        pass
    finally:
        connection.commit()
        cursor.close()

def cdc_trigger(ti, **kwargs):
    process_type = kwargs['process_type']
    dag_name = kwargs['dag_id']
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    table_name = kwargs['table_name']
    file_name = kwargs['file_name']
    cdc_load_type = kwargs['cdc_load_type']
    token_data = kwargs['token_data']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
    source_connection, error = DB_connection(source_DB_details)

    config_data = get_config_id(project_connection, file_name, process_type)
    config_id = config_data[0][0]

    target_DB_details = {}
    extra_path = os.environ['EXTRA_FOLDER']
    if target_connection_id in ['', None]:
        root_folder = extra_path + '/' + str(source_connection_id)
    else:
        root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

        target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))

    position_file_folder = root_folder + '/' + 'Position_Files' + '/' + file_name.lower()
    if not os.path.exists(position_file_folder):
        os.makedirs(position_file_folder)
    file_path = position_file_folder + 'position.txt'

    position_file, position = '', ''
    if config_data[0][1] in ['', None]:
        position_fetch_query = "SHOW MASTER STATUS;;"
        position_data = execute_query(source_connection, position_fetch_query)
        position_file, position = position_data[0][0], position_data[0][1]

        with open(position_file_folder + '/' + 'position.txt', "w") as f:
            f.write(f"{position_file},{position}")

        transaction_insert(project_connection, config_id, position, position_file, 'Running')
    else:
        with open(file_path, 'r') as f:
            position_data = f.read().strip().split(',')
            print('Position Data from the File ', position_data)
            if len(position_data) == 2:
                position_file, position = position_data[0], int(position_data[1]) if position_data[1] not in ['', None] else \
                position_data[1]
    print(f'position_file: {position_file}, position: {position}')

    dag_id = dag_insert(project_connection, dag_name, 'CDC', schema_name, target_schema, table_name,
                        None,
                        None, None, None,
                        'Running', config_id)
    dag_id = dag_id[0][0]

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, 'cdc_' + str(file_name), 'CDC', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    cdc_data_folder = root_folder + '/' + 'CDC_Data_Files' + '/' + file_name.lower()
    if not os.path.exists(cdc_data_folder):
        os.makedirs(cdc_data_folder)

    batch_number = 1

    current_file_name, file_limit_size, csv_file = None, '', ''
    if cdc_load_type == 'File':
        file_limit_size = 1 * 1024 * 1024
        start_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S-%f")
        current_file_name = cdc_data_folder + '/' + f"CDC_{file_name.lower()}_{start_time}_batch_{batch_number}.csv"
        csv_file, writer = open_new_csv_file(current_file_name)

    try:
        while True:
            source_cursor = source_connection.cursor()
            source_cursor.execute(f"SHOW BINLOG EVENTS IN '{position_file}' FROM {position};")

            extract_start_time = datetime.now()
            batch_rows = source_cursor.fetchall()
            extract_end_time = datetime.now()
            extract_time = (extract_end_time - extract_start_time).total_seconds() / 60
            print('Length of Rows', len(batch_rows))

            if batch_rows:
                batch_start_time = datetime.now()
                transform_start_time = datetime.now()
                df = pd.DataFrame(batch_rows, columns=['Log_name', 'Pos', 'Event_type', 'Server_id', 'End_log_pos', 'Info'])
                if process_type == 'Data_Dumper':
                    dml_df = df[df['Event_type'] == 'Annotate_rows'].copy()
                elif table_name != 'Schema' and table_name != 'Database':
                    table_list = [i for i in table_name.split(',') if i != '']

                    dml_df = df[
                        (df['Event_type'] == 'Annotate_rows') &
                        (df['Info'].str.contains(f"`{schema_name}`\.", case=False)) &
                        (df['Info'].str.contains('|'.join([f"`{tbl}`" for tbl in table_list]), case=False))
                        ].copy()
                xid_df = df[df['Event_type'] == 'Xid']

                if not dml_df.empty:
                    dml_df['Cleaned_Info'] = dml_df['Info'].str.replace(
                        f'{source_DB_details["db_name"]}.',
                        f'{target_DB_details["db_name"]}.',
                        regex=False
                    )
                    transform_end_time = datetime.now()
                    transform_time = (transform_end_time - transform_start_time).total_seconds() / 60

                    batch_error_list, load_time, cdc_data_file = [], None, None
                    if cdc_load_type == 'File':
                        dml_df.to_csv(csv_file, header=False, index=False)
                        csv_file.flush()
                        file_size = os.path.getsize(current_file_name)
                        cdc_data_file = current_file_name

                        if file_size >= file_limit_size:
                            csv_file.close()
                            end_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S-%f")
                            renamed_file_name = current_file_name.replace('.csv', f'_{end_time}.csv')

                            os.rename(current_file_name, renamed_file_name)
                            print(f"File renamed to: {renamed_file_name}")

                            start_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S-%f")
                            current_file_name = cdc_data_folder + '/' + f"CDC_{file_name.lower()}_{start_time}_batch_{batch_number}.csv"
                            csv_file, writer = open_new_csv_file(current_file_name)

                        if not xid_df.empty:
                            position = xid_df['End_log_pos'].max()
                            with open(file_path, "w") as f:
                                f.write(f"{position_file},{position}")
                            print(f"Commit detected. Updated position to: {position}")

                    elif cdc_load_type == 'Database':
                        load_start_time = datetime.now()
                        target_connection, error = target_DB_connection(target_DB_details)
                        target_cursor = target_connection.cursor()
                        try:
                            dml_df['Cleaned_Info'].apply(target_cursor.execute)

                            if not xid_df.empty:
                                position = xid_df['End_log_pos'].max()
                                with open(file_path, "w") as f:
                                    f.write(f"{position_file},{position}")
                                print(f"Commit detected. Updated position to: {position}")
                        except Exception as error:
                            for index, record in dml_df[['Cleaned_Info','Log_name','Pos']].iterrows():
                                target_cursor = target_connection.cursor()
                                try:
                                    target_cursor.execute(record[0])
                                    with open(file_path, "w") as f:
                                        f.write(f"{record[1]},{record[2]}")
                                    print(f"Commit detected. Updated position to: {record[2]}")
                                except Exception as e:
                                    print("Error occurred at statement: " + str(
                                        record[0]) + " loading to mysql: " + str(
                                        error))
                                    error_tuple = (tuple(record.values), str(error), datetime.now())
                                    batch_error_list.append(error_tuple)
                        finally:
                            target_connection.commit()
                            target_cursor.close()

                        load_end_time = datetime.now()
                        load_time = (load_end_time - load_start_time).total_seconds() / 60

                    batch_end_time = datetime.now()
                    batch_number = batch_number + 1

                    batch_length = len(dml_df['Cleaned_Info'].values.tolist())
                    batch_size = sum(sys.getsizeof(statement) for statement in dml_df['Cleaned_Info'].values.tolist())

                    project_connection = connect_database(project_DB_details)
                    cdc_data_id = cdc_data_insert(project_connection, batch_number, batch_length,
                                                  batch_size, cdc_load_type,
                                                  cdc_data_file, batch_start_time, extract_time,
                                                  transform_time, load_time,
                                                  batch_end_time, task_id)

                    if batch_error_list:
                        batch_error_list = [t + (cdc_data_id,) for t in batch_error_list]
                        cdc_errors_insert(project_connection, batch_error_list)

                else:
                    if 'Rotate' in df['Event_type'].values.tolist():
                        rotate_info = df[df['Event_type'] == 'Rotate']['Info'].iloc[0]
                        current_log_file = str(rotate_info).split(';')[0].strip()
                        print(f"Switching to new log file: {current_log_file}")
                        position_file = current_log_file
                        position = 0
                        with open(file_path, "w") as f:
                            f.write(f"{position_file},{position}")
                    else:
                        position = df['End_log_pos'].iloc[-1]
    except Exception as error:
        print(f"CDC failed for {file_name}: {error}")
        with open(file_path, "w") as f:
            f.write(f"{position_file},{position}")
        print(f"Error detected. Process stopped. Current state saved: log file = {position_file}, position = {position}")

        project_connection = connect_database(project_DB_details)

        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        if ti.try_number == 3:
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
        ti.UP_FOR_RETRY


def load_files_tracking_insert(connection, data_file_name, start_time, load_time, end_time, task_id):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_audit_load_files_tracking_insert(%s,%s,%s,%,%s);fetch all in "dataset";',
                       (data_file_name, start_time, load_time, end_time, task_id, 'dataset'))
        data = cursor.fetchall()
    except Exception as error:
        data = None
        print(f"Error occurred at cdc data insert: {str(error)}")
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def load_files_errors_insert(connection, batch_error_list):
    cdc_error_insert_query = "insert into public.audit_load_files_error_tracking (transaction, transaction_error, transaction_error_time, load_file_id) values (%s,%s,%s,%s,%s)"

    cursor = connection.cursor()
    try:
        cursor.executemany(cdc_error_insert_query, batch_error_list)
    except Exception as error:
        print(f"Error occurred at cdc data insert: {str(error)}")
        pass
    finally:
        connection.commit()
        cursor.close()


def load_files_trigger(ti, **kwargs):
    dag_name = kwargs['dag_id']
    process_type = kwargs['process_type']
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    file_name = kwargs['file_name']
    token_data = kwargs['token_data']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    config_data = get_config_id(project_connection, file_name, process_type)
    config_id = config_data[0][0]

    dag_id = dag_insert(project_connection, dag_name, 'Load_Files', None, None, None,
                        None, None, None, None,
                        'Running', config_id)
    dag_id = dag_id[0][0]

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, 'load_files_' + str(file_name), 'Load_Files', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    extra_path = os.environ['EXTRA_FOLDER']

    root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)
    target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))

    archive_folder = root_folder + '/' + 'Archived_CDC_Files' + '/' + file_name.lower()
    if not os.path.exists(archive_folder):
        os.makedirs(archive_folder)

    cdc_data_folder = root_folder + '/' + 'CDC_Data_Files' + '/' + file_name.lower()
    load_files_tracker = cdc_data_folder + '/' + 'load_files_tracker.txt'

    processing_file_name, processing_index = '', ''
    if os.path.isfile(load_files_tracker):
        with open(load_files_tracker, 'r') as f:
            index_data = f.read().strip().split(',')
            print('Position Data from the File ', index_data)
            if len(index_data) == 2:
                processing_file_name, processing_index = index_data[0], int(index_data[1])
    try:

        batch_size = 1000
        while True:
            cdc_data_files = [cdc_data_folder + '/' + file for file in os.listdir(cdc_data_folder) if
                              file.endswith('.csv')]

            for data_file in cdc_data_files:
                batch_split_list = data_file.split('batch_')[1].split('_')
                if len(batch_split_list) > 1:
                    if processing_file_name == data_file.split('/')[-1] and processing_index != '':
                        if processing_index == '-1':
                            shutil.move(data_file, archive_folder + '/' + data_file.split('/')[-1])
                            continue
                        else:
                            start_index = int(processing_index) + 1
                    else:
                        start_index = 0
                    batch_error_list, load_end_time = [], None
                    data = pd.read_csv(data_file)
                    load_start_time = datetime.now()
                    if len([i for i in range(start_index, len(data))]):
                        for i in range(start_index, len(data), batch_size):
                            batch_data = data.iloc[i:i + batch_size]
                            if batch_data:
                                target_connection, error = target_DB_connection(target_DB_details)
                                target_cursor = target_connection.cursor()
                                try:
                                    batch_statement = '\n'.join(batch_data['Statement'].values.tolist())
                                    target_cursor.execute(batch_statement)
                                    target_connection.commit()

                                    with open(load_files_tracker, "w") as f:
                                        f.write(f"{data_file.split('/')[-1]}, {i + batch_size}")
                                    print(f'Data Loaded into Target for batch {i}:{i + batch_size}')

                                except Exception as batch_error:
                                    for index, record in batch_data.iterrows():
                                        statement = record['Statement']
                                        try:
                                            target_cursor.execute(statement)
                                            target_connection.commit()
                                        except Exception as bi_error:
                                            print("Error occurred at statement: " + str(
                                                record[0]) + " loading to target: " + str(error))

                                            error_tuple = (
                                            data_file, tuple(record.values), str(bi_error), datetime.now())
                                            batch_error_list.append(error_tuple)

                                            error_tuple = (
                                                config_id, int(data_file.split('batch_')[1].split('_')[0]),
                                                tuple(record.values), str(bi_error), datetime.now())
                                            batch_error_list.append(error_tuple)

                                        with open(load_files_tracker, "w") as f:
                                            f.write(f"{data_file.split('/')[-1]},{index}")
                            else:
                                with open(load_files_tracker, "w") as f:
                                    f.write(f"{data_file.split('/')[-1]},-1")
                        load_end_time = datetime.now()
                    else:
                        with open(load_files_tracker, "w") as f:
                            f.write(f"{data_file.split('/')[-1]},-1")

                    destination = archive_folder + '/' + data_file.split('/')[-1]
                    shutil.move(data_file, destination)
                    print(f"{data_file.split('/')[-1]} is being archived as it is processed")

                    load_time = (load_end_time - load_start_time).total_seconds() / 60

                    load_file_id = load_files_tracking_insert(project_connection, data_file, load_start_time, load_time,
                                                              load_end_time, task_id)

                    if batch_error_list:
                        batch_error_list = [t + (load_file_id,) for t in batch_error_list]
                        load_files_errors_insert(project_connection, batch_error_list)
                else:
                    print('File is not ready to process as it is still in extraction phase')
    except Exception as error:
        print(f"Load files task failed for {file_name}")
        project_connection = connect_database(project_DB_details)

        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        if ti.try_number == 3:
            dag_update(project_connection, dag_id, 'Fail', dag_end_time)
        ti.UP_FOR_RETRY
