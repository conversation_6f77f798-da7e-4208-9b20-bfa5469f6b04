import os, sys, time, shutil, re
from import_file import import_file
import pandas as pd
from common_modules.api import api_authentication, decrypt_database_details
from common_modules.stored_procedures import insert_config_name, get_failed_tasks


def copy_dag_files(file_name, config_files_path, local_migration_path,dag_path, root_path, dag_execute_type):
    source_file = local_migration_path + '/' + 'Dag_Files' + '/' + 'dag_file.py'
    target_file = dag_path + '/' + f'dag_file_{file_name}.py'

    with open(source_file, 'r') as file:
        file_content = file.read()
        file_replaced_content = re.sub('@Config_File_Path',
                                       f'{config_files_path}/Data_Migration/{file_name}.xlsx',
                                       file_content)
        if dag_execute_type == 'Manual':
            file_replaced_content = file_replaced_content.replace('@schedule_interval', 'None').replace("'@pause_flag'",
                                                                                                        'True')
        else:
            file_replaced_content = file_replaced_content.replace('@schedule_interval', '@once').replace("'@pause_flag'",
                                                                                                        'False')
    with open(target_file, 'w') as f:
        f.write(file_replaced_content)

    dag_triggers_file = local_migration_path + '/' + 'Dag_Files' + '/' + 'dag_triggers.py'
    trigger_destination_file = dag_path + '/' + 'dag_triggers.py'
    with open(dag_triggers_file, 'rb') as f:
        file_content = f.read()
    with open(trigger_destination_file, 'wb') as f:
        f.write(file_content)

    if not os.path.exists(root_path + '/Validation_Queries/'):
        os.makedirs(root_path + '/Validation_Queries/')

    xml_files = local_migration_path + '/' + 'validation_queries.xml'
    xml_destination_file = root_path + '/' + 'Validation_Queries' + '/' + 'validation_queries.xml'
    shutil.copyfile(xml_files, xml_destination_file)


def catchup_trigger(task_name, project_id, migration_name, source_connection_id, target_connection_id, parent_file,
                    dag_execute_type, file_name, cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')
        dag_path = getattr(import_object, 'Dag_Path')

        working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')
        local_migration_path = local_root_path + '/' + 'Migrations' + '/' + migration_name

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)

            token_data = api_authentication()

            project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
            project_function_call = getattr(import_object, 'connect_database')

            if target_connection_id in ['', None]:
                config_files_path = root_path + '/' + str(source_connection_id) + '/' + 'Config_Files'
            else:
                config_files_path = root_path + '/' + str(source_connection_id) + '/' + str(
                    target_connection_id) + '/' + 'Config_Files'

            if not os.path.exists(working_directory_path):
                os.makedirs(working_directory_path)

            config_path = config_files_path + '/' + 'Data_Migration' + '/' + 'inc_' + str(
                file_name).lower().strip() + '.xlsx'
            temp_config_path = working_directory_path + '/' + 'inc_' + str(file_name).lower().strip() + '.xlsx'
            excel_file_name = 'inc_' + str(file_name).lower().strip()

            parent_task = ''
            if parent_file.startswith('init_'):
                parent_task = 'Initial_Data_Load'
            elif parent_file.startswith('e2e_'):
                parent_task = 'E2E_Data_Load'

            with pd.ExcelWriter(temp_config_path, engine="xlsxwriter") as writer:

                parent_config_df = pd.read_excel(config_files_path + '/' + parent_file + '.xlsx', sheet_name='Configuration')

                parent_config_df = parent_config_df.append({'Parameter': 'Parent_File_Name', 'Value': parent_file},
                                                           ignore_index=True)
                parent_config_df.loc[parent_config_df["Parameter"] == "File_Name", "Value"] = excel_file_name
                parent_config_df.fillna('', inplace=True)
                parent_config_df.to_excel(writer, sheet_name='Configuration', index=False)

                parent_tasks_df = pd.read_excel(config_files_path + '/' + parent_file + '.xlsx', sheet_name='Dags')
                parent_dag_names_list = parent_tasks_df['Dag Name'].values.to_list()

                dag_tasks_list = []
                if parent_dag_names_list:
                    project_connection = project_function_call(project_DB_details)
                    for dag_name in parent_dag_names_list:
                        failed_tasks = get_failed_tasks(project_connection, parent_file, parent_task, dag_name)
                        failed_parts = [tuple[1] for tuple in failed_tasks]

                        failed_dag_tasks_list = parent_tasks_df[(parent_tasks_df['Dag Name'] == dag_name) & (
                            parent_tasks_df['Part_Name'].isin(failed_parts))].values.tolist()
                        dag_tasks_list.extend(failed_dag_tasks_list)

                if dag_tasks_list:
                    tasks_df = pd.DataFrame(dag_tasks_list,
                                            columns=['Schema_Name', 'Table_Name', 'Table_Category', 'Dag Name',
                                                     'Part_Name',
                                                     'Row_Count', 'Chunk_Size', 'Lower_Bound', 'Upper_Bound'])
                    tasks_df.fillna('', inplace=True)
                    tasks_df.to_excel(writer, sheet_name='Dags', index=False)

                    dag_names_list = tasks_df['Dag Name'].values.to_list()

                if dag_names_list:
                    priority_list = []
                    for dag_name in dag_names_list:
                        epoch_time = time.time()
                        priority_tuple = (dag_name, int(epoch_time))
                        priority_list.append(priority_tuple)
                        time.sleep(1)
                    priority_df = pd.DataFrame(priority_list, columns=['Task', 'Priority_Weight'])
                    priority_df.to_excel(writer, sheet_name="Priority_Weights", index=False)

            shutil.copyfile(temp_config_path, config_path)

            project_connection = project_function_call(project_DB_details)
            insert_config_name(project_connection, task_name, excel_file_name, None, None)
            copy_dag_files(excel_file_name, config_files_path, local_migration_path,dag_path, root_path, dag_execute_type)