<Queries>
    <Validation_Queries>
        <Source>
            <Storage>
                <Sequence>
                    select /*+ PARALLEL(@degree) */ distinct schemaname as schema,
                    sequencename as sequence,
                    last_value - 1 as sequence_value
                    from pg_sequences where schemaname =lower('@schemaname')
                </Sequence>
                <Table>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name ,table_type FROM information_schema.tables
                    WHERE table_schema = lower('@schemaname') AND table_type = 'BASE TABLE' AND
                    table_schema||'.'||table_name
                    NOT IN (
                    SELECT inhrelid::regclass::text
                    FROM pg_inherits
                    )
                </Table>
                <Partition>
                    select /*+ PARALLEL(@degree) */ nmsp_parent.nspname as schemaname,parent.relname tablename,child.relname AS partition_table FROM pg_inherits
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_namespace nmsp_parent ON nmsp_parent.oid = parent.relnamespace
                    JOIN pg_namespace nmsp_child ON nmsp_child.oid = child.relnamespace
                    WHERE nmsp_parent.nspname = lower('@schemaname')
                    ORDER BY child.relname
                </Partition>
                <Primary_Key>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name,constraint_name,column_list,'ENABLED' as
                    status FROM (
                    SELECT /*+ PARALLEL(@degree) */
                    tc.table_schema,
                    tc.table_name,
                    tc.constraint_name,
                    string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list,'ENABLED'
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                    AND tc.table_name = kcu.table_name
                    WHERE upper(tc.constraint_type) = 'PRIMARY KEY'
                    AND tc.table_schema = lower('@schemaname')
                    GROUP BY tc.table_schema, tc.table_name, tc.constraint_name
                    ) AS primary_keys
                    WHERE (table_schema || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Primary_Key>
                <Unique_Constraint>
                    select /*+ PARALLEL(@degree) */ table_schema ,table_name ,constraint_name,column_list,'ENABLED' as
                    status FROM (
                    SELECT /*+ PARALLEL(@degree) */ tc.table_schema,
                    tc.table_name,
                    tc.constraint_name,
                    string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list,'ENABLED'
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                    AND tc.table_name = kcu.table_name
                    WHERE upper(tc.constraint_type) = 'UNIQUE'
                    AND tc.table_schema = lower('@schemaname')
                    GROUP BY tc.table_schema, tc.table_name, tc.constraint_name
                    ) AS unique_constraints
                    WHERE (table_schema || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Unique_Constraint>
                <Foreign_Key>
                    with cte as (
                    SELECT /*+ PARALLEL(@degree) */ conname AS constraint_name, conrelid::regclass AS table_name,
                    a.attname AS column_name,
                    confrelid::regclass AS referenced_table, fa.attname AS referenced_column
                    FROM pg_constraint AS c
                    JOIN pg_attribute AS a ON a.attnum = ANY(c.conkey) AND a.attrelid = c.conrelid
                    JOIN pg_attribute AS fa ON fa.attnum = ANY(c.confkey) AND fa.attrelid = c.confrelid)

                    select /*+ PARALLEL(@degree) */ split_part(table_name::regclass::varchar,'.',1)
                    schema_name,split_part(table_name::regclass::varchar,'.',2) table_name, constraint_name,'FOREIGN
                    KEY'||' (' ||column_name ||') ' ||'REFERENCES'
                    ||' '||referenced_table||' (' || referenced_column||');','ENABLED'
                    from cte
                    where split_part(table_name::regclass::varchar,'.',1) = lower('@schemaname')
                    and table_name::regclass::varchar not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname )
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema ,table_name ,column_name
                    FROM information_schema.columns
                    WHERE upper(is_nullable) = 'NO'
                    AND table_schema = lower('@schemaname')
                    and table_schema||'.'||table_name not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                    ORDER BY table_schema, table_name, column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name,column_name,column_default
                    FROM information_schema.columns
                    WHERE table_schema = lower('@schemaname')
                    and column_default IS NOT NULL
                    and table_schema||'.'||table_name not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Default_Constraint>
                <Check_Constraint>
                    with cte as(select /*+ PARALLEL(@degree) */
                    n.nspname AS schema_name,
                    conname AS constraint_name,
                    conrelid::regclass AS table_name,
                    pg_get_constraintdef(c.oid) AS check_expression,'ENABLED'
                    FROM pg_constraint c
                    JOIN pg_namespace n ON n.oid = c.connamespace
                    WHERE confrelid = 0 AND contype = 'c'
                    and n.nspname = lower('@schemaname')
                    )
                    select /*+ PARALLEL(@degree) */ split_part(table_name::text,'.',1) as
                    schema_name,split_part(table_name::text,'.',2) as
                    table_name,constraint_name,check_expression,'ENABLED' as status from cte where
                    schema_name ||'.'|| table_name::text not in (SELECT (cn.nspname || '.' || child.relname) AS
                    Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Check_Constraint>
                <Index>
                    SELECT /*+ PARALLEL(@degree) */
                    schema_name,
                    CASE WHEN table_name IS NULL OR table_name = '' THEN idx_schema_name ELSE table_name END AS
                    table_name,
                    index_name,
                    array_to_string(index_columns,',')AS index_columns,
                    pkey AS contraint_type,
                    table_name || '-' || index_name AS postgresconcat,
                    index_definition

                    FROM (
                    SELECT /*+ PARALLEL(@degree) */
                    ns.nspname AS schema_name,
                    split_part(idx.indrelid::REGCLASS::text, '.', 1) AS idx_schema_name,
                    split_part(idx.indrelid::REGCLASS::text, '.', 2) AS table_name,
                    ARRAY(SELECT pg_get_indexdef(idx.indexrelid, k + 1, TRUE)
                    FROM generate_subscripts(idx.indkey, 1) AS k
                    ORDER BY k) AS index_columns,
                    CASE
                    WHEN idx.indisprimary = true THEN 'Primary Key'
                    ELSE 'Non Primary Key'
                    END AS pkey,
                    pg_get_indexdef(idx.indexrelid::regclass) AS index_definition,
                    i.relname AS index_name,
                    idx.indisunique AS is_unique,
                    idx.indisprimary AS is_primary,
                    am.amname AS index_type,
                    (idx.indexprs IS NOT NULL) OR (idx.indkey::int[] @> array[0]) AS is_functional
                    FROM pg_index AS idx
                    JOIN pg_class AS i ON i.oid = idx.indexrelid
                    JOIN pg_am AS am ON i.relam = am.oid
                    JOIN pg_namespace AS ns ON i.relnamespace = ns.oid
                    JOIN pg_user AS u ON i.relowner = u.usesysid
                    LEFT JOIN pg_constraint AS con ON con.conindid = idx.indexrelid
                    WHERE NOT nspname LIKE 'pg%'
                    AND nspname = LOWER('@schemaname')
                    AND idx.indisprimary = false
                    AND (con.contype IS NULL OR con.contype != 'u')
                    ) ind_details
                    WHERE (schema_name || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname
                    )
                    ORDER BY 1, 2, 3
                </Index>
                <Synonym>
                    SELECT /*+ PARALLEL(@degree) */ viewname
                    FROM pg_views
                    WHERE schemaname = lower('@schemaname')
                    order by viewname
                </Synonym>
                <View>
                    SELECT /*+ PARALLEL(@degree) */ schemaname,viewname
                    FROM pg_views
                    WHERE schemaname = lower('@schemaname')
                    order by viewname
                </View>
                <Datatype>
                    SELECT /*+ PARALLEL(@degree) */
                    isc.table_schema AS "OWNER",
                    isc.table_name AS "TABLE_NAME",
                    isc.column_name AS "COLUMN_NAME",
                    isc.data_type AS "DATA_TYPE",
                    CASE
                    WHEN isc.data_type = 'integer' THEN '(' || numeric_precision || ',' || isc.numeric_scale || ')'
                    WHEN isc.data_type IN ('double precision', 'bigint') THEN '(' || numeric_precision || ')'
                    WHEN isc.data_type IN ('character varying', 'numeric', 'character') THEN '(' ||
                    character_maximum_length || ')'
                    WHEN isc.data_type = 'numeric'
                    AND isc.numeric_precision IS NULL THEN '[Default]'
                    WHEN isc.data_type IN ('date', 'timestamp without time zone', 'text', 'timestamp with time zone',
                    'ARRAY', 'USER-DEFINED', 'bytea', 'xml', 'uuid', 'oid' ) THEN '(' || ')'
                    END AS "COLUMN_SIZE",
                    isc.ordinal_position,
                    ist.table_type
                    FROM
                    information_schema.tables ist,
                    information_schema.columns isc
                    WHERE
                    ist.table_schema = isc.table_schema
                    AND ist.table_name = isc.table_name
                    AND ist.table_schema IN (lower('@schemaname'))
                    ORDER BY
                    isc.table_schema,
                    isc.table_name,
                    isc.ordinal_position
                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    Select /*+ PARALLEL(@degree) */ res.* from (select /*+ PARALLEL(@degree) */ routine_type,
                    routine_name as Code_Object_Name from information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    except
                    select /*+ PARALLEL(@degree) */ routine_type, routine_name as Code_Object_Name from
                    information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    and lower(data_type) =lower( 'trigger') ) res order by res.Code_Object_Name
                </Code_Objects>
                <Trigger>
                    SELECT /*+ PARALLEL(@degree) */ upper(trigger_schema) trigger_schema,
                    upper(event_object_table) as Table_Name,
                    upper(trigger_name) as Trigger_Name
                    FROM information_schema.triggers
                    WHERE trigger_schema = lower('@schemaname')
                    GROUP BY trigger_schema, trigger_name, event_object_table, action_timing, action_orientation
                    ORDER BY event_object_table, trigger_name;
                </Trigger>
            </Code>
        </Source>
        <Target>
            <Storage>
                <Sequence>
                    select /*+ PARALLEL(@degree) */ distinct schemaname as schema,
                    sequencename as sequence,
                    last_value - 1 as sequence_value
                    from pg_sequences where schemaname =lower('@schemaname')
                </Sequence>
                <Table>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name ,table_type FROM information_schema.tables
                    WHERE table_schema = lower('@schemaname') AND table_type = 'BASE TABLE' AND
                    table_schema||'.'||table_name
                    NOT IN (
                    SELECT inhrelid::regclass::text
                    FROM pg_inherits
                    )
                </Table>
                <Partition>
                    select /*+ PARALLEL(@degree) */ nmsp_parent.nspname as schemaname,parent.relname tablename,child.relname AS partition_table FROM pg_inherits
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_namespace nmsp_parent ON nmsp_parent.oid = parent.relnamespace
                    JOIN pg_namespace nmsp_child ON nmsp_child.oid = child.relnamespace
                    WHERE nmsp_parent.nspname = lower('@schemaname')
                    ORDER BY child.relname
                </Partition>
                <Primary_Key>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name,constraint_name,column_list,'ENABLED' as
                    status FROM (
                    SELECT /*+ PARALLEL(@degree) */
                    tc.table_schema,
                    tc.table_name,
                    tc.constraint_name,
                    string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list,'ENABLED'
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                    AND tc.table_name = kcu.table_name
                    WHERE upper(tc.constraint_type) = 'PRIMARY KEY'
                    AND tc.table_schema = lower('@schemaname')
                    GROUP BY tc.table_schema, tc.table_name, tc.constraint_name
                    ) AS primary_keys
                    WHERE (table_schema || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Primary_Key>
                <Unique_Constraint>
                    select /*+ PARALLEL(@degree) */ table_schema ,table_name ,constraint_name,column_list,'ENABLED' as
                    status FROM (
                    SELECT /*+ PARALLEL(@degree) */ tc.table_schema,
                    tc.table_name,
                    tc.constraint_name,
                    string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list,'ENABLED'
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                    AND tc.table_name = kcu.table_name
                    WHERE upper(tc.constraint_type) = 'UNIQUE'
                    AND tc.table_schema = lower('@schemaname')
                    GROUP BY tc.table_schema, tc.table_name, tc.constraint_name
                    ) AS unique_constraints
                    WHERE (table_schema || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Unique_Constraint>
                <Foreign_Key>
                    with cte as (
                    SELECT /*+ PARALLEL(@degree) */ conname AS constraint_name, conrelid::regclass AS table_name,
                    a.attname AS column_name,
                    confrelid::regclass AS referenced_table, fa.attname AS referenced_column
                    FROM pg_constraint AS c
                    JOIN pg_attribute AS a ON a.attnum = ANY(c.conkey) AND a.attrelid = c.conrelid
                    JOIN pg_attribute AS fa ON fa.attnum = ANY(c.confkey) AND fa.attrelid = c.confrelid)

                    select /*+ PARALLEL(@degree) */ split_part(table_name::regclass::varchar,'.',1)
                    schema_name,split_part(table_name::regclass::varchar,'.',2) table_name, constraint_name,'FOREIGN
                    KEY'||' (' ||column_name ||') ' ||'REFERENCES'
                    ||' '||referenced_table||' (' || referenced_column||');','ENABLED'
                    from cte
                    where split_part(table_name::regclass::varchar,'.',1) = lower('@schemaname')
                    and table_name::regclass::varchar not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname )
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema ,table_name ,column_name
                    FROM information_schema.columns
                    WHERE upper(is_nullable) = 'NO'
                    AND table_schema = lower('@schemaname')
                    and table_schema||'.'||table_name not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                    ORDER BY table_schema, table_name, column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name,column_name,column_default
                    FROM information_schema.columns
                    WHERE table_schema = lower('@schemaname')
                    and column_default IS NOT NULL
                    and table_schema||'.'||table_name not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Default_Constraint>
                <Check_Constraint>
                    with cte as(select /*+ PARALLEL(@degree) */
                    n.nspname AS schema_name,
                    conname AS constraint_name,
                    conrelid::regclass AS table_name,
                    pg_get_constraintdef(c.oid) AS check_expression,'ENABLED'
                    FROM pg_constraint c
                    JOIN pg_namespace n ON n.oid = c.connamespace
                    WHERE confrelid = 0 AND contype = 'c'
                    and n.nspname = lower('@schemaname')
                    )
                    select /*+ PARALLEL(@degree) */ split_part(table_name::text,'.',1) as
                    schema_name,split_part(table_name::text,'.',2) as
                    table_name,constraint_name,check_expression,'ENABLED' as status from cte where
                    schema_name ||'.'|| table_name::text not in (SELECT (cn.nspname || '.' || child.relname) AS
                    Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Check_Constraint>
                <Index>
                    SELECT /*+ PARALLEL(@degree) */
                    schema_name,
                    CASE WHEN table_name IS NULL OR table_name = '' THEN idx_schema_name ELSE table_name END AS
                    table_name,
                    index_name,
                    array_to_string(index_columns,',')AS index_columns,
                    pkey AS contraint_type,
                    table_name || '-' || index_name AS postgresconcat,
                    index_definition

                    FROM (
                    SELECT /*+ PARALLEL(@degree) */
                    ns.nspname AS schema_name,
                    split_part(idx.indrelid::REGCLASS::text, '.', 1) AS idx_schema_name,
                    split_part(idx.indrelid::REGCLASS::text, '.', 2) AS table_name,
                    ARRAY(SELECT pg_get_indexdef(idx.indexrelid, k + 1, TRUE)
                    FROM generate_subscripts(idx.indkey, 1) AS k
                    ORDER BY k) AS index_columns,
                    CASE
                    WHEN idx.indisprimary = true THEN 'Primary Key'
                    ELSE 'Non Primary Key'
                    END AS pkey,
                    pg_get_indexdef(idx.indexrelid::regclass) AS index_definition,
                    i.relname AS index_name,
                    idx.indisunique AS is_unique,
                    idx.indisprimary AS is_primary,
                    am.amname AS index_type,
                    (idx.indexprs IS NOT NULL) OR (idx.indkey::int[] @> array[0]) AS is_functional
                    FROM pg_index AS idx
                    JOIN pg_class AS i ON i.oid = idx.indexrelid
                    JOIN pg_am AS am ON i.relam = am.oid
                    JOIN pg_namespace AS ns ON i.relnamespace = ns.oid
                    JOIN pg_user AS u ON i.relowner = u.usesysid
                    LEFT JOIN pg_constraint AS con ON con.conindid = idx.indexrelid
                    WHERE NOT nspname LIKE 'pg%'
                    AND nspname = LOWER('@schemaname')
                    AND idx.indisprimary = false
                    AND (con.contype IS NULL OR con.contype != 'u')
                    ) ind_details
                    WHERE (schema_name || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname
                    )
                    ORDER BY 1, 2, 3
                </Index>
                <Synonym>
                    SELECT /*+ PARALLEL(@degree) */ viewname
                    FROM pg_views
                    WHERE schemaname = lower('@schemaname')
                    order by viewname
                </Synonym>
                <View>
                    SELECT /*+ PARALLEL(@degree) */ schemaname,viewname
                    FROM pg_views
                    WHERE schemaname = lower('@schemaname')
                    order by viewname
                </View>
                <Datatype>
                    SELECT /*+ PARALLEL(@degree) */
                    isc.table_schema AS "OWNER",
                    isc.table_name AS "TABLE_NAME",
                    isc.column_name AS "COLUMN_NAME",
                    isc.data_type AS "DATA_TYPE",
                    CASE
                    WHEN isc.data_type = 'integer' THEN '(' || numeric_precision || ',' || isc.numeric_scale || ')'
                    WHEN isc.data_type IN ('double precision', 'bigint') THEN '(' || numeric_precision || ')'
                    WHEN isc.data_type IN ('character varying', 'numeric', 'character') THEN '(' ||
                    character_maximum_length || ')'
                    WHEN isc.data_type = 'numeric'
                    AND isc.numeric_precision IS NULL THEN '[Default]'
                    WHEN isc.data_type IN ('date', 'timestamp without time zone', 'text', 'timestamp with time zone',
                    'ARRAY', 'USER-DEFINED', 'bytea', 'xml', 'uuid', 'oid' ) THEN '(' || ')'
                    END AS "COLUMN_SIZE",
                    isc.ordinal_position,
                    ist.table_type
                    FROM
                    information_schema.tables ist,
                    information_schema.columns isc
                    WHERE
                    ist.table_schema = isc.table_schema
                    AND ist.table_name = isc.table_name
                    AND ist.table_schema IN (lower('@schemaname'))
                    ORDER BY
                    isc.table_schema,
                    isc.table_name,
                    isc.ordinal_position
                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    Select /*+ PARALLEL(@degree) */ res.* from (select /*+ PARALLEL(@degree) */ routine_type,
                    routine_name as Code_Object_Name from information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    except
                    select /*+ PARALLEL(@degree) */ routine_type, routine_name as Code_Object_Name from
                    information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    and lower(data_type) =lower( 'trigger') ) res order by res.Code_Object_Name
                </Code_Objects>
                <Trigger>
                    SELECT /*+ PARALLEL(@degree) */ upper(trigger_schema) trigger_schema,
                    upper(event_object_table) as Table_Name,
                    upper(trigger_name) as Trigger_Name
                    FROM information_schema.triggers
                    WHERE trigger_schema = lower('@schemaname')
                    GROUP BY trigger_schema, trigger_name, event_object_table, action_timing, action_orientation
                    ORDER BY event_object_table, trigger_name;
                </Trigger>
            </Code>
        </Target>
    </Validation_Queries>
    <Table_Validation_Queries>
        <Source>
            <Storage>
                <Sequence>
                    select /*+ PARALLEL(@degree) */ distinct schemaname as schema,
                    sequencename as sequence,
                    last_value - 1 as sequence_value
                    from pg_sequences where schemaname =lower('@schemaname')
                </Sequence>
                <Table>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name ,table_type FROM information_schema.tables
                    WHERE table_schema = lower('@schemaname') AND table_type = 'BASE TABLE'
                </Table>
                <Partition>
                    select /*+ PARALLEL(@degree) */ nmsp_parent.nspname as schemaname,parent.relname tablename,child.relname AS partition_table FROM pg_inherits
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_namespace nmsp_parent ON nmsp_parent.oid = parent.relnamespace
                    JOIN pg_namespace nmsp_child ON nmsp_child.oid = child.relnamespace
                    WHERE nmsp_parent.nspname = lower('@schemaname') and parent.relname = lower('@tablename')
                    ORDER BY child.relname
                </Partition>
                <Primary_Key>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name,constraint_name,column_list,'ENABLED' as
                    status FROM (
                    SELECT /*+ PARALLEL(@degree) */
                    tc.table_schema,
                    tc.table_name,
                    tc.constraint_name,
                    string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list,'ENABLED'
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                    AND tc.table_name = kcu.table_name
                    WHERE upper(tc.constraint_type) = 'PRIMARY KEY'
                    AND tc.table_schema = lower('@schemaname')
                    and tc.table_name in (lower('@tablename'))
                    GROUP BY tc.table_schema, tc.table_name, tc.constraint_name
                    ) AS primary_keys
                    WHERE (table_schema || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Primary_Key>
                <Unique_Constraint>
                    select /*+ PARALLEL(@degree) */ table_schema ,table_name ,constraint_name,column_list,'ENABLED' as
                    status FROM (
                    SELECT /*+ PARALLEL(@degree) */ tc.table_schema,
                    tc.table_name,
                    tc.constraint_name,
                    string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list,'ENABLED'
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                    AND tc.table_name = kcu.table_name
                    WHERE upper(tc.constraint_type) = 'UNIQUE'
                    AND tc.table_schema = lower('@schemaname')
                    and tc.table_name in (lower('@tablename'))
                    GROUP BY tc.table_schema, tc.table_name, tc.constraint_name
                    ) AS unique_constraints
                    WHERE (table_schema || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Unique_Constraint>
                <Foreign_Key>
                    with cte as (
                    SELECT /*+ PARALLEL(@degree) */ conname AS constraint_name, conrelid::regclass AS table_name,
                    a.attname AS column_name,
                    confrelid::regclass AS referenced_table, fa.attname AS referenced_column
                    FROM pg_constraint AS c
                    JOIN pg_attribute AS a ON a.attnum = ANY(c.conkey) AND a.attrelid = c.conrelid
                    JOIN pg_attribute AS fa ON fa.attnum = ANY(c.confkey) AND fa.attrelid = c.confrelid)

                    select /*+ PARALLEL(@degree) */ split_part(table_name::regclass::varchar,'.',1)
                    schema_name,split_part(table_name::regclass::varchar,'.',2) table_name, constraint_name,'FOREIGN
                    KEY'||' (' ||column_name ||') ' ||'REFERENCES'
                    ||' '||referenced_table||' (' || referenced_column||');','ENABLED'
                    from cte
                    where split_part(table_name::regclass::varchar,'.',1) = lower('@schemaname')
                    and split_part(table_name::regclass::varchar,'.',2) in (lower('@tablename'))
                    and table_name::regclass::varchar not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname )
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema ,table_name ,column_name
                    FROM information_schema.columns
                    WHERE upper(is_nullable) = 'NO'
                    AND table_schema = lower('@schemaname')
                    and table_name in (lower('@tablename'))
                    and table_schema||'.'||table_name not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                    ORDER BY table_schema, table_name, column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name,column_name,column_default
                    FROM information_schema.columns
                    WHERE table_schema = lower('@schemaname')
                    and table_name in (lower('@tablename'))
                    and column_default IS NOT NULL
                    and table_schema||'.'||table_name not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Default_Constraint>
                <Check_Constraint>
                    with cte as(select /*+ PARALLEL(@degree) */
                    n.nspname AS schema_name,
                    conname AS constraint_name,
                    conrelid::regclass AS table_name,
                    pg_get_constraintdef(c.oid) AS check_expression,'ENABLED'
                    FROM pg_constraint c
                    JOIN pg_namespace n ON n.oid = c.connamespace
                    WHERE confrelid = 0 AND contype = 'c'
                    and n.nspname = lower('@schemaname')
                    )
                    select /*+ PARALLEL(@degree) */ split_part(table_name::text,'.',1) as
                    schema_name,split_part(table_name::text,'.',2) as
                    table_name,constraint_name,check_expression,'ENABLED' as status from cte where
                    split_part(table_name::text,'.',2) in (lower('@tablename'))
                    and schema_name ||'.'|| table_name::text not in (SELECT (cn.nspname || '.' || child.relname) AS
                    Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Check_Constraint>
                <Index>
                    SELECT /*+ PARALLEL(@degree) */
                    schema_name,
                    CASE WHEN table_name IS NULL OR table_name = '' THEN idx_schema_name ELSE table_name END AS
                    table_name,
                    index_name,
                    index_columns,
                    pkey AS contraint_type,
                    index_definition,
                    table_name || '-' || index_name AS postgresconcat
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */
                    ns.nspname AS schema_name,
                    split_part(idx.indrelid::REGCLASS::text, '.', 1) AS idx_schema_name,
                    split_part(idx.indrelid::REGCLASS::text, '.', 2) AS table_name,
                    ARRAY(SELECT pg_get_indexdef(idx.indexrelid, k + 1, TRUE)
                    FROM generate_subscripts(idx.indkey, 1) AS k
                    ORDER BY k) AS index_columns,
                    CASE
                    WHEN idx.indisprimary = true THEN 'Primary Key'
                    ELSE 'Non Primary Key'
                    END AS pkey,
                    pg_get_indexdef(idx.indexrelid::regclass) AS index_definition,
                    i.relname AS index_name,
                    idx.indisunique AS is_unique,
                    idx.indisprimary AS is_primary,
                    am.amname AS index_type,
                    (idx.indexprs IS NOT NULL) OR (idx.indkey::int[] @> array[0]) AS is_functional
                    FROM pg_index AS idx
                    JOIN pg_class AS i ON i.oid = idx.indexrelid
                    JOIN pg_am AS am ON i.relam = am.oid
                    JOIN pg_namespace AS ns ON i.relnamespace = ns.oid
                    JOIN pg_user AS u ON i.relowner = u.usesysid
                    LEFT JOIN pg_constraint AS con ON con.conindid = idx.indexrelid
                    WHERE NOT nspname LIKE 'pg%'
                    AND nspname = LOWER('@schemaname')
                    and split_part(idx.indrelid :: REGCLASS :: text,'.',2) in (lower('@tablename'))
                    AND idx.indisprimary = false
                    AND (con.contype IS NULL OR con.contype != 'u')
                    ) ind_details
                    WHERE (schema_name || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname
                    )
                    ORDER BY 1, 2, 3
                </Index>
                <Synonym>
                    SELECT /*+ PARALLEL(@degree) */ viewname
                    FROM pg_views
                    WHERE schemaname = lower('@schemaname')

                    order by viewname
                </Synonym>
                <View>
                    SELECT /*+ PARALLEL(@degree) */ schemaname,viewname
                    FROM pg_views
                    WHERE schemaname = lower('@schemaname')
                    order by viewname
                </View>
                <Datatype>
                    SELECT /*+ PARALLEL(@degree) */
                    isc.table_schema AS "OWNER",
                    isc.table_name AS "TABLE_NAME",
                    isc.column_name AS "COLUMN_NAME",
                    isc.data_type AS "DATA_TYPE",
                    CASE
                    WHEN isc.data_type = 'integer' THEN '(' || numeric_precision || ',' || isc.numeric_scale || ')'
                    WHEN isc.data_type IN ('double precision', 'bigint') THEN '(' || numeric_precision || ')'
                    WHEN isc.data_type IN ('character varying', 'numeric', 'character') THEN '(' ||
                    character_maximum_length || ')'
                    WHEN isc.data_type = 'numeric'
                    AND isc.numeric_precision IS NULL THEN '[Default]'
                    WHEN isc.data_type IN ('date', 'timestamp without time zone', 'text', 'timestamp with time zone',
                    'ARRAY', 'USER-DEFINED', 'bytea', 'xml', 'uuid', 'oid' ) THEN '(' || ')'
                    END AS "COLUMN_SIZE",
                    isc.ordinal_position,
                    ist.table_type
                    FROM
                    information_schema.tables ist,
                    information_schema.columns isc
                    WHERE
                    ist.table_schema = isc.table_schema
                    AND ist.table_name = isc.table_name
                    AND ist.table_schema IN (lower('@schemaname'))
                    and ist.table_name in (lower('@tablename'))
                    ORDER BY
                    isc.table_schema,
                    isc.table_name,
                    isc.ordinal_position
                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    Select /*+ PARALLEL(@degree) */ res.* from (select /*+ PARALLEL(@degree) */ routine_type,
                    routine_name as Code_Object_Name from information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    except
                    select /*+ PARALLEL(@degree) */ routine_type, routine_name as Code_Object_Name from
                    information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    and lower(data_type) =lower( 'trigger') ) res order by res.Code_Object_Name
                </Code_Objects>
                <Trigger>
                    SELECT /*+ PARALLEL(@degree) */ upper(trigger_schema) trigger_schema,
                    upper(event_object_table) as Table_Name,
                    upper(trigger_name) as Trigger_Name
                    FROM information_schema.triggers
                    WHERE trigger_schema = lower('@schemaname')

                    GROUP BY trigger_schema, trigger_name, event_object_table, action_timing, action_orientation
                    ORDER BY event_object_table, trigger_name;
                </Trigger>
            </Code>
        </Source>
        <Target>
            <Storage>
                <Sequence>
                    select /*+ PARALLEL(@degree) */ distinct schemaname as schema,
                    sequencename as sequence,
                    last_value - 1 as sequence_value
                    from pg_sequences where schemaname =lower('@schemaname')
                </Sequence>
                <Table>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name ,table_type FROM information_schema.tables
                    WHERE table_schema = lower('@schemaname') AND table_type = 'BASE TABLE'
                </Table>
                <Partition>
                    select /*+ PARALLEL(@degree) */ nmsp_parent.nspname as schemaname,parent.relname tablename,child.relname AS partition_table FROM pg_inherits
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_namespace nmsp_parent ON nmsp_parent.oid = parent.relnamespace
                    JOIN pg_namespace nmsp_child ON nmsp_child.oid = child.relnamespace
                    WHERE nmsp_parent.nspname = lower('@schemaname') and parent.relname = lower('@tablename')
                    ORDER BY child.relname
                </Partition>
                <Primary_Key>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name,constraint_name,column_list,'ENABLED' as
                    status FROM (
                    SELECT /*+ PARALLEL(@degree) */
                    tc.table_schema,
                    tc.table_name,
                    tc.constraint_name,
                    string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list,'ENABLED'
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                    AND tc.table_name = kcu.table_name
                    WHERE upper(tc.constraint_type) = 'PRIMARY KEY'
                    AND tc.table_schema = lower('@schemaname')
                    and tc.table_name in (lower('@tablename'))
                    GROUP BY tc.table_schema, tc.table_name, tc.constraint_name
                    ) AS primary_keys
                    WHERE (table_schema || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Primary_Key>
                <Unique_Constraint>
                    select /*+ PARALLEL(@degree) */ table_schema ,table_name ,constraint_name,column_list,'ENABLED' as
                    status FROM (
                    SELECT /*+ PARALLEL(@degree) */ tc.table_schema,
                    tc.table_name,
                    tc.constraint_name,
                    string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list,'ENABLED'
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                    AND tc.table_name = kcu.table_name
                    WHERE upper(tc.constraint_type) = 'UNIQUE'
                    AND tc.table_schema = lower('@schemaname')
                    and tc.table_name in (lower('@tablename'))
                    GROUP BY tc.table_schema, tc.table_name, tc.constraint_name
                    ) AS unique_constraints
                    WHERE (table_schema || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Unique_Constraint>
                <Foreign_Key>
                    with cte as (
                    SELECT /*+ PARALLEL(@degree) */ conname AS constraint_name, conrelid::regclass AS table_name,
                    a.attname AS column_name,
                    confrelid::regclass AS referenced_table, fa.attname AS referenced_column
                    FROM pg_constraint AS c
                    JOIN pg_attribute AS a ON a.attnum = ANY(c.conkey) AND a.attrelid = c.conrelid
                    JOIN pg_attribute AS fa ON fa.attnum = ANY(c.confkey) AND fa.attrelid = c.confrelid)

                    select /*+ PARALLEL(@degree) */ split_part(table_name::regclass::varchar,'.',1)
                    schema_name,split_part(table_name::regclass::varchar,'.',2) table_name, constraint_name,'FOREIGN
                    KEY'||' (' ||column_name ||') ' ||'REFERENCES'
                    ||' '||referenced_table||' (' || referenced_column||');','ENABLED'
                    from cte
                    where split_part(table_name::regclass::varchar,'.',1) = lower('@schemaname')
                    and split_part(table_name::regclass::varchar,'.',2) in (lower('@tablename'))
                    and table_name::regclass::varchar not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname )
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema ,table_name ,column_name
                    FROM information_schema.columns
                    WHERE upper(is_nullable) = 'NO'
                    AND table_schema = lower('@schemaname')
                    and table_name in (lower('@tablename'))
                    and table_schema||'.'||table_name not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                    ORDER BY table_schema, table_name, column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name,column_name,column_default
                    FROM information_schema.columns
                    WHERE table_schema = lower('@schemaname')
                    and table_name in (lower('@tablename'))
                    and column_default IS NOT NULL
                    and table_schema||'.'||table_name not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Default_Constraint>
                <Check_Constraint>
                    with cte as(select /*+ PARALLEL(@degree) */
                    n.nspname AS schema_name,
                    conname AS constraint_name,
                    conrelid::regclass AS table_name,
                    pg_get_constraintdef(c.oid) AS check_expression,'ENABLED'
                    FROM pg_constraint c
                    JOIN pg_namespace n ON n.oid = c.connamespace
                    WHERE confrelid = 0 AND contype = 'c'
                    and n.nspname = lower('@schemaname')
                    )
                    select /*+ PARALLEL(@degree) */ split_part(table_name::text,'.',1) as
                    schema_name,split_part(table_name::text,'.',2) as
                    table_name,constraint_name,check_expression,'ENABLED' as status from cte where
                    split_part(table_name::text,'.',2) in (lower('@tablename'))
                    and schema_name ||'.'|| table_name::text not in (SELECT (cn.nspname || '.' || child.relname) AS
                    Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Check_Constraint>
                <Index>
                    SELECT /*+ PARALLEL(@degree) */
                    schema_name,
                    CASE WHEN table_name IS NULL OR table_name = '' THEN idx_schema_name ELSE table_name END AS
                    table_name,
                    index_name,
                    index_columns,
                    pkey AS contraint_type,
                    index_definition,
                    table_name || '-' || index_name AS postgresconcat
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */
                    ns.nspname AS schema_name,
                    split_part(idx.indrelid::REGCLASS::text, '.', 1) AS idx_schema_name,
                    split_part(idx.indrelid::REGCLASS::text, '.', 2) AS table_name,
                    ARRAY(SELECT pg_get_indexdef(idx.indexrelid, k + 1, TRUE)
                    FROM generate_subscripts(idx.indkey, 1) AS k
                    ORDER BY k) AS index_columns,
                    CASE
                    WHEN idx.indisprimary = true THEN 'Primary Key'
                    ELSE 'Non Primary Key'
                    END AS pkey,
                    pg_get_indexdef(idx.indexrelid::regclass) AS index_definition,
                    i.relname AS index_name,
                    idx.indisunique AS is_unique,
                    idx.indisprimary AS is_primary,
                    am.amname AS index_type,
                    (idx.indexprs IS NOT NULL) OR (idx.indkey::int[] @> array[0]) AS is_functional
                    FROM pg_index AS idx
                    JOIN pg_class AS i ON i.oid = idx.indexrelid
                    JOIN pg_am AS am ON i.relam = am.oid
                    JOIN pg_namespace AS ns ON i.relnamespace = ns.oid
                    JOIN pg_user AS u ON i.relowner = u.usesysid
                    LEFT JOIN pg_constraint AS con ON con.conindid = idx.indexrelid
                    WHERE NOT nspname LIKE 'pg%'
                    AND nspname = LOWER('@schemaname')
                    and split_part(idx.indrelid :: REGCLASS :: text,'.',2) in (lower('@tablename'))
                    AND idx.indisprimary = false
                    AND (con.contype IS NULL OR con.contype != 'u')
                    ) ind_details
                    WHERE (schema_name || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname
                    )
                    ORDER BY 1, 2, 3
                </Index>
                <Synonym>
                    SELECT /*+ PARALLEL(@degree) */ viewname
                    FROM pg_views
                    WHERE schemaname = lower('@schemaname')

                    order by viewname
                </Synonym>
                <View>
                    SELECT /*+ PARALLEL(@degree) */ schemaname,viewname
                    FROM pg_views
                    WHERE schemaname = lower('@schemaname')
                    order by viewname
                </View>
                <Datatype>
                    SELECT /*+ PARALLEL(@degree) */
                    isc.table_schema AS "OWNER",
                    isc.table_name AS "TABLE_NAME",
                    isc.column_name AS "COLUMN_NAME",
                    isc.data_type AS "DATA_TYPE",
                    CASE
                    WHEN isc.data_type = 'integer' THEN '(' || numeric_precision || ',' || isc.numeric_scale || ')'
                    WHEN isc.data_type IN ('double precision', 'bigint') THEN '(' || numeric_precision || ')'
                    WHEN isc.data_type IN ('character varying', 'numeric', 'character') THEN '(' ||
                    character_maximum_length || ')'
                    WHEN isc.data_type = 'numeric'
                    AND isc.numeric_precision IS NULL THEN '[Default]'
                    WHEN isc.data_type IN ('date', 'timestamp without time zone', 'text', 'timestamp with time zone',
                    'ARRAY', 'USER-DEFINED', 'bytea', 'xml', 'uuid', 'oid' ) THEN '(' || ')'
                    END AS "COLUMN_SIZE",
                    isc.ordinal_position,
                    ist.table_type
                    FROM
                    information_schema.tables ist,
                    information_schema.columns isc
                    WHERE
                    ist.table_schema = isc.table_schema
                    AND ist.table_name = isc.table_name
                    AND ist.table_schema IN (lower('@schemaname'))
                    and ist.table_name in (lower('@tablename'))
                    ORDER BY
                    isc.table_schema,
                    isc.table_name,
                    isc.ordinal_position
                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    Select /*+ PARALLEL(@degree) */ res.* from (select /*+ PARALLEL(@degree) */ routine_type,
                    routine_name as Code_Object_Name from information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    except
                    select /*+ PARALLEL(@degree) */ routine_type, routine_name as Code_Object_Name from
                    information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    and lower(data_type) =lower( 'trigger') ) res order by res.Code_Object_Name
                </Code_Objects>
                <Trigger>
                    SELECT /*+ PARALLEL(@degree) */ upper(trigger_schema) trigger_schema,
                    upper(event_object_table) as Table_Name,
                    upper(trigger_name) as Trigger_Name
                    FROM information_schema.triggers
                    WHERE trigger_schema = lower('@schemaname')

                    GROUP BY trigger_schema, trigger_name, event_object_table, action_timing, action_orientation
                    ORDER BY event_object_table, trigger_name;
                </Trigger>
            </Code>
        </Target>
    </Table_Validation_Queries>
    <Extract_Tables>
        SELECT distinct c.relname AS table_name,'VALID'as STATUS
        FROM pg_catalog.pg_class c
        JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
        WHERE upper(n.nspname) = upper('@schemaname')
        AND c.relkind = 'r'
        ORDER BY c.relname
    </Extract_Tables>
</Queries>