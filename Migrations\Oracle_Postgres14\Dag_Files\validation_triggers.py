import os, re, requests, json, psycopg2, cx_Oracle, decimal, warnings, shutil
import pandas as pd
from datetime import datetime
import xml.etree.ElementTree as ET
from openpyxl import load_workbook, Workbook
from openpyxl.styles import PatternFill, Border, Side, Font
from copy import copy

warnings.filterwarnings('ignore')

connection_url = os.getenv("API_HOST", 'http://qmig-eng.qmig-ns.svc.cluster.local:8080')
connection_url = connection_url + '/api/v1/'


def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


def DB_connection(database_data):
    host_name = database_data['host']
    port = database_data['port']
    service_name = database_data['db_name']
    user_name = database_data['name']
    password = database_data['password']
    try:
        dsn = cx_Oracle.makedsn(host=host_name, port=port, service_name=service_name)
        connection = cx_Oracle.Connection(user=user_name, password=password, dsn=dsn)
        error = ''
    except cx_Oracle.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near database connection", e)
    return connection, error


def target_DB_connection(db_data):
    try:
        connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                      host=db_data['host'], database=db_data['db_name'],
                                      port=db_data['port'])
        error = ''
    except psycopg2.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near target database connection", e)
    return connection, error


def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        data = cursor.fetchall()
        data = [[str(value) if isinstance(value, cx_Oracle.LOB) else value for value in row] for row in data]
    except cx_Oracle.DatabaseError as e:
        print("Issue found near database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data


def api_authentication():
    url = connection_url + 'Common/Security/VerifyPython'
    project_id = os.environ['PROJECT_ID']
    params = {
        "projectId": str(project_id),
        "content": "8/2DlAH8q1R+untey0ZuqMuD89WX4m2XoSCWhxQ+UOk="}
    response = requests.get(url, params=params)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = json.loads(status_response['text'])
        token_data = data['token']
        return token_data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def get_project_DB_info(token_data, project_id):
    url = connection_url + 'Common/Master/GetProjectMigDetailSelect'
    payload = {
        "projectId": str(project_id),
        "migsrcType": "D"
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.post(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = data_text['jsonResponseData']
        data_text = [dict for dict in data_text['Table1'] if dict['dbconname'] == 'PRJDB' + str(project_id)][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def get_source_DB_info(token_data, project_id, source_connection_id):
    url = connection_url + 'Common/Master/GetDBConnections'
    payload = {
        "projectId": str(project_id),
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.get(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = [dict for dict in data_text['Table1'] if
                     dict['migsrctgt'] == 'S' and dict['Connection_ID'] == source_connection_id][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def get_target_DB_info(token_data, project_id, target_connection_id):
    url = connection_url + 'Common/Master/GetDBConnections'
    payload = {
        "projectId": str(project_id),
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.get(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = [dict for dict in data_text['Table1'] if
                     dict['migsrctgt'] == 'T' and dict['Connection_ID'] == target_connection_id][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def decrypt_string(encrypted_str, token_data):
    application_url = connection_url + 'Common/Cryptography/DecryptData'
    payload = {
        "NeedToDecrypt": encrypted_str
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response = requests.post(application_url, data=json.dumps(payload), headers=headers)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = response.text
        return data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def decrypt_database_details(token_data, project_id, category, connection_id):
    prj_data = {}
    prj_db_data = {}
    if category == 'Project':
        prj_db_data = get_project_DB_info(token_data, project_id)
    elif category == 'Source':
        prj_db_data = get_source_DB_info(token_data, project_id, connection_id)
    elif category == 'Target':
        prj_db_data = get_target_DB_info(token_data, project_id, connection_id)
    password = decrypt_string(prj_db_data['dbpassword'], token_data)
    userid = decrypt_string(prj_db_data['dbuserid'], token_data)
    userid = re.split('\s+', re.sub(r"[\x00-\x1F\x7F]", ' ', userid))
    userid = ' '.join(userid).strip()
    password = re.split('\s+', re.sub(r"[\x00-\x1F\x7F]", ' ', password))
    password = ' '.join(password).strip()
    port = prj_db_data['dbport']
    host = prj_db_data['dbhost']
    if category == 'Project':
        service_name = ''
        parallelprocess = ''
    else:
        service_name = prj_db_data['service_name']
        parallelprocess = prj_db_data['parallelprocess']
    dbname = prj_db_data['dbname']
    prj_data['password'] = password
    prj_data['name'] = userid
    prj_data['port'] = port
    prj_data['host'] = host
    prj_data['db_name'] = dbname
    prj_data['service_name'] = service_name
    prj_data['parallelprocess'] = parallelprocess
    return prj_data


def get_config_id(connection, file_name, process_type):
    cursor = connection.cursor()
    try:
        cursor.execute(
            f"select config_id, transaction_number, transaction_file, config_status from audit_config where config_name = '{file_name}' and process_type = '{process_type}'")
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def get_dag_id(connection, config_id, process_type):
    cursor = connection.cursor()
    try:
        cursor.execute(
            f"select dag_id, dag_name, dag_status from audit_dags where config_id = {config_id} and dag_type = '{process_type}'")
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def transaction_insert(connection, config_id, transaction_number, transaction_file, config_status):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_transaction_insert(%s,%s,%s,%s,%s);fetch all in "dataset";',
                       (config_id, transaction_number, transaction_file, config_status, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def dag_insert(connection, dag_name, dag_type, schema_name, target_schema, table_name,
               table_size, concurrency, chunk_size, chunk_parts, dag_status, config_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_dags_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (dag_name, dag_type, schema_name, target_schema, table_name, table_size, concurrency, chunk_size,
             chunk_parts, dag_status, config_id, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def dag_update(connection, dag_id, dag_status, dag_end_time):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_audit_dags_update(%s,%s,%s,%s);fetch all in "dataset";',
                       (dag_id, dag_status, dag_end_time, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def task_insert(connection, task_name, task_type, attempt, lower_bound, upper_bound, request_memory, limit_memory,
                task_start_time,
                task_status, dag_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_task_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (task_name, task_type, attempt, str(lower_bound), str(upper_bound), request_memory, limit_memory,
             str(task_start_time),
             task_status, dag_id, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def task_update(connection, task_id, task_row_count, task_status, task_error, task_end_time, extraction_time,
                transform_time, load_time):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_task_update(%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (
            task_id, task_row_count, task_status, task_error, task_end_time, extraction_time, transform_time, load_time,
            'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def pre_validation_trigger(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    file_schema_name = kwargs['file_schema']
    target_schema = kwargs['target_schema']
    file_target_schema = kwargs['file_target_schema']
    table_name = kwargs['table_name']
    file_name = kwargs['file_name']
    token_data = kwargs['token_data']
    dag_name = kwargs['dag_id']
    task_name = kwargs['task_name']

    source_name = 'Oracle'
    target_name = 'Postgres'

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    config_data = get_config_id(project_connection, file_name, 'Pre_Validation')
    config_id = config_data[0][0]

    if config_data[0][3] in ['Created', None, '']:
        transaction_insert(project_connection, config_id, None, None, 'Running')
        dag_id = dag_insert(project_connection, dag_name, 'Pre_Validation', file_schema_name, file_target_schema, None,
                            None,
                            kwargs['concurrecy'], None, None, 'Running', config_id)
    else:
        dag_id = get_dag_id(project_connection, config_id, 'Pre_Validation')
    dag_id = dag_id[0][0]

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, task_name, 'Pre_Validation', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    try:
        extra_path = os.environ['EXTRA_FOLDER']
        root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

        validation_reports_path = root_folder + '/' + 'Validation_Reports' + '/' + file_name.lower()
        if not os.path.exists(validation_reports_path):
            os.makedirs(validation_reports_path)

        xml_path = extra_path + '/' + 'Validation_Queries' + '/' + 'validation_queries.xml'
        tree = ET.parse(xml_path)
        root = tree.getroot()

        objects_list = ['Table', 'Partition', 'Datatype']

        source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
        source_connection, error = DB_connection(source_DB_details)

        target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
        target_connection, error = target_DB_connection(target_DB_details)

        file_path = f'{validation_reports_path}/{schema_name.lowe()}_{table_name.lower()}_report.xlsx'
        with pd.ExcelWriter(file_path) as writer:
            for object_type in objects_list:
                source_query_tag = 'Table_Validation_Queries/Source/' + '/' + object_type
                object_source_query = list(root.iterfind(source_query_tag))[0].text
                object_source_query = object_source_query.replace('@schemaname', schema_name.lower()).replace('@order',
                                                                                                              '').replace(
                    '@degree', str(source_DB_details['parallelprocess'])).replace('@tablename', table_name.upper())
                source_object_output = execute_query(source_connection, object_source_query)

                target_query_tag = 'Table_Validation_Queries/Target/' + '/' + object_type
                object_target_query = list(root.iterfind(target_query_tag))[0].text
                object_target_query = object_target_query.replace('@schemaname', target_schema.upper()).replace(
                    '@order',
                    '').replace(
                    '@degree', str(target_DB_details['parallelprocess'])).replace('@tablename', table_name.lower())
                target_object_output = execute_query(target_connection, object_target_query)

                if object_type == "Table":
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Source_Schema_Name', 'Table_Name', 'Table_Type'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Target_Schema_Name', 'Table_Name', 'Table_Type'])
                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    source_data_df['Table_Type'] = source_data_df['Table_Type'].replace({'base table': 'table'})
                    target_data_df['Table_Type'] = target_data_df['Table_Type'].replace({'base table': 'table'})

                    matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Table_Type'],
                                            suffixes=('_Source', '_Target'), how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                    source_data = source_data_df[
                        ~source_data_df['Table_Name'].isin(matched_data['Table_Name'])]
                    target_data = target_data_df[
                        ~target_data_df['Table_Name'].isin(matched_data['Table_Name'])]

                    source_only_data = pd.merge(source_data, target_data,
                                                on=['Table_Name', 'Table_Type'], suffixes=('_Source', '_Target'),
                                                how='left', indicator=True)
                    source_only_data = source_only_data[source_only_data['_merge'] == 'left_only'].drop(
                        columns=['_merge'])
                    source_only_data['Status'] = f"Available in {source_name} not in {target_name}"

                    target_only_data = pd.merge(source_data, target_data,
                                                on=['Table_Name', 'Table_Type'], suffixes=('_Source', '_Target'),
                                                how='right', indicator=True)
                    target_only_data = target_only_data[target_only_data['_merge'] == 'right_only'].drop(
                        columns=['_merge'])
                    target_only_data['Status'] = f"Available in {target_name} not in {source_name}"

                    result_df = pd.concat([matched_data, source_only_data, target_only_data], ignore_index=True)
                    result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                    matched_tables_list = matched_data['Table_Name'].values.tolist()
                    table_counts_list = []
                    for table_name in matched_tables_list:
                        source_column_count_query = f"select COLUMN_NAME, DATA_TYPE from ALL_TAB_COLUMNS where upper(OWNER) = '{schema_name.upper()}' and upper(TABLE_NAME) = '{table_name.upper()}' ORDER BY COLUMN_ID"
                        target_column_count_query = f"select column_name, data_type from information_schema.columns where upper(table_schema) = '{target_schema.upper()}' and UPPER(table_name) = '{table_name.upper()}' order by ordinal_position "

                        source_column_data = execute_query(source_connection, source_column_count_query)
                        target_column_data = execute_query(target_connection, target_column_count_query)
                        difference_count = abs(len(source_column_data) - len(target_column_data))

                        created_tuple = (
                            schema_name, target_schema, table_name, len(source_column_data), len(target_column_data),
                            difference_count)
                        table_counts_list.append(created_tuple)

                    table_count_df = pd.DataFrame(table_counts_list,
                                                  columns=['Source_Schema', 'Target_Schema', 'Table_Name',
                                                           'Source_Column_Count', 'Target_Column_Count',
                                                           'Difference_Count'])
                    table_count_df.to_excel(writer, sheet_name="Column_Counts", index=False)

                elif object_type == 'Partition':
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Schema_name', 'Table_Name', 'Partition_Name'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Schema_name', 'Table_Name', 'Partition_Name'])

                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                    source_data_df['Partition_Name'] = source_data_df.apply(
                        lambda row: f"{row['Table_Name']}_{row['Partition_Name']}" if isinstance(row['Partition_Name'],
                                                                                                 str) else row[
                            'Partition_Name'], axis=1)

                    if not source_data_df.empty or not target_data_df.empty:
                        matched_data = pd.merge(source_data_df, target_data_df,
                                                on=['Table_Name', 'Partition_Name'],
                                                suffixes=('_Source', '_Target'), how='inner')
                        matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                        source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Partition_Name'],
                                               suffixes=('_Source', '_Target'), how='left', indicator=True)
                        source_data = source_data[source_data['_merge'] == 'left_only']
                        source_data['Status'] = f"Available in {source_name} not in {target_name}"

                        target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Partition_Name'],
                                               suffixes=('_Source', '_Target'), how='right', indicator=True)
                        target_data = target_data[target_data['_merge'] == 'right_only']
                        target_data['Status'] = f"Available in {target_name} not in {source_name}"

                        result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                        result_df = result_df.drop(columns=['_merge'], errors='ignore')
                        result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                if object_type == 'Datatype':
                    datatypes_dict = {'char': 'character',
                                      'date': 'timestamp without time zone',
                                      'long': 'text',
                                      'long raw': 'bytea',
                                      'clob': 'text',
                                      'nclob': 'text',
                                      'blob': 'bytea',
                                      'bfile': 'bytea',
                                      'raw': 'bytea',
                                      'float': 'double precision',
                                      'urowid': 'oid',
                                      'rowid': 'oid',
                                      'timestamp': 'timestamp without time zone',
                                      'timestamp(6)': 'timestamp without time zone',
                                      'timestamp(6) with time zone': 'timestamp with time zone',
                                      'xmltype': 'xml',
                                      'binary_integer': 'integer',
                                      'pls_integer': 'integer',
                                      'timestamp with time zone': 'timestamp with time zone',
                                      'timestamp with local time zone': 'timestamp with time zone',
                                      'varchar2': 'character varying',
                                      'number': 'numeric',
                                      'nvarchar2': 'character varying',
                                      'obj_snsdetails': 'user-defined',
                                      'timestamp(8)': 'timestamp without time zone',
                                      'timestamp(8) with time zone': 'timestamp with time zone',
                                      'nchar': 'character',
                                      'oid': 'text'}
                    datatypes_dict = {key.lower(): value.lower() for key, value in datatypes_dict.items()}
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Source_Schema', 'Table_Name', 'Column_Name',
                                                           'Source_Datatype',
                                                           'Source_Datatype_Length', 'Source_Index',
                                                           'Table_Type'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Target_Schema', 'Table_Name', 'Column_Name',
                                                           'Target_Datatype',
                                                           'Target_Datatype_Length', 'Target_Index',
                                                           'Table_Type'])

                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    source_data_df['Table_Type'] = source_data_df['Table_Type'].replace({'base table': 'table'})
                    target_data_df['Table_Type'] = target_data_df['Table_Type'].replace({'base table': 'table'})
                    source_data_df['Source_Modified_Datatype'] = source_data_df['Source_Datatype'].replace(
                        datatypes_dict)
                    source_data_df['Source_Datatype_Length'] = source_data_df[
                        'Source_Datatype_Length'].str.replace('(', '', regex=False).str.replace(')', '',
                                                                                                regex=False)
                    target_data_df['Target_Modified_Datatype'] = target_data_df['Target_Datatype']
                    target_data_df['Target_Datatype_Length'] = target_data_df[
                        'Target_Datatype_Length'].str.replace('(', '', regex=False).str.replace(')', '',
                                                                                                regex=False)

                    merged_df = pd.merge(source_data_df, target_data_df,
                                         on=['Table_Name', 'Column_Name', 'Table_Type'], how='inner')

                    result_match = []
                    for index, row in merged_df.iterrows():
                        if row['Source_Modified_Datatype'] == row['Target_Modified_Datatype'] and row[
                            'Source_Index'] == \
                                row[
                                    'Target_Index'] and row['Source_Datatype_Length'] == row[
                            'Target_Datatype_Length']:
                            status = 'Matched'
                        elif (row['Source_Modified_Datatype'] == 'double precision' and row[
                            'Target_Modified_Datatype'] == 'double precision') and row[
                            'Target_Datatype_Length'] == '53':
                            status = 'Matched'
                        elif (row['Source_Modified_Datatype'] == 'numeric') and row['Target_Modified_Datatype'] in [
                            'smallint',
                            'bigint',
                            'integer',
                            'numeric']:
                            status = 'Matched'
                        else:
                            if (row['Source_Modified_Datatype'] == row['Target_Modified_Datatype']) and (
                                    row['Source_Index'] == row['Target_Index']) and (
                                    row['Source_Datatype_Length'] in ['', None, 'None', 'none'] or row[
                                'Target_Datatype_Length'] in ['', None, 'None', 'none']):
                                status = 'Matched'
                            else:
                                status = 'Not_Matched'

                        result_match.append(
                            (row['Source_Schema'], row['Target_Schema'], row['Table_Name'], row['Column_Name'],
                             row['Source_Datatype'],
                             row['Source_Datatype_Length'], row['Target_Datatype'],
                             row['Target_Datatype_Length'], row['Source_Index'], row['Target_Index'],
                             row['Table_Type'], status))

                    result_match_df = pd.DataFrame(result_match,
                                                   columns=['Schema_Name', 'Target_Schema', 'Table_Name', 'Column_Name',
                                                            'Source_Datatype',
                                                            'Source_Datatype_Length', 'Target_Datatype',
                                                            'Target_Datatype_Length',
                                                            'Source_Index', 'Target_Index', 'Table_Type', 'Status'])
                    result_match_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Success', None, task_end_time, None, None, None)

        dag_end_time = datetime.now()
        dag_update(project_connection, dag_id, 'Success', dag_end_time)
    except Exception as error:
        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        dag_update(project_connection, dag_id, 'Fail', dag_end_time)


def row_count_insert(connection, table_counts_list):
    row_count_insert_query = """insert into public.audit_validation (schema, target_schema, table_name, source_row_count, target_row_count, difference_count, dag_id, config_id)
                                values (%s,%s,%s,%s,%s,%s,%s,%s)
                                ON CONFLICT (schema, target_schema, table_name, dag_id, config_id) DO UPDATE SET
                                source_row_count = EXCLUDED.source_row_count,
                                target_row_count = EXCLUDED.target_row_count,
                                difference_count = EXCLUDED.difference_count
                                """

    cursor = connection.cursor()
    try:
        cursor.executemany(row_count_insert_query, table_counts_list)
    except Exception as error:
        print(f"Error occurred at row count insert: {str(error)}")
        pass
    finally:
        connection.commit()
        cursor.close()


def post_validation_trigger(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    file_schema_name = kwargs['file_schema']
    target_schema = kwargs['target_schema']
    file_target_schema = kwargs['file_target_schema']
    table_name = kwargs['table_name']
    file_name = kwargs['file_name']
    token_data = kwargs['token_data']
    dag_name = kwargs['dag_id']
    task_name = kwargs['task_name']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    config_data = get_config_id(project_connection, file_name, 'Post_Validation')
    config_id = config_data[0][0]

    if config_data[0][3] in ['Created', None, '']:
        transaction_insert(project_connection, config_id, None, None, 'Running')
        dag_id = dag_insert(project_connection, dag_name, 'Post_Validation', file_schema_name, file_target_schema, None,
                            None,
                            kwargs['concurrecy'], None, None, 'Running', config_id)
    else:
        dag_id = get_dag_id(project_connection, config_id, 'Post_Validation')
    dag_id = dag_id[0][0]

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, task_name, 'Post_Validation', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    try:
        extra_path = os.environ['EXTRA_FOLDER']
        root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

        validation_reports_path = root_folder + '/' + 'Validation_Reports' + '/' + file_name.lower()
        if not os.path.exists(validation_reports_path):
            os.makedirs(validation_reports_path)

        xml_path = extra_path + '/' + 'Validation_Queries' + '/' + 'validation_queries.xml'
        tree = ET.parse(xml_path)
        root = tree.getroot()

        objects_list = ['Table']

        source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
        source_connection, error = DB_connection(source_DB_details)

        target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
        target_connection, error = target_DB_connection(target_DB_details)

        file_path = f'{validation_reports_path}/{schema_name}_{table_name}_report.xlsx'
        with pd.ExcelWriter(file_path) as writer:
            for object_type in objects_list:
                source_query_tag = 'Table_Validation_Queries/Source/' + '/' + object_type
                object_source_query = list(root.iterfind(source_query_tag))[0].text
                object_source_query = object_source_query.replace('@schemaname', schema_name.lower()).replace('@order',
                                                                                                              '').replace(
                    '@degree', str(source_DB_details['parallelprocess'])).replace('@tablename', table_name.upper())
                source_object_output = execute_query(source_connection, object_source_query)

                target_query_tag = 'Table_Validation_Queries/Target/' + '/' + object_type
                object_target_query = list(root.iterfind(target_query_tag))[0].text
                object_target_query = object_target_query.replace('@schemaname', target_schema.upper()).replace(
                    '@order',
                    '').replace(
                    '@degree', str(target_DB_details['parallelprocess'])).replace('@tablename', table_name.lower())
                target_object_output = execute_query(target_connection, object_target_query)

                source_data_df = pd.DataFrame(source_object_output,
                                              columns=['Schema_name', 'Table_Name', 'Table_Type'])
                target_data_df = pd.DataFrame(target_object_output,
                                              columns=['Schema_name', 'Table_Name', 'Table_Type'])
                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                source_data_df['Table_Type'] = source_data_df['Table_Type'].replace({'base table': 'table'})
                target_data_df['Table_Type'] = target_data_df['Table_Type'].replace({'base table': 'table'})

                matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Table_Type'],
                                        suffixes=('_Source', '_Target'), how='inner')

                matched_tables_list = matched_data['Table_Name'].values.tolist()
                table_counts_list = []
                for table_name in matched_tables_list:
                    source_row_count_query = f"select count(*) from {schema_name}.{table_name}"
                    target_row_count_query = f"select count(*) from {target_schema.upper()}.{table_name.upper()}"

                    source_row_count = execute_query(source_connection, source_row_count_query)[0][0]
                    target_row_count = execute_query(target_connection, target_row_count_query)[0][0]
                    difference_count = abs(source_row_count - target_row_count)

                    created_tuple = (
                        schema_name, target_schema, table_name, source_row_count, target_row_count, difference_count)
                    table_counts_list.append(created_tuple)

                table_count_df = pd.DataFrame(table_counts_list,
                                              columns=['Source_Schema', 'Target_Schema', 'Table_Name',
                                                       'Source_Row_Count', 'Target_Row_Count',
                                                       'Difference_Count'])
                table_count_df.to_excel(writer, sheet_name="Row_Counts", index=False)

                if table_counts_list:
                    table_counts_list = [t + (dag_id, config_id) for t in table_counts_list]
                    row_count_insert(project_connection, table_counts_list)

        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Success', None, task_end_time, None, None, None)

        dag_end_time = datetime.now()
        dag_update(project_connection, dag_id, 'Success', dag_end_time)
    except Exception as error:
        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        dag_update(project_connection, dag_id, 'Fail', dag_end_time)


def get_summary_objects_counts(result_df, object_name, schema_name, target_schema):
    source_name = 'Oracle'
    target_name = 'Postgres'

    summary_table_count_list = []
    if object_name != 'Datatype':
        source_table_count = len(result_df[(result_df[
                                                'Status'] == f"Available in both {source_name} and {target_name}") | (
                                                   result_df[
                                                       'Status'] == f"Available in {source_name} not in {target_name}")])
        target_table_count = len(result_df[(result_df[
                                                'Status'] == f"Available in both {source_name} and {target_name}") | (
                                                   result_df[
                                                       'Status'] == f"Available in {target_name} not in {source_name}")])
        difference_count = abs(source_table_count - target_table_count)
        summary_table_count_list.append(
            (schema_name, target_schema, object_name, source_table_count, target_table_count, difference_count))
    return summary_table_count_list


def complete_validation_trigger(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    file_name = kwargs['file_name']
    token_data = kwargs['token_data']
    process_type = kwargs['process_type']
    dag_name = kwargs['dag_id']
    task_name = kwargs['task_name']

    source_name = 'Oracle'
    target_name = 'Postgres'

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    config_data = get_config_id(project_connection, file_name, process_type)
    config_id = config_data[0][0]

    dag_id = dag_insert(project_connection, dag_name, 'Validation', schema_name, target_schema, None, None,
                        None, None, None, 'Running', config_id)
    dag_id = dag_id[0][0]

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, task_name, 'Complete_Validation', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    try:
        extra_path = os.environ['EXTRA_FOLDER']
        root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

        validation_reports_path = root_folder + '/' + 'Validation_Reports' + '/' + file_name.lower()
        if not os.path.exists(validation_reports_path):
            os.makedirs(validation_reports_path)

        xml_path = extra_path + '/' + 'Validation_Queries' + '/' + 'validation_queries.xml'
        tree = ET.parse(xml_path)
        root = tree.getroot()

        objects_list = ['Table', 'Partition', 'Datatype', 'Sequence', 'Primary_Key', 'Unique_Constraint', 'Foreign_Key',
                        'Not_Null_Constraint','Default_Constraint', 'Check_Constraint', 'Index']

        source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
        source_connection, error = DB_connection(source_DB_details)

        target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
        target_connection, error = target_DB_connection(target_DB_details)

        file_path = f'{validation_reports_path}/{schema_name}_complete_validation_report.xlsx'
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            final_summary_objects_list = []
            for object_type in objects_list:
                source_query_tag = 'Validation_Queries/Source/' + '/' + object_type
                object_source_query = list(root.iterfind(source_query_tag))[0].text
                object_source_query = object_source_query.replace('@schemaname', schema_name.lower()).replace('@order',
                                                                                                              '').replace(
                    '@degree', str(source_DB_details['parallelprocess']))
                source_object_output = execute_query(source_connection, object_source_query)

                target_query_tag = 'Validation_Queries/Target/' + '/' + object_type
                object_target_query = list(root.iterfind(target_query_tag))[0].text
                object_target_query = object_target_query.replace('@schemaname', target_schema.upper()).replace(
                    '@order',
                    '').replace(
                    '@degree', str(target_DB_details['parallelprocess']))
                target_object_output = execute_query(target_connection, object_target_query)

                if object_type == "Table":
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Source_Schema_Name', 'Table_Name', 'Table_Type'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Target_Schema_Name', 'Table_Name', 'Table_Type'])
                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    source_data_df['Table_Type'] = source_data_df['Table_Type'].replace({'base table': 'table'})
                    target_data_df['Table_Type'] = target_data_df['Table_Type'].replace({'base table': 'table'})

                    matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Table_Type'],
                                            suffixes=('_Source', '_Target'), how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                    source_data = source_data_df[
                        ~source_data_df['Table_Name'].isin(matched_data['Table_Name'])]
                    target_data = target_data_df[
                        ~target_data_df['Table_Name'].isin(matched_data['Table_Name'])]

                    source_only_data = pd.merge(source_data, target_data,
                                                on=['Table_Name', 'Table_Type'], suffixes=('_Source', '_Target'),
                                                how='left', indicator=True)
                    source_only_data = source_only_data[source_only_data['_merge'] == 'left_only'].drop(
                        columns=['_merge'])
                    source_only_data['Status'] = f"Available in {source_name} not in {target_name}"

                    target_only_data = pd.merge(source_data, target_data,
                                                on=['Table_Name', 'Table_Type'], suffixes=('_Source', '_Target'),
                                                how='right', indicator=True)
                    target_only_data = target_only_data[target_only_data['_merge'] == 'right_only'].drop(
                        columns=['_merge'])
                    target_only_data['Status'] = f"Available in {target_name} not in {source_name}"

                    result_df = pd.concat([matched_data, source_only_data, target_only_data], ignore_index=True)
                    result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                    matched_tables_list = matched_data['Table_Name'].values.tolist()
                    table_counts_list, table_row_count_list = [], []
                    for table_name in matched_tables_list:
                        source_column_count_query = f"select COLUMN_NAME, DATA_TYPE from ALL_TAB_COLUMNS where upper(OWNER) = '{schema_name.upper()}' and upper(TABLE_NAME) = '{table_name.upper()}' ORDER BY COLUMN_ID"
                        target_column_count_query = f"select column_name, data_type from information_schema.columns where upper(table_schema) = '{target_schema.upper()}' and UPPER(table_name) = '{table_name.upper()}' order by ordinal_position "

                        source_column_data = execute_query(source_connection, source_column_count_query)
                        target_column_data = execute_query(target_connection, target_column_count_query)
                        difference_count = abs(len(source_column_data) - len(target_column_data))

                        created_tuple = (
                            schema_name, target_schema, table_name, len(source_column_data), len(target_column_data),
                            difference_count)
                        table_counts_list.append(created_tuple)

                        source_row_count_query = f"select count(*) from {schema_name}.{table_name}"
                        target_row_count_query = f"select count(*) from {target_schema.upper()}.{table_name.upper()}"

                        source_row_count = execute_query(source_connection, source_row_count_query)[0][0]
                        target_row_count = execute_query(target_connection, target_row_count_query)[0][0]
                        difference_count = abs(source_row_count - target_row_count)

                        created_tuple = (
                            schema_name, target_schema, table_name, source_row_count, target_row_count,
                            difference_count)
                        table_counts_list.append(created_tuple)

                    table_count_df = pd.DataFrame(table_counts_list,
                                                  columns=['Source_Schema', 'Target_Schema', 'Table_Name',
                                                           'Source_Column_Count', 'Target_Column_Count',
                                                           'Difference_Count'])
                    table_count_df.to_excel(writer, sheet_name="Column_Counts", index=False)

                    table_count_df = pd.DataFrame(table_counts_list,
                                                  columns=['Source_Schema', 'Target_Schema', 'Table_Name',
                                                           'Source_Row_Count', 'Target_Row_Count',
                                                           'Difference_Count'])
                    table_count_df.to_excel(writer, sheet_name="Row_Counts", index=False)

                elif object_type == 'Partition':
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Schema_name', 'Table_Name', 'Partition_Name'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Schema_name', 'Table_Name', 'Partition_Name'])

                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                    source_data_df['Partition_Name'] = source_data_df.apply(
                        lambda row: f"{row['Table_Name']}_{row['Partition_Name']}" if isinstance(row['Partition_Name'],
                                                                                                 str) else row[
                            'Partition_Name'], axis=1)

                    if not source_data_df.empty or not target_data_df.empty:
                        matched_data = pd.merge(source_data_df, target_data_df,
                                                on=['Table_Name', 'Partition_Name'],
                                                suffixes=('_Source', '_Target'), how='inner')
                        matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                        source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Partition_Name'],
                                               suffixes=('_Source', '_Target'), how='left', indicator=True)
                        source_data = source_data[source_data['_merge'] == 'left_only']
                        source_data['Status'] = f"Available in {source_name} not in {target_name}"

                        target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Partition_Name'],
                                               suffixes=('_Source', '_Target'), how='right', indicator=True)
                        target_data = target_data[target_data['_merge'] == 'right_only']
                        target_data['Status'] = f"Available in {target_name} not in {source_name}"

                        result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                        result_df = result_df.drop(columns=['_merge'], errors='ignore')
                        result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                elif object_type == 'Datatype':
                    datatypes_dict = {'char': 'character',
                                      'date': 'timestamp without time zone',
                                      'long': 'text',
                                      'long raw': 'bytea',
                                      'clob': 'text',
                                      'nclob': 'text',
                                      'blob': 'bytea',
                                      'bfile': 'bytea',
                                      'raw': 'bytea',
                                      'float': 'double precision',
                                      'urowid': 'oid',
                                      'rowid': 'oid',
                                      'timestamp': 'timestamp without time zone',
                                      'timestamp(6)': 'timestamp without time zone',
                                      'timestamp(6) with time zone': 'timestamp with time zone',
                                      'xmltype': 'xml',
                                      'binary_integer': 'integer',
                                      'pls_integer': 'integer',
                                      'timestamp with time zone': 'timestamp with time zone',
                                      'timestamp with local time zone': 'timestamp with time zone',
                                      'varchar2': 'character varying',
                                      'number': 'numeric',
                                      'nvarchar2': 'character varying',
                                      'obj_snsdetails': 'user-defined',
                                      'timestamp(8)': 'timestamp without time zone',
                                      'timestamp(8) with time zone': 'timestamp with time zone',
                                      'nchar': 'character',
                                      'oid': 'text'}
                    datatypes_dict = {key.lower(): value.lower() for key, value in datatypes_dict.items()}
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Source_Schema', 'Table_Name', 'Column_Name',
                                                           'Source_Datatype',
                                                           'Source_Datatype_Length', 'Source_Index',
                                                           'Table_Type'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Target_Schema', 'Table_Name', 'Column_Name',
                                                           'Target_Datatype',
                                                           'Target_Datatype_Length', 'Target_Index',
                                                           'Table_Type'])

                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    source_data_df['Table_Type'] = source_data_df['Table_Type'].replace({'base table': 'table'})
                    target_data_df['Table_Type'] = target_data_df['Table_Type'].replace({'base table': 'table'})
                    source_data_df['Source_Modified_Datatype'] = source_data_df['Source_Datatype'].replace(
                        datatypes_dict)
                    source_data_df['Source_Datatype_Length'] = source_data_df[
                        'Source_Datatype_Length'].str.replace('(', '', regex=False).str.replace(')', '',
                                                                                                regex=False)
                    target_data_df['Target_Modified_Datatype'] = target_data_df['Target_Datatype']
                    target_data_df['Target_Datatype_Length'] = target_data_df[
                        'Target_Datatype_Length'].str.replace('(', '', regex=False).str.replace(')', '',
                                                                                                regex=False)

                    merged_df = pd.merge(source_data_df, target_data_df,
                                         on=['Table_Name', 'Column_Name', 'Table_Type'], how='inner')

                    result_match = []
                    for index, row in merged_df.iterrows():
                        if row['Source_Modified_Datatype'] == row['Target_Modified_Datatype'] and row[
                            'Source_Index'] == \
                                row[
                                    'Target_Index'] and row['Source_Datatype_Length'] == row[
                            'Target_Datatype_Length']:
                            status = 'Matched'
                        elif (row['Source_Modified_Datatype'] == 'double precision' and row[
                            'Target_Modified_Datatype'] == 'double precision') and row[
                            'Target_Datatype_Length'] == '53':
                            status = 'Matched'
                        elif (row['Source_Modified_Datatype'] == 'numeric') and row['Target_Modified_Datatype'] in [
                            'smallint',
                            'bigint',
                            'integer',
                            'numeric']:
                            status = 'Matched'
                        else:
                            if (row['Source_Modified_Datatype'] == row['Target_Modified_Datatype']) and (
                                    row['Source_Index'] == row['Target_Index']) and (
                                    row['Source_Datatype_Length'] in ['', None, 'None', 'none'] or row[
                                'Target_Datatype_Length'] in ['', None, 'None', 'none']):
                                status = 'Matched'
                            else:
                                status = 'Not_Matched'

                        result_match.append(
                            (row['Source_Schema'], row['Target_Schema'], row['Table_Name'], row['Column_Name'],
                             row['Source_Datatype'],
                             row['Source_Datatype_Length'], row['Target_Datatype'],
                             row['Target_Datatype_Length'], row['Source_Index'], row['Target_Index'],
                             row['Table_Type'], status))

                    result_match_df = pd.DataFrame(result_match,
                                                   columns=['Schema_Name', 'Target_Schema', 'Table_Name', 'Column_Name',
                                                            'Source_Datatype',
                                                            'Source_Datatype_Length', 'Target_Datatype',
                                                            'Target_Datatype_Length',
                                                            'Source_Index', 'Target_Index', 'Table_Type', 'Status'])
                    result_match_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                elif (object_type == 'Primary_Key') or (object_type == 'Foreign_Key') or (
                        object_type == 'Check_Constraint') or (object_type == 'Unique_Constraint'):
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Schema_Name', 'Table_Name', object_type,
                                                           'Source_Constraint_Name',
                                                           'Source_' + object_type + '_Status'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Schema_Name', 'Table_Name', object_type,
                                                           'Target_Constraint_Name',
                                                           'Target_' + object_type + '_Status'])

                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                    matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                            suffixes=('_Source', '_Target'),
                                            how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                    source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                           suffixes=('_Source', '_Target'),
                                           how='left', indicator=True)
                    source_data = source_data[source_data['_merge'] == 'left_only'].drop(
                        columns=['Target_Constraint_Name', 'Target_' + object_type + '_Status', '_merge'])
                    source_data['Status'] = f"Available in {source_name} not in {target_name}"
                    source_data['Target_Constraint_Name'] = 'Nan'
                    source_data['Target_' + object_type + '_Status'] = 'Nan'

                    target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                           suffixes=('_Source', '_Target'),
                                           how='right', indicator=True)
                    target_data = target_data[target_data['_merge'] == 'right_only'].drop(
                        columns=['Source_Constraint_Name', 'Source_' + object_type + '_Status', '_merge'])
                    target_data['Status'] = f"Available in {target_name} not in {source_name}"
                    target_data['Source_Constraint_Name'] = 'Nan'
                    target_data['Source_' + object_type + '_Status'] = 'Nan'
                    result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                    result_df.rename(columns={'_merge': 'Status'}, inplace=True, errors='ignore')
                    result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                elif object_type == "Default_Constraint":
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Schema_Name', 'Table_Name', 'Constraint_Name',
                                                           'Source_Column_Name'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Schema_Name', 'Table_Name', 'Constraint_Name',
                                                           'Target_Column_Name'])
                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                    matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Constraint_Name'],
                                            suffixes=('_Source', '_Target'), how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                    source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Constraint_Name'],
                                           suffixes=('_Source', '_Target'), how='left', indicator=True)
                    source_data = source_data[source_data['_merge'] == 'left_only']
                    source_data['Status'] = f"Available in {source_name} not in {target_name}"
                    source_data['Target_Column_Name'] = 'Nan'

                    target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Constraint_Name'],
                                           suffixes=('_Source', '_Target'), how='right', indicator=True)
                    target_data = target_data[target_data['_merge'] == 'right_only']
                    target_data['Status'] = f"Available in {target_name} not in {source_name}"
                    target_data['Source_Column_Name'] = 'Nan'

                    result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                    result_df = result_df.drop(columns=['_merge'], errors='ignore')
                    result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                elif object_type == "Not_Null_Constraint":

                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Schema_name', 'Constraint_Name', 'Constraint_Value'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Schema_name', 'Constraint_Name', 'Constraint_Value'])
                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                    matched_data = pd.merge(source_data_df, target_data_df, on=['Constraint_Name', 'Constraint_Value'],
                                            suffixes=('_Source', '_Target'), how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                    source_data = pd.merge(source_data_df, target_data_df, on=['Constraint_Name', 'Constraint_Value'],
                                           suffixes=('_Source', '_Target'), how='left', indicator=True)
                    source_data = source_data[source_data['_merge'] == 'left_only'].drop(columns=['Schema_name_Target',
                                                                                                  '_merge'])
                    source_data['Status'] = f"Available in {source_name} not in {target_name}"

                    target_data = pd.merge(source_data_df, target_data_df, on=['Constraint_Name', 'Constraint_Value'],
                                           suffixes=('_Source', '_Target'), how='right', indicator=True)
                    target_data = target_data[target_data['_merge'] == 'right_only'].drop(
                        columns=['Schema_name_Source',
                                 '_merge'])
                    target_data['Status'] = f"Available in {target_name} not in {source_name}"
                    result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                    result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                elif object_type == 'View':
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Schema_Name', 'Table_Name'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Schema_Name', 'Table_Name'])
                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                    matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name'],
                                            suffixes=('_Source', '_Target'),
                                            how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                    source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name'],
                                           suffixes=('_Source', '_Target'),
                                           how='left', indicator=True)
                    source_data = source_data[source_data['_merge'] == 'left_only'].drop(columns=['_merge'])
                    source_data['Status'] = f"Available in {source_name} not in {target_name}"
                    source_data['Schema_Name_Target'] = 'Nan'

                    target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name'],
                                           suffixes=('_Source', '_Target'), how='right', indicator=True)
                    target_data = target_data[target_data['_merge'] == 'right_only'].drop(columns=['_merge'])
                    target_data['Status'] = f"Available in {target_name} not in {source_name}"
                    target_data['Schema_Name_Source'] = 'Nan'

                    result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                    result_df = result_df.drop(columns=['_merge'], errors='ignore')
                    result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                elif object_type == "Sequence":
                    source_data_df = pd.DataFrame(source_object_output,
                                                  columns=['Source_Schema_Name', 'Sequence_Name',
                                                           'Source_Sequence_Value'])
                    target_data_df = pd.DataFrame(target_object_output,
                                                  columns=['Target_Schema_Name', 'Sequence_Name',
                                                           'Target_Sequence_Value'])
                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    matched_data = pd.merge(source_data_df, target_data_df, on='Sequence_Name',
                                            suffixes=('_Source', '_Target'), how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                    source_data = pd.merge(source_data_df, target_data_df, on='Sequence_Name',
                                           suffixes=('_Source', '_Target'), how='left', indicator=True)
                    source_data = source_data[source_data['_merge'] == 'left_only']
                    source_data['Status'] = f"Available in {source_name} not in {target_name}"

                    target_data = pd.merge(source_data_df, target_data_df, on='Sequence_Name',
                                           suffixes=('_Source', '_Target'), how='right', indicator=True)
                    target_data = target_data[target_data['_merge'] == 'right_only']
                    target_data['Status'] = f"Available in {target_name} not in {source_name}"

                    result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                    result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                elif object_type == "Index":
                    source_data_df = pd.DataFrame(source_object_output)
                    if len(source_data_df):
                        source_data_df = source_data_df.loc[:, [0, 1, 2, 3, 6]]
                        source_data_df.columns = ['Schema_Name', 'Table_Name', 'Index_Name', 'Source_Index_Column',
                                                  'Source_Index_DDL']
                    else:
                        source_data_df = pd.DataFrame(
                            columns=['Schema_Name', 'Table_Name', 'Index_Name', 'Source_Index_Column',
                                     'Source_Index_DDL'])

                    target_data_df = pd.DataFrame(target_object_output)
                    if len(target_data_df):
                        target_data_df = target_data_df.loc[:, [0, 1, 2, 3, 6]]
                        target_data_df.columns = ['Schema_Name', 'Table_Name', 'Index_Name', 'Target_Index_Column',
                                                  'Target_Index_DDL']
                    else:
                        target_data_df = pd.DataFrame(
                            columns=['Schema_Name', 'Table_Name', 'Index_Name', 'Target_Index_Column',
                                     'Target_Index_DDL'])

                    source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                    target_data_df['Target_Index_Column'] = target_data_df['Target_Index_Column'].str.replace("['", '',
                                                                                                              regex=False).replace(
                        "']", '', regex=False).replace('["', '', regex=False).replace('"]', '', regex=False)
                    target_data_df['Index_Name'] = target_data_df['Index_Name'].str.replace('___idx1$', '', case=False,
                                                                                            regex=True)

                    matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Index_Name'],
                                            suffixes=('_Source', '_Target'), how='inner')
                    matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                    source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Index_Name'],
                                           suffixes=('_Source', '_Target'), how='left', indicator=True)
                    source_data = source_data[source_data['_merge'] == 'left_only'].drop(
                        columns=['Target_Index_Column', 'Target_Index_DDL', '_merge'])
                    source_data['Status'] = f"Available in {source_name} not in {target_name}"
                    source_data['Target_Index_Column'] = 'Nan'
                    source_data['Target_Index_DDL'] = 'Nan'

                    target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Index_Name'],
                                           suffixes=('_Source', '_Target'), how='right', indicator=True)
                    target_data = target_data[target_data['_merge'] == 'right_only'].drop(
                        columns=['Source_Index_Column', 'Source_Index_DDL', '_merge'])
                    target_data['Status'] = f"Available in {target_name} not in {source_name}"
                    target_data['Source_Index_Column'] = 'Nan'
                    target_data['Source_Index_DDL'] = 'Nan'

                    result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                    result_df = result_df.drop(columns=['_merge'], errors='ignore')
                    result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)
                else:
                    result_df = pd.DataFrame()

                inter_summary_objects_list = get_summary_objects_counts(result_df, object_type, schema_name,
                                                                        target_schema)
                final_summary_objects_list.extend(inter_summary_objects_list)

            final_summary_objects_df = pd.DataFrame(final_summary_objects_list,
                                                    columns=['Source_Schema', 'Target_Schema', 'Object_Name',
                                                             'Source_Object_Count', 'Target_Object_Count',
                                                             'Difference_Count'])

            final_summary_objects_df.to_excel(writer, sheet_name='Summary', index=False)

            workbook = writer.book
            sheets = workbook.sheetnames
            sheets.remove('Summary')
            sheets.insert(0, 'Summary')
            workbook._sheets = [workbook.get_sheet_by_name(sheet) for sheet in sheets]

        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Success', None, task_end_time, None, None, None)

        dag_end_time = datetime.now()
        dag_update(project_connection, dag_id, 'Success', dag_end_time)
    except Exception as error:
        task_end_time = datetime.now()
        task_update(project_connection, task_id, None, 'Fail', str(error), task_end_time, None, None, None)

        dag_end_time = datetime.now()
        dag_update(project_connection, dag_id, 'Fail', dag_end_time)


def validation_summary_trigger(ti, **kwargs):
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    process_type = kwargs['process_type']
    file_name = kwargs['file_name']

    extra_path = os.environ['EXTRA_FOLDER']
    root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

    validation_reports_path = root_folder + '/' + 'Data_Compare_Reports' + '/' + file_name.lower()
    summary_output_file = root_folder + '/' + 'Data_Compare_Reports' + '/' + 'Summary_~_' + file_name.lower() + '.xlsx'

    if process_type == 'Pre_Validation':
        sheet_names = ['Table', 'Partition', 'Datatype', 'Column_Counts']
    elif process_type == 'Post_Validation':
        sheet_names = ['Row_Counts']
    elif process_type == 'Complete_Validation':
        sheet_names = ['Summary', 'Table', 'Column_Counts', 'Row_Counts','Partition', 'Datatype', 'Sequence', 'Primary_Key', 'Unique_Constraint', 'Foreign_Key',
                        'Not_Null_Constraint','Default_Constraint', 'Check_Constraint', 'Index']
    else:
        sheet_names = []

    if not os.path.exists(validation_reports_path):
        print(f"Validation reports path does not exist: {validation_reports_path}")
        return

    validation_files_list = [f for f in os.listdir(validation_reports_path) if f.endswith('.xlsx')]
    if not validation_files_list:
        print(f"No Excel files found in: {validation_reports_path}")
        return

    validation_files_list = [validation_reports_path + '/' + file_i for file_i in validation_files_list]
    print(f"Found {len(validation_files_list)} Excel files to process")

    # Create output workbook
    output_wb = Workbook()
    if "Sheet" in output_wb.sheetnames:
        del output_wb["Sheet"]

    header_font = Font(bold=True, size=16)
    color_fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")

    # Process each sheet type
    for sheet_name in sheet_names:
        print(f"Processing sheet: {sheet_name}")
        output_ws = output_wb.create_sheet(title=sheet_name)

        # Add header for consolidated sheet
        header_cell = output_ws.cell(row=1, column=1, value=f"Consolidated {sheet_name}")
        header_cell.font = header_font
        header_cell.fill = color_fill

        start_row = 3
        header_copied = False

        # Loop through each file and concatenate data from this sheet
        for input_file in validation_files_list:
            try:
                print(f"  Processing file: {input_file.split('/')[-1]}")

                # Check if sheet exists in this file
                wb = load_workbook(input_file)
                if sheet_name not in wb.sheetnames:
                    print(f"    Sheet '{sheet_name}' not found in {input_file.split('/')[-1]}")
                    wb.close()
                    continue

                ws = wb[sheet_name]

                # Check if sheet has data
                data_found = False
                for row in ws.iter_rows(values_only=True):
                    if any(cell is not None for cell in row):
                        data_found = True
                        break

                if data_found:
                    print(f"    Found data in sheet '{sheet_name}', copying {ws.max_row} rows")

                    # Copy data from this sheet
                    for row_idx, row in enumerate(ws.iter_rows(values_only=False)):
                        # Skip header row if already copied
                        if row_idx == 0 and header_copied:
                            continue

                        # Skip empty rows
                        if is_empty_row(row):
                            continue

                        # Copy row data
                        for col_idx, cell in enumerate(row, start=1):
                            target_cell = output_ws.cell(row=start_row, column=col_idx)
                            copy_cell(cell, target_cell)

                        start_row += 1

                    header_copied = True

                    # Apply borders to the copied section
                    if ws.max_row > 0 and ws.max_column > 0:
                        complete_apply_borders(output_ws, start_row - ws.max_row, start_row - 1, 1, ws.max_column)
                else:
                    print(f"    No data found in sheet '{sheet_name}'")

                wb.close()

            except Exception as e:
                print(f"Error processing file {input_file}, sheet {sheet_name}: {str(e)}")
                continue

        # Add spacing between sections
        start_row += 2
        print(f"  Completed processing sheet '{sheet_name}' with {start_row - 3} total rows")

    # Create summary statistics
    print("Creating summary statistics...")
    final_list = []
    for input_file in validation_files_list:
        try:
            file_name_only = input_file.split('/')[-1].split('.')[0]
            created_list = [file_name_only]

            for sheet in sheet_names:
                try:
                    df = pd.read_excel(input_file, sheet_name=sheet)
                    data_length = len(df)
                    created_list.append(data_length)
                except Exception:
                    created_list.append(0)  # If sheet doesn't exist or error reading

            final_list.append(created_list)
        except Exception as e:
            print(f"Error creating summary for file {input_file}: {str(e)}")
            continue

    # Save the consolidated workbook first
    output_wb.save(summary_output_file)
    print(f"Consolidated workbook saved with {len(sheet_names)} sheets")

    # Create summary DataFrame and add it to the existing file
    if final_list:
        columns = ['File_Name'] + [f'{sheet}_Count' for sheet in sheet_names]
        summary_df = pd.DataFrame(final_list, columns=columns)

        # Add total row
        total_row = ['Total'] + [summary_df[col].sum() for col in columns[1:]]
        summary_df.loc[len(summary_df.index)] = total_row

        # Write summary to Excel (append to existing file)
        with pd.ExcelWriter(summary_output_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
            summary_df.to_excel(writer, sheet_name='Summary_Statistics', index=False)
            # Move Summary sheet to first position
            workbook = writer.book
            if 'Summary_Statistics' in workbook.sheetnames:
                summary_sheet = workbook['Summary_Statistics']
                workbook._sheets.remove(summary_sheet)
                workbook._sheets.insert(0, summary_sheet)

    print(f"Validation summary created successfully: {summary_output_file}")
    print(f"Total files processed: {len(validation_files_list)}")
    print(f"Total sheets consolidated: {len(sheet_names)}")


def sample_compare_trigger(**kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    table_name = kwargs['table_name']
    file_name = kwargs['file_name']
    token_data = kwargs['token_data']

    extra_path = os.environ['EXTRA_FOLDER']
    root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

    data_compare_reports_path = root_folder + '/' + 'Data_Compare_Reports' + '/' + file_name.lower()
    if not os.path.exists(data_compare_reports_path):
        os.makedirs(data_compare_reports_path)

    report_file_name = data_compare_reports_path + '/' + table_name.lower() + '.xlsx'

    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
    source_connection, error = DB_connection(source_DB_details)

    target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
    target_connection = psycopg2.connect(database=target_DB_details['db_name'],
                                         user=target_DB_details['name'],
                                         password=target_DB_details['password'],
                                         host=target_DB_details['host'], port=target_DB_details['port'],
                                         options=f"-c search_path={target_schema}")

    column_fetch_query = "SELECT COLUMN_NAME,data_type FROM ALL_TAB_COLUMNS WHERE OWNER||'.'||TABLE_NAME = '{0}.{1}'".format(
        schema_name.upper(), table_name.upper())

    columns_data = execute_query(source_connection, column_fetch_query)
    columns_data = [tuple(col) for col in columns_data]
    source_columns_list = [i[0] for i in columns_data]
    source_columns_string = ','.join(source_columns_list)

    source_row_count_query = f"select count(*) from {schema_name.upper()}.{table_name.upper()}"
    target_row_count_query = f"select count(*) from {target_schema}.{table_name}"
    source_row_count = execute_query(source_connection, source_row_count_query)[0][0]
    target_row_count = execute_query(target_connection, target_row_count_query)[0][0]

    if source_row_count > 0 and target_row_count > 0:
        with pd.ExcelWriter(report_file_name, engine="xlsxwriter") as writer:
            table_data_query = 'SELECT {0} FROM (SELECT  * FROM {1}.{2} ORDER BY DBMS_RANDOM.VALUE) WHERE ROWNUM <= 25'.format(
                source_columns_string,
                schema_name.upper(),
                table_name.upper())
            source_data_list = execute_query(source_connection, table_data_query)
            target_data_list = []
            for record in source_data_list:
                record_dict = dict(zip(columns_data, record))
                filtered_dict = {k: v for k, v in record_dict.items() if
                                 k[1] not in ['DATE', 'TIMESTAMP(6)'] and v is not None}
                where_string = " and ".join(
                    [f"{k[0]} = '{v}'" if isinstance(v, str) else f"{k[0]} = {v}" for k, v in filtered_dict.items()])
                target_data_query = "select {0} from {1}.{2} where {3}".format(source_columns_string, target_schema,
                                                                               table_name, where_string)

                target_record_data = execute_query(target_connection, target_data_query)
                if target_record_data:
                    target_data_list.append(target_record_data[0])
                else:
                    target_data_list.append(())
            source_dataframe = pd.DataFrame(source_data_list, columns=source_columns_list)
            target_dataframe = pd.DataFrame(target_data_list, columns=source_columns_list)
            target_dataframe = target_dataframe.applymap(lambda x: float(x) if isinstance(x, decimal.Decimal) else x)

            sample_df = source_dataframe.compare(target_dataframe, keep_shape=True, keep_equal=True)
            sample_df.columns = pd.MultiIndex.from_tuples(
                [(col[0], 'Source' if col[1] == 'self' else 'Target') for col in sample_df.columns])
            sample_df = sample_df.dropna(how="all")
            sample_df.to_excel(writer, sheet_name='Sample_Data')

            differences = source_dataframe.compare(target_dataframe)
            if not differences.empty:
                differences.columns = pd.MultiIndex.from_tuples(
                    [(col[0], 'Source' if col[1] == 'self' else 'Target') for col in differences.columns])
                differences = differences.dropna(how="all")
            else:
                differences = pd.DataFrame(columns=pd.MultiIndex.from_product(
                    [source_dataframe.columns, ['Source', 'Target']]))

            differences_sheet = source_dataframe.compare(target_dataframe, keep_equal=True)
            if not differences_sheet.empty:
                differences_sheet.columns = pd.MultiIndex.from_tuples(
                    [(col[0], 'Source' if col[1] == 'self' else 'Target') for col in differences_sheet.columns])
                differences_sheet = differences_sheet.notna().any(axis=1)
                differences_sheet_indices = differences_sheet[differences_sheet].index
                differences_sheet = sample_df.loc[differences_sheet_indices]
            else:
                differences_sheet = pd.DataFrame(columns=pd.MultiIndex.from_product(
                    [source_dataframe.columns, ['Source', 'Target']]))
            differences_sheet.to_excel(writer, sheet_name='Differences')

            final_diff = sample_df.iloc[differences.index.to_list()]
            missing_rows = source_dataframe[~source_dataframe.apply(tuple, 1).isin(target_dataframe.apply(tuple, 1))]
            extra_rows = target_dataframe[~target_dataframe.apply(tuple, 1).isin(source_dataframe.apply(tuple, 1))]

            missing_rows_filtered = missing_rows[~missing_rows.index.isin(final_diff.index)]
            missing_rows_filtered = missing_rows_filtered.dropna(how="all")
            missing_rows_filtered.to_excel(writer, sheet_name='Missing_Rows')

            extra_rows_filtered = extra_rows[~extra_rows.index.isin(final_diff.index)]
            extra_rows_filtered = extra_rows_filtered.dropna(how="all")
            extra_rows_filtered.to_excel(writer, sheet_name='Extra_Rows')

        wb = load_workbook(report_file_name)
        ws = wb['Sample_Data']
        highlight_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")

        for col in differences.columns.get_level_values(0).unique():
            col_index = list(sample_df.columns.get_level_values(0)).index(
                col) + 2
            for row in differences.index.values.tolist():
                excel_row = sample_df.index.get_loc(
                    row) + 4
                diff_value = differences.loc[row, col]
                if diff_value['Source'] not in [pd.NaT, ''] and pd.notna(diff_value['Source']) and diff_value[
                    'Target'] not in [pd.NaT, ''] and pd.notna(diff_value['Target']):
                    ws.cell(row=excel_row, column=col_index).fill = highlight_fill
                    ws.cell(row=excel_row, column=col_index + 1).fill = highlight_fill
        wb.save(report_file_name)
        wb.close()

        wb = load_workbook(report_file_name)
        ws = wb['Differences']
        highlight_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")

        for col in differences.columns.get_level_values(0).unique():
            col_index = list(differences_sheet.columns.get_level_values(0)).index(
                col) + 2
            for row in differences.index.values.tolist():
                excel_row = differences_sheet.index.get_loc(
                    row) + 4
                diff_value = differences.loc[row, col]
                if diff_value['Source'] not in [pd.NaT, ''] and pd.notna(diff_value['Source']) and diff_value[
                    'Target'] not in [pd.NaT, ''] and pd.notna(diff_value['Target']):
                    ws.cell(row=excel_row, column=col_index).fill = highlight_fill
                    ws.cell(row=excel_row, column=col_index + 1).fill = highlight_fill
        wb.save(report_file_name)
        wb.close()
    else:
        print(f'Either Source or Target count is 0')


def copy_cell(source_cell, target_cell):
    target_cell.value = source_cell.value
    if source_cell.has_style:
        target_cell.font = copy(source_cell.font)
        target_cell.border = copy(source_cell.border)
        target_cell.fill = copy(source_cell.fill)
        target_cell.number_format = source_cell.number_format
        target_cell.protection = copy(source_cell.protection)
        target_cell.alignment = copy(source_cell.alignment)


def sample_apply_borders(ws, start_row, end_row, start_col, end_col):
    thin_border = Border(left=Side(style='thin'),
                         right=Side(style='thin'),
                         top=Side(style='thin'),
                         bottom=Side(style='thin'))
    for row in ws.iter_rows(min_row=start_row + 1, max_row=end_row, min_col=start_col, max_col=end_col):
        for cell in row:
            cell.border = thin_border


def complete_apply_borders(ws, start_row, end_row, start_col, end_col):
    thin_border = Border(left=Side(style='thin'),
                         right=Side(style='thin'),
                         top=Side(style='thin'),
                         bottom=Side(style='thin'))
    for row in ws.iter_rows(min_row=start_row, max_row=end_row, min_col=start_col, max_col=end_col):
        for cell in row:
            cell.border = thin_border


def is_empty_row(row):
    return all(cell.value is None for cell in row)


def sample_compare_summary(**kwargs):
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    file_name = kwargs['file_name']

    extra_path = os.environ['EXTRA_FOLDER']
    root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

    data_compare_reports_path = root_folder + '/' + 'Data_Compare_Reports' + '/' + file_name.lower()

    summary_output_file = root_folder + '/' + 'Data_Compare_Reports' + '/' + 'Summary_~_' + file_name.lower() + '.xlsx'

    compare_files_list = os.listdir(data_compare_reports_path)
    compare_files_list = [data_compare_reports_path + '/' + file_i for file_i in compare_files_list]
    sheet_names = ['Sample_Data', 'Differences', 'Extra_Rows', 'Missing_Rows']

    output_wb = Workbook()

    header_font = Font(bold=True, size=16)
    color_fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")

    for input_file in compare_files_list:
        sheet_name = input_file.split('/')[-1].split('.')[0]
        output_ws = output_wb.create_sheet(title=sheet_name)

        start_row = 1
        for section in sheet_names:
            header_cell = output_ws.cell(row=start_row, column=1, value=section)
            header_cell.font = header_font
            header_cell.fill = color_fill

            start_row += 1

            wb = load_workbook(input_file)

            if section not in wb.sheetnames:
                continue

            ws = wb[section]
            for row in ws.iter_rows(values_only=False):
                for col_idx, cell in enumerate(row[1:], start=1):
                    target_cell = output_ws.cell(row=start_row, column=col_idx)
                    copy_cell(cell, target_cell)
                start_row += 1
            sample_apply_borders(output_ws, start_row - len(ws['A']) - 1, start_row - 1, 1, ws.max_column - 1)
            start_row += 3
        start_row += 1
    if "Sheet" in output_wb.sheetnames:
        del output_wb['Sheet']
    output_wb.save(summary_output_file)

    final_list = []
    for input_file in compare_files_list:
        created_list = [input_file.split('.')[0].split('/')[-1]]
        for sheet in sheet_names:
            df = pd.read_excel(input_file, sheet_name=sheet)
            data_length = len(df)
            if sheet in ['Sample_Data', 'Differences']:
                if data_length > 1:
                    data_length = data_length - 2
                else:
                    data_length = data_length - 1
            created_list.append(data_length)
        final_list.append(created_list)

    summary_df = pd.DataFrame(final_list,
                              columns=['Table_Name', 'Compare_Data_Count', 'Differences_Count',
                                       'Extra_Rows_Count', 'Missing_Rows_Count'])
    with pd.ExcelWriter(summary_output_file, engine='openpyxl', mode='a') as writer:
        summary_df.to_excel(writer, sheet_name='Summary', index=False)
        writer.book._sheets.insert(0, writer.book._sheets.pop(-1))

    # shutil.rmtree(data_compare_reports_path)


def detailed_compare_trigger(**kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    table_name = kwargs['table_name']
    file_name = kwargs['file_name']
    task_name = kwargs['task_name']
    compare_record_percentage = kwargs['compare_record_percentage']
    token_data = kwargs['token_data']

    source_query = kwargs['source_query']
    target_query = kwargs['target_query']
    primary_key = kwargs['primary_key']

    extra_path = os.environ['EXTRA_FOLDER']
    root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

    data_compare_reports_path = root_folder + '/' + 'Data_Compare_Reports' + '/' + file_name.lower() + '/' + schema_name.lower() + '_~_' + table_name.lower()
    if not os.path.exists(data_compare_reports_path):
        os.makedirs(data_compare_reports_path)

    report_file_name = data_compare_reports_path + '/' + task_name.lower() + '.xlsx'

    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
    source_connection, error = DB_connection(source_DB_details)

    target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
    target_connection = psycopg2.connect(database=target_DB_details['db_name'],
                                         user=target_DB_details['name'],
                                         password=target_DB_details['password'],
                                         host=target_DB_details['host'], port=target_DB_details['port'],
                                         options=f"-c search_path={target_schema}")

    column_fetch_query = "SELECT COLUMN_NAME,data_type FROM ALL_TAB_COLUMNS WHERE OWNER||'.'||TABLE_NAME = '{0}.{1}'".format(
        schema_name.upper(), table_name.upper())

    columns_data = execute_query(source_connection, column_fetch_query)
    source_columns_list = [i[0] for i in columns_data]

    source_row_count_query = f"select count(*) from {schema_name.upper()}.{table_name.upper()}"
    target_row_count_query = f"select count(*) from {target_schema}.{table_name}"
    source_row_count = execute_query(source_connection, source_row_count_query)[0][0]
    target_row_count = execute_query(target_connection, target_row_count_query)[0][0]

    if source_row_count >= 1 and target_row_count >= 1:
        with pd.ExcelWriter(report_file_name, engine="xlsxwriter") as writer:
            source_data_list = execute_query(source_connection, source_query)
            source_dataframe = pd.DataFrame(source_data_list)

            target_data_list = execute_query(target_connection, target_query)
            target_dataframe = pd.DataFrame(target_data_list)

            if primary_key in [None, '']:
                source_dataframe.columns += 1
                source_dataframe.insert(loc=0, column=0, value=source_dataframe.apply(lambda x: hash(tuple(x)), axis=1))
                source_dataframe = source_dataframe.sort_values(by=0)
                source_dataframe = source_dataframe.groupby(source_dataframe.columns.tolist(), as_index=False,
                                                            dropna=False).size()
                source_dataframe = source_dataframe.drop(source_dataframe.columns[[-1]], axis=1)
                source_columns_list.insert(0, 'hash_index')

                target_dataframe.columns += 1
                target_dataframe.insert(loc=0, column=0, value=target_dataframe.apply(lambda x: hash(tuple(x)), axis=1))
                target_dataframe = target_dataframe.sort_values(by=0)
                target_dataframe = target_dataframe.groupby(target_dataframe.columns.tolist(), as_index=False,
                                                            dropna=False).size()
                target_dataframe = target_dataframe.drop(target_dataframe.columns[[-1]], axis=1)

            source_dataframe.columns = source_columns_list
            target_dataframe.columns = source_columns_list

            if primary_key in [None, '']:
                source_dataframe.set_index('hash_index', inplace=True)
                target_dataframe.set_index('hash_index', inplace=True)

                source_columns_list.remove('hash_index')

                extra = target_dataframe[~target_dataframe.index.isin(source_dataframe.index)]
                missing = source_dataframe[~source_dataframe.index.isin(target_dataframe.index)]
            else:
                extra = target_dataframe[
                    ~target_dataframe.loc[:, primary_key].isin(source_dataframe.loc[:, primary_key])]
                missing = source_dataframe[
                    ~source_dataframe.loc[:, primary_key].isin(target_dataframe.loc[:, primary_key])]

            extra.columns = source_columns_list
            missing.columns = source_columns_list
            print(len(source_dataframe), '=========', len(target_dataframe))
            extra.to_excel(writer, sheet_name='Extra_Rows')
            missing.to_excel(writer, sheet_name='Missing_Rows')

            source_dataframe = source_dataframe.head(int((source_dataframe.shape[0] * compare_record_percentage) / 100))
            target_dataframe = target_dataframe.head(int((target_dataframe.shape[0] * compare_record_percentage) / 100))
            target_dataframe = target_dataframe.applymap(lambda x: float(x) if isinstance(x, decimal.Decimal) else x)

            source_dataframe = source_dataframe.drop(list(missing.index.values))
            target_dataframe = target_dataframe.drop(list(extra.index.values))

            if len(source_dataframe) > 1000000:
                source_dataframe = source_dataframe.head(1000000)
                target_dataframe = target_dataframe.head(1000000)
            sample_df = source_dataframe.compare(target_dataframe, keep_shape=True, keep_equal=True)
            sample_df.columns = pd.MultiIndex.from_tuples(
                [(col[0], 'Source' if col[1] == 'self' else 'Target') for col in sample_df.columns])
            sample_df = sample_df.dropna(how="all")
            sample_df.to_excel(writer, sheet_name='Sample_Data')

            differences = source_dataframe.compare(target_dataframe)
            if not differences.empty:
                differences.columns = pd.MultiIndex.from_tuples(
                    [(col[0], 'Source' if col[1] == 'self' else 'Target') for col in differences.columns])
                differences = differences.dropna(how="all")
            else:
                differences = pd.DataFrame(columns=pd.MultiIndex.from_product(
                    [source_dataframe.columns, ['Source', 'Target']]))

            differences_sheet = source_dataframe.compare(target_dataframe, keep_equal=True)
            if not differences_sheet.empty:
                differences_sheet.columns = pd.MultiIndex.from_tuples(
                    [(col[0], 'Source' if col[1] == 'self' else 'Target') for col in differences_sheet.columns])
                differences_sheet = differences_sheet.notna().any(axis=1)
                differences_sheet_indices = differences_sheet[differences_sheet].index
                differences_sheet = sample_df.loc[differences_sheet_indices]
            else:
                differences_sheet = pd.DataFrame(columns=pd.MultiIndex.from_product(
                    [source_dataframe.columns, ['Source', 'Target']]))
            differences_sheet.to_excel(writer, sheet_name='Differences')

        wb = load_workbook(report_file_name)
        ws = wb['Sample_Data']
        highlight_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")

        for col in differences.columns.get_level_values(0).unique():
            col_index = list(sample_df.columns.get_level_values(0)).index(
                col) + 2
            for row in differences.index.values.tolist():
                excel_row = sample_df.index.get_loc(
                    row) + 4
                diff_value = differences.loc[row, col]
                if diff_value['Source'] not in [pd.NaT, ''] and pd.notna(diff_value['Source']) and diff_value[
                    'Target'] not in [pd.NaT, ''] and pd.notna(diff_value['Target']):
                    ws.cell(row=excel_row, column=col_index).fill = highlight_fill
                    ws.cell(row=excel_row, column=col_index + 1).fill = highlight_fill
        wb.save(report_file_name)
        wb.close()

        wb = load_workbook(report_file_name)
        ws = wb['Differences']

        highlight_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")

        for col in differences.columns.get_level_values(0).unique():
            col_index = list(differences_sheet.columns.get_level_values(0)).index(
                col) + 2
            for row in differences.index.values.tolist():
                excel_row = differences_sheet.index.get_loc(
                    row) + 4
                diff_value = differences.loc[row, col]
                if diff_value['Source'] not in [pd.NaT, ''] and pd.notna(diff_value['Source']) and diff_value[
                    'Target'] not in [pd.NaT, ''] and pd.notna(diff_value['Target']):
                    ws.cell(row=excel_row, column=col_index).fill = highlight_fill
                    ws.cell(row=excel_row, column=col_index + 1).fill = highlight_fill
        wb.save(report_file_name)
        wb.close()
    else:
        print('Either Source or Target count is 0')


def detailed_compare_summary(**kwargs):
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    table_name = kwargs['table_name']
    file_name = kwargs['file_name']
    output_excel_rows = 100000

    extra_path = os.environ['EXTRA_FOLDER']
    root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

    data_compare_reports_path = root_folder + '/' + 'Data_Compare_Reports' + '/' + file_name.lower() + '/' + schema_name.lower() + '_~_' + table_name.lower()

    summary_output_file = root_folder + '/' + 'Data_Compare_Reports' + '/' + 'Summary_' + table_name.lower() + '_~_' + file_name.lower() + '.xlsx'

    compare_files_list = os.listdir(data_compare_reports_path)
    compare_files_list = [data_compare_reports_path + '/' + file_i for file_i in compare_files_list]
    sheet_names = ['Sample_Data', 'Differences', 'Extra_Rows', 'Missing_Rows']

    output_wb = Workbook()
    for sheet_name in sheet_names:
        sheet_record_count = 0
        if sheet_name in output_wb.sheetnames:
            output_ws = output_wb[sheet_name]
        else:
            output_ws = output_wb.create_sheet(title=sheet_name)

        start_row = 1
        header_copied = False

        for input_file in compare_files_list:
            wb = load_workbook(input_file)
            if sheet_name not in wb.sheetnames:
                continue
            ws = wb[sheet_name]
            data_found = False
            for row in ws.iter_rows(values_only=True):
                if any(cell is not None for cell in row):
                    data_found = True
                    break

            if data_found:
                for row_idx, row in enumerate(ws.iter_rows(values_only=False)):
                    if row_idx in [0, 1] and header_copied:
                        continue

                    if is_empty_row(row):
                        continue

                    for col_idx, cell in enumerate(row[1:], start=1):
                        target_cell = output_ws.cell(row=start_row, column=col_idx)
                        copy_cell(cell, target_cell)

                    start_row += 1
                    sheet_record_count += 1
                    if sheet_record_count >= output_excel_rows:
                        break
                header_copied = True

                complete_apply_borders(output_ws, start_row, start_row - 1, 1, ws.max_column - 1)
            if sheet_record_count >= output_excel_rows:
                break

    if "Sheet" in output_wb.sheetnames:
        del output_wb["Sheet"]
    output_wb.save(summary_output_file)

    final_list = []
    for input_file in compare_files_list:
        created_list = [input_file.split('.')[0].split('/')[-1]]
        for sheet in sheet_names:
            df = pd.read_excel(input_file, sheet_name=sheet)
            data_length = len(df)
            if sheet in ['Sample_Data', 'Differences']:
                if data_length > 1:
                    data_length = data_length - 2
                else:
                    data_length = data_length - 1
            created_list.append(data_length)
        final_list.append(created_list)

    summary_df = pd.DataFrame(final_list,
                              columns=['Table_Name', 'Compare_Data_Count', 'Differences_Count', 'Extra_Rows_Count',
                                       'Missing_Rows_Count'])
    total_row = ['Total', summary_df['Compare_Data_Count'].sum(), summary_df['Differences_Count'].sum(),
                 summary_df['Extra_Rows_Count'].sum(), summary_df['Missing_Rows_Count'].sum()]
    summary_df.loc[len(summary_df.index)] = total_row

    with pd.ExcelWriter(summary_output_file, engine='openpyxl', mode='a') as writer:
        summary_df.to_excel(writer, sheet_name='Summary', index=False)
        writer.book._sheets.insert(0, writer.book._sheets.pop(-1))

    # shutil.rmtree(data_compare_reports_path)
