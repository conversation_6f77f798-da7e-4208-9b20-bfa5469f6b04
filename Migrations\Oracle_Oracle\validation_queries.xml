<Queries>
    <Validation_Queries>
        <Source>
            <Storage>
                <Sequence>
                    select /*+ PARALLEL(@degree) */ DISTINCT sequence_owner,sequence_name, TO_NUMBER(LAST_NUMBER) -
                    TO_NUMBER(INCREMENT_BY) AS sequence_value
                    from all_sequences
                    where sequence_owner in (upper('@schemaname'))
                    AND sequence_name NOT LIKE '%$%'
                </Sequence>
                <Table>
                    WITH cte AS (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name,
                    CASE WHEN OBJECT_TYPE = 'TABLE'
                    THEN 'TEMPORARY TABLE'
                    ELSE OBJECT_TYPE END AS OBJECT_TYPE
                    FROM dba_objects
                    WHERE TEMPORARY = 'Y'
                    AND OBJECT_TYPE = 'TABLE'
                    UNION
                    SELECT /*+ PARALLEL(@degree) */ owner,object_name,
                    CASE WHEN count_mv = 2 THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS OBJECT_TYPE
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name, count(*) AS count_mv
                    FROM dba_objects
                    WHERE OBJECT_TYPE in ('MATERIALIZED VIEW','TABLE')
                    AND TEMPORARY = 'N'
                    GROUP BY owner, object_name)
                    )
                    SELECT /*+ PARALLEL(@degree) */ * FROM cte
                    WHERE owner = upper('@schemaname')
                    AND
                    OBJECT_NAME NOT LIKE 'MLOG$%' AND OBJECT_NAME NOT LIKE 'RUPD$%' and OBJECT_NAME not like '%$%'
                    AND OBJECT_NAME NOT LIKE 'SYS%'
                    ORDER BY 1,2
                </Table>
                <Partition>
                    SELECT DISTINCT /*+ PARALLEL(@degree) */
                    A.TABLE_OWNER,A.TABLE_NAME,A.PARTITION_NAME
                    FROM ALL_TAB_PARTITIONS A
                    JOIN ALL_TABLES B
                    ON A.TABLE_NAME = B.TABLE_NAME
                    WHERE TABLE_OWNER = upper('@schemaname')
                </Partition>
                <Primary_Key>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner,ac.table_name,ac.constraint_name,ccl.col_list,ac.status
                    from all_constraints ac,
                    (select /*+ PARALLEL(@degree) */ acc.owner, acc.table_name, acc.constraint_name,
                    listagg(acc.column_name,',') within group (order by acc.position) as col_list from all_cons_columns
                    acc
                    group by acc.owner, acc.table_name, acc.constraint_name ) ccl
                    where ac.owner = upper('@schemaname')
                    and ac.constraint_type = 'P'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name and ac.table_name not like '%$%'
                    order by ac.owner
                </Primary_Key>
                <Unique_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner,ac.table_name,ac.constraint_name,ccl.col_list,ac.status
                    from all_constraints ac,
                    (select /*+ PARALLEL(@degree) */ acc.owner, acc.table_name, acc.constraint_name,
                    listagg(acc.column_name,',') within group (order by acc.position) as col_list from all_cons_columns
                    acc
                    group by acc.owner, acc.table_name, acc.constraint_name ) ccl
                    where ac.owner = upper('@schemaname')
                    and ac.constraint_type = 'U'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name and ac.table_name not like '%$%'
                    order by ac.owner
                </Unique_Constraint>
                <Foreign_Key>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner schema_name,ac.table_name,ac.constraint_name,'FOREIGN KEY
                    (' || acc.column_name || ')' || ' REFERENCES ' ||
                    (SELECT /*+ PARALLEL(@degree) */ LISTAGG(alc.owner || '.' || alc.table_name || '(' ||
                    alc.column_name || ')', ', ')
                    WITHIN GROUP (ORDER BY alc.position)
                    FROM all_cons_columns alc
                    WHERE alc.constraint_name = ac.r_constraint_name AND alc.owner = ac.owner) || ';',ac.status
                    FROM all_constraints ac
                    JOIN all_cons_columns acc ON ac.owner = acc.owner
                    AND ac.table_name = acc.table_name
                    AND ac.constraint_name = acc.constraint_name
                    WHERE ac.owner in (upper('@schemaname'))
                    AND ac.constraint_type = 'R' and ac.table_name not like '%$%'
                    ORDER BY ac.owner
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ OWNER,TABLE_NAME,column_name
                    FROM all_tab_columns
                    WHERE nullable = 'N'
                    AND owner = upper('@schemaname') and TABLE_NAME not like '%$%'
                    order by owner, table_name, column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ OWNER,TABLE_NAME,column_name, data_default
                    FROM all_tab_columns
                    WHERE data_default IS NOT NULL AND TABLE_NAME not like '%$%'
                    AND owner = upper('@schemaname')
                </Default_Constraint>
                <Check_Constraint>
                    SELECT /*+ PARALLEL(@degree) */
                    ac.owner,ac.table_name,ac.constraint_name,ac.search_condition,ac.status
                    from all_constraints ac, all_cons_columns acc
                    where ac.constraint_type = 'C'
                    and ac.owner = acc.owner
                    and ac.table_name = acc.table_name
                    and ac.constraint_name = acc.constraint_name
                    AND ac.owner = upper('@schemaname')
                    AND (AC.TABLE_NAME||'.'||acc.column_name) NOT IN (SELECT /*+ PARALLEL(@degree) */ DISTINCT
                    (TABLE_NAME||'.'||COLUMN_NAME) FROM all_tab_columns WHERE all_tab_columns.nullable = 'N')
                    AND AC.TABLE_NAME NOT LIKE '%$%'
                    order by ac.owner
                </Check_Constraint>
                <Index>
                    SELECT /*+ PARALLEL(@degree) */
                    schema_name,
                    table_name,
                    index_name,
                    index_cols,
                    uniqueness,
                    constraint_type,
                    lower('create' || case when UNIQUENESS = 'NONUNIQUE' then ' index ' else ' UNIQUE index ' end ||
                    INDEX_NAME || ' on ' || schema_name || '.' || table_name || '(' || index_cols || ');') IDX_DEF,
                    lower(schema_name||'.'||table_name||'-'||index_name) oraConcat
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */ * from
                    (
                    WITH cols AS (
                    SELECT /*+ PARALLEL(@degree) */
                    idx.table_owner AS schema_name,
                    idx.table_name,
                    idx.index_name,
                    cols.column_name,
                    cols.column_position,
                    idx.uniqueness,
                    decode(cols.descend,'ASC','',' ' || cols.descend) descend
                    FROM
                    DBA_INDEXES idx,
                    DBA_IND_COLUMNS cols
                    WHERE
                    idx.owner = cols.index_owner
                    AND idx.table_name = cols.table_name
                    AND idx.index_name = cols.index_name
                    AND idx.table_owner = upper('@schemaname')
                    AND idx.table_name not like '%$%'),
                    expr AS (
                    SELECT /*+ PARALLEL(@degree) */
                    extractValue(xs.object_value,'/ROW/TABLE_NAME') AS table_name,
                    extractValue(xs.object_value,'/ROW/INDEX_NAME') AS index_name,
                    extractValue(xs.object_value,'/ROW/COLUMN_EXPRESSION') AS column_expression,
                    extractValue(xs.object_value,'/ROW/COLUMN_POSITION') AS column_position
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */
                    XMLTYPE(DBMS_XMLGEN.GETXML( 'SELECT table_name, index_name, column_expression, column_position FROM
                    DBA_IND_EXPRESSIONS WHERE table_name not like ''%$%'' '
                    || ' union all SELECT null, null, null, null FROM dual ')
                    ) AS xml FROM DUAL
                    ) x,
                    TABLE(XMLSEQUENCE(EXTRACT(x.xml,'/ROWSET/ROW'))) xs)
                    SELECT /*+ PARALLEL(@degree) */
                    cols.schema_name,
                    cols.table_name,
                    DO.OBJECT_TYPE AS table_type,
                    listagg(CASE
                    WHEN cols.column_name LIKE 'SYS_N%' THEN expr.column_expression || cols.descend
                    ELSE cols.column_name || cols.descend
                    END,
                    ', ') within group(
                    order by cols.column_position) as Index_Cols,
                    cols.index_name,
                    cols.uniqueness,
                    ac.constraint_type
                    FROM
                    cols
                    LEFT JOIN expr ON
                    cols.table_name = expr.table_name
                    AND cols.index_name = expr.index_name
                    AND cols.column_position = expr.column_position
                    LEFT JOIN all_constraints ac ON
                    cols.index_name = ac.constraint_name
                    AND cols.table_name = ac.table_name
                    AND ac.constraint_type = 'P'
                    AND ac.owner = cols.schema_name
                    LEFT JOIN DBA_OBJECTS DO ON
                    DO.OWNER = cols.schema_name
                    AND do.OBJECT_NAME = cols.table_name
                    AND do.object_type = 'MATERIALIZED VIEW'
                    LEFT JOIN dba_dependencies DD ON
                    do.OBJECT_NAME = dd.NAME
                    GROUP BY
                    cols.schema_name,
                    cols.table_name,
                    DO.OBJECT_TYPE,
                    cols.index_name,
                    cols.uniqueness,
                    ac.constraint_type
                    --, DBMS_METADATA.get_ddl ('INDEX', cols.index_name, cols.schema_name)
                    ORDER BY
                    cols.table_name,
                    cols.index_name,
                    cols.uniqueness)) res
                    WHERE upper(res.index_name) not in ( select upper(INDEX_NAME) from all_constraints where upper(owner
                    )= upper('@schemaname') and constraint_type in ( 'P', 'U'))
                    GROUP BY
                    res.schema_name,
                    res.table_name,
                    res.table_type,
                    res.index_cols,
                    res.index_name,
                    res.uniqueness,
                    res.constraint_type
                    ORDER BY 1,2
                </Index>
                <Synonym>
                    select /*+ PARALLEL(@degree) */ SYNONYM_NAME,TABLE_NAME,TABLE_OWNER from dba_synonyms where
                    owner=upper('@schemaname')
                </Synonym>
                <View>
                    SELECT /*+ PARALLEL(@degree) */ owner,view_name
                    FROM dba_views
                    WHERE owner = upper('@schemaname') and view_name not like '%$%'
                    order by view_name
                </View>
                <Datatype>
                    WITH cte AS (SELECT /*+ PARALLEL(@degree) */ * from(
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name,
                    CASE WHEN OBJECT_TYPE = 'TABLE'
                    THEN 'TEMPORARY TABLE'
                    ELSE OBJECT_TYPE END AS OBJECT_TYPE
                    FROM dba_objects
                    WHERE TEMPORARY = 'Y'
                    AND OBJECT_TYPE = 'TABLE'
                    UNION
                    SELECT /*+ PARALLEL(@degree) */ owner,object_name,
                    CASE WHEN count_mv = 2 THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS OBJECT_TYPE
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name, count(*) AS count_mv
                    FROM dba_objects
                    WHERE OBJECT_TYPE in ('MATERIALIZED VIEW','TABLE')
                    AND TEMPORARY = 'N'
                    GROUP BY owner, object_name)
                    )
                    WHERE owner = upper('@schemaname')
                    AND
                    OBJECT_NAME NOT LIKE 'MLOG$%' AND OBJECT_NAME NOT LIKE 'RUPD$%'
                    ORDER BY 1,2),
                    datatype as(SELECT /*+ PARALLEL(@degree) */ OWNER, TABLE_NAME, COLUMN_NAME,
                    DATA_TYPE,COLUMN_SIZE,COLUMN_ID FROM (
                    select /*+ PARALLEL(@degree) */ OWNER, TABLE_NAME, COLUMN_NAME, DATA_TYPE,
                    case when DATA_TYPE = 'NUMBER' AND DATA_PRECISION IS NULL AND DATA_SCALE IS NULL THEN
                    '('||to_char(DATA_LENGTH)||')'
                    WHEN DATA_TYPE IN ('VARCHAR2', 'NVARCHAR2', 'NCHAR' ) THEN '('||to_char(CHAR_LENGTH)||')'
                    when DATA_TYPE IN ('TIMESTAMP(8)', 'XMLTYPE','CHAR', 'CLOB', 'UROWID') THEN
                    '('||to_char(DATA_LENGTH)||')'
                    when DATA_TYPE IN ('DATE', 'LONG', 'TIMESTAMP(6) WITH TIME ZONE', 'TIMESTAMP(6)', 'ROWID', 'RAW',
                    'BLOB', 'OBJ_SNSDETAILS') THEN NULL
                    when DATA_TYPE = 'NUMBER' AND DATA_PRECISION IS NOT NULL AND DATA_SCALE IS NOT NULL THEN
                    '('||to_char(DATA_PRECISION)||','||to_char(DATA_SCALE)||')'
                    -- WHEN DATA_TYPE IN ('FLOAT') THEN NVL(DATA_PRECISION,'9999999')
                    END AS "COLUMN_SIZE", CHARACTER_SET_NAME
                    ,DATA_PRECISION, DATA_SCALE, CHAR_COL_DECL_LENGTH, DATA_LENGTH, COLUMN_ID
                    from all_tab_columns
                    where owner in upper('@schemaname')
                    order by owner, table_name, COLUMN_ID
                    )
                    )
                    SELECT /*+ PARALLEL(@degree) */ kk.owner,
                    kk.table_name,kk.column_name,kk.DATA_TYPE,kk.column_size,
                    kk.column_id,
                    cc.OBJECT_TYPE
                    FROM datatype kk
                    LEFT JOIN cte cc ON kk.owner = cc.owner
                    AND cc.object_name = kk.table_name
                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    SELECT /*+ PARALLEL(@degree) */
                    object_type,
                    CASE
                    WHEN object_type LIKE 'PACKAGE%' THEN object_name || '_' || Method_name
                    WHEN object_type = 'TYPE_PROCEDURE' THEN object_name || '_' || Method_name
                    WHEN object_type = 'TYPE_FUNCTION' THEN object_name || '_' || Method_name
                    WHEN object_type = 'PROCEDURE' THEN object_name
                    WHEN object_type = 'FUNCTION' THEN object_name
                    END AS Code_Object_Name
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */
                    CASE
                    WHEN p.object_type = 'PACKAGE' AND p.procedure_name IS NOT NULL AND EXISTS (
                    SELECT 1 FROM all_arguments a
                    WHERE a.owner = p.owner
                    AND a.package_name = p.object_name
                    AND a.object_name = p.procedure_name
                    AND a.argument_name IS NOT NULL
                    ) THEN 'PACKAGE_PROCEDURE'
                    WHEN p.object_type = 'PACKAGE' AND p.procedure_name IS NOT NULL THEN 'PACKAGE_FUNCTION'
                    WHEN p.object_type = 'TYPE' AND p.procedure_name IS NOT NULL THEN 'TYPE_PROCEDURE'
                    WHEN p.object_type = 'TYPE' AND p.procedure_name IS NULL THEN 'TYPE_FUNCTION'
                    ELSE p.object_type
                    END AS object_type,
                    p.object_name,
                    CASE
                    WHEN p.object_type IN ('PACKAGE', 'TYPE') THEN p.procedure_name
                    WHEN p.object_type IN ('PROCEDURE', 'FUNCTION') THEN p.object_name
                    END AS Method_name
                    FROM dba_procedures p
                    JOIN dba_objects o ON p.object_name = o.object_name AND p.object_type = o.object_type AND p.owner =
                    o.owner
                    WHERE p.owner = UPPER('@schemaname')
                    AND p.OBJECT_TYPE IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'TYPE')
                    AND o.status = 'VALID'
                    ) a
                    WHERE Method_name IS NOT NULL
                    ORDER BY object_type, Code_Object_Name
                </Code_Objects>
                <Trigger>
                    SELECT /*+ PARALLEL(@degree) */ table_owner,
                    table_name,
                    trigger_name
                    FROM DBA_TRIGGERS
                    WHERE owner = upper('@schemaname') and table_name not like '%$%'
                    ORDER BY table_name,
                    trigger_name
                </Trigger>
            </Code>
        </Source>
        <Target>
            <Storage>
                <Sequence>
                    select /*+ PARALLEL(@degree) */ DISTINCT sequence_owner,sequence_name, TO_NUMBER(LAST_NUMBER) -
                    TO_NUMBER(INCREMENT_BY) AS sequence_value
                    from all_sequences
                    where sequence_owner in (upper('@schemaname'))
                    AND sequence_name NOT LIKE '%$%'
                </Sequence>
                <Table>
                    WITH cte AS (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name,
                    CASE WHEN OBJECT_TYPE = 'TABLE'
                    THEN 'TEMPORARY TABLE'
                    ELSE OBJECT_TYPE END AS OBJECT_TYPE
                    FROM dba_objects
                    WHERE TEMPORARY = 'Y'
                    AND OBJECT_TYPE = 'TABLE'
                    UNION
                    SELECT /*+ PARALLEL(@degree) */ owner,object_name,
                    CASE WHEN count_mv = 2 THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS OBJECT_TYPE
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name, count(*) AS count_mv
                    FROM dba_objects
                    WHERE OBJECT_TYPE in ('MATERIALIZED VIEW','TABLE')
                    AND TEMPORARY = 'N'
                    GROUP BY owner, object_name)
                    )
                    SELECT /*+ PARALLEL(@degree) */ * FROM cte
                    WHERE owner = upper('@schemaname')
                    AND
                    OBJECT_NAME NOT LIKE 'MLOG$%' AND OBJECT_NAME NOT LIKE 'RUPD$%' and OBJECT_NAME not like '%$%'
                    AND OBJECT_NAME NOT LIKE 'SYS%'
                    ORDER BY 1,2
                </Table>
                <Partition>
                    SELECT DISTINCT /*+ PARALLEL(@degree) */
                    A.TABLE_OWNER,A.TABLE_NAME,A.PARTITION_NAME
                    FROM ALL_TAB_PARTITIONS A
                    JOIN ALL_TABLES B
                    ON A.TABLE_NAME = B.TABLE_NAME
                    WHERE TABLE_OWNER = upper('@schemaname')
                </Partition>
                <Primary_Key>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner,ac.table_name,ac.constraint_name,ccl.col_list,ac.status
                    from all_constraints ac,
                    (select /*+ PARALLEL(@degree) */ acc.owner, acc.table_name, acc.constraint_name,
                    listagg(acc.column_name,',') within group (order by acc.position) as col_list from all_cons_columns
                    acc
                    group by acc.owner, acc.table_name, acc.constraint_name ) ccl
                    where ac.owner = upper('@schemaname')
                    and ac.constraint_type = 'P'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name and ac.table_name not like '%$%'
                    order by ac.owner
                </Primary_Key>
                <Unique_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner,ac.table_name,ac.constraint_name,ccl.col_list,ac.status
                    from all_constraints ac,
                    (select /*+ PARALLEL(@degree) */ acc.owner, acc.table_name, acc.constraint_name,
                    listagg(acc.column_name,',') within group (order by acc.position) as col_list from all_cons_columns
                    acc
                    group by acc.owner, acc.table_name, acc.constraint_name ) ccl
                    where ac.owner = upper('@schemaname')
                    and ac.constraint_type = 'U'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name and ac.table_name not like '%$%'
                    order by ac.owner
                </Unique_Constraint>
                <Foreign_Key>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner schema_name,ac.table_name,ac.constraint_name,'FOREIGN KEY
                    (' || acc.column_name || ')' || ' REFERENCES ' ||
                    (SELECT /*+ PARALLEL(@degree) */ LISTAGG(alc.owner || '.' || alc.table_name || '(' ||
                    alc.column_name || ')', ', ')
                    WITHIN GROUP (ORDER BY alc.position)
                    FROM all_cons_columns alc
                    WHERE alc.constraint_name = ac.r_constraint_name AND alc.owner = ac.owner) || ';',ac.status
                    FROM all_constraints ac
                    JOIN all_cons_columns acc ON ac.owner = acc.owner
                    AND ac.table_name = acc.table_name
                    AND ac.constraint_name = acc.constraint_name
                    WHERE ac.owner in (upper('@schemaname'))
                    AND ac.constraint_type = 'R' and ac.table_name not like '%$%'
                    ORDER BY ac.owner
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ OWNER,TABLE_NAME,column_name
                    FROM all_tab_columns
                    WHERE nullable = 'N'
                    AND owner = upper('@schemaname') and TABLE_NAME not like '%$%'
                    order by owner, table_name, column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ OWNER,TABLE_NAME,column_name, data_default
                    FROM all_tab_columns
                    WHERE data_default IS NOT NULL AND TABLE_NAME not like '%$%'
                    AND owner = upper('@schemaname')
                </Default_Constraint>
                <Check_Constraint>
                    SELECT /*+ PARALLEL(@degree) */
                    ac.owner,ac.table_name,ac.constraint_name,ac.search_condition,ac.status
                    from all_constraints ac, all_cons_columns acc
                    where ac.constraint_type = 'C'
                    and ac.owner = acc.owner
                    and ac.table_name = acc.table_name
                    and ac.constraint_name = acc.constraint_name
                    AND ac.owner = upper('@schemaname')
                    AND (AC.TABLE_NAME||'.'||acc.column_name) NOT IN (SELECT /*+ PARALLEL(@degree) */ DISTINCT
                    (TABLE_NAME||'.'||COLUMN_NAME) FROM all_tab_columns WHERE all_tab_columns.nullable = 'N')
                    AND AC.TABLE_NAME NOT LIKE '%$%'
                    order by ac.owner
                </Check_Constraint>
                <Index>
                    SELECT /*+ PARALLEL(@degree) */
                    schema_name,
                    table_name,
                    index_name,
                    index_cols,
                    uniqueness,
                    constraint_type,
                    lower('create' || case when UNIQUENESS = 'NONUNIQUE' then ' index ' else ' UNIQUE index ' end ||
                    INDEX_NAME || ' on ' || schema_name || '.' || table_name || '(' || index_cols || ');') IDX_DEF,
                    lower(schema_name||'.'||table_name||'-'||index_name) oraConcat
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */ * from
                    (
                    WITH cols AS (
                    SELECT /*+ PARALLEL(@degree) */
                    idx.table_owner AS schema_name,
                    idx.table_name,
                    idx.index_name,
                    cols.column_name,
                    cols.column_position,
                    idx.uniqueness,
                    decode(cols.descend,'ASC','',' ' || cols.descend) descend
                    FROM
                    DBA_INDEXES idx,
                    DBA_IND_COLUMNS cols
                    WHERE
                    idx.owner = cols.index_owner
                    AND idx.table_name = cols.table_name
                    AND idx.index_name = cols.index_name
                    AND idx.table_owner = upper('@schemaname')
                    AND idx.table_name not like '%$%'),
                    expr AS (
                    SELECT /*+ PARALLEL(@degree) */
                    extractValue(xs.object_value,'/ROW/TABLE_NAME') AS table_name,
                    extractValue(xs.object_value,'/ROW/INDEX_NAME') AS index_name,
                    extractValue(xs.object_value,'/ROW/COLUMN_EXPRESSION') AS column_expression,
                    extractValue(xs.object_value,'/ROW/COLUMN_POSITION') AS column_position
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */
                    XMLTYPE(DBMS_XMLGEN.GETXML( 'SELECT table_name, index_name, column_expression, column_position FROM
                    DBA_IND_EXPRESSIONS WHERE table_name not like ''%$%'' '
                    || ' union all SELECT null, null, null, null FROM dual ')
                    ) AS xml FROM DUAL
                    ) x,
                    TABLE(XMLSEQUENCE(EXTRACT(x.xml,'/ROWSET/ROW'))) xs)
                    SELECT /*+ PARALLEL(@degree) */
                    cols.schema_name,
                    cols.table_name,
                    DO.OBJECT_TYPE AS table_type,
                    listagg(CASE
                    WHEN cols.column_name LIKE 'SYS_N%' THEN expr.column_expression || cols.descend
                    ELSE cols.column_name || cols.descend
                    END,
                    ', ') within group(
                    order by cols.column_position) as Index_Cols,
                    cols.index_name,
                    cols.uniqueness,
                    ac.constraint_type
                    FROM
                    cols
                    LEFT JOIN expr ON
                    cols.table_name = expr.table_name
                    AND cols.index_name = expr.index_name
                    AND cols.column_position = expr.column_position
                    LEFT JOIN all_constraints ac ON
                    cols.index_name = ac.constraint_name
                    AND cols.table_name = ac.table_name
                    AND ac.constraint_type = 'P'
                    AND ac.owner = cols.schema_name
                    LEFT JOIN DBA_OBJECTS DO ON
                    DO.OWNER = cols.schema_name
                    AND do.OBJECT_NAME = cols.table_name
                    AND do.object_type = 'MATERIALIZED VIEW'
                    LEFT JOIN dba_dependencies DD ON
                    do.OBJECT_NAME = dd.NAME
                    GROUP BY
                    cols.schema_name,
                    cols.table_name,
                    DO.OBJECT_TYPE,
                    cols.index_name,
                    cols.uniqueness,
                    ac.constraint_type
                    --, DBMS_METADATA.get_ddl ('INDEX', cols.index_name, cols.schema_name)
                    ORDER BY
                    cols.table_name,
                    cols.index_name,
                    cols.uniqueness)) res
                    WHERE upper(res.index_name) not in ( select upper(INDEX_NAME) from all_constraints where upper(owner
                    )= upper('@schemaname') and constraint_type in ( 'P', 'U'))
                    GROUP BY
                    res.schema_name,
                    res.table_name,
                    res.table_type,
                    res.index_cols,
                    res.index_name,
                    res.uniqueness,
                    res.constraint_type
                    ORDER BY 1,2
                </Index>
                <Synonym>
                    select /*+ PARALLEL(@degree) */ SYNONYM_NAME,TABLE_NAME,TABLE_OWNER from dba_synonyms where
                    owner=upper('@schemaname')
                </Synonym>
                <View>
                    SELECT /*+ PARALLEL(@degree) */ owner,view_name
                    FROM dba_views
                    WHERE owner = upper('@schemaname') and view_name not like '%$%'
                    order by view_name
                </View>
                <Datatype>
                    WITH cte AS (SELECT /*+ PARALLEL(@degree) */ * from(
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name,
                    CASE WHEN OBJECT_TYPE = 'TABLE'
                    THEN 'TEMPORARY TABLE'
                    ELSE OBJECT_TYPE END AS OBJECT_TYPE
                    FROM dba_objects
                    WHERE TEMPORARY = 'Y'
                    AND OBJECT_TYPE = 'TABLE'
                    UNION
                    SELECT /*+ PARALLEL(@degree) */ owner,object_name,
                    CASE WHEN count_mv = 2 THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS OBJECT_TYPE
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name, count(*) AS count_mv
                    FROM dba_objects
                    WHERE OBJECT_TYPE in ('MATERIALIZED VIEW','TABLE')
                    AND TEMPORARY = 'N'
                    GROUP BY owner, object_name)
                    )
                    WHERE owner = upper('@schemaname')
                    AND
                    OBJECT_NAME NOT LIKE 'MLOG$%' AND OBJECT_NAME NOT LIKE 'RUPD$%'
                    ORDER BY 1,2),
                    datatype as(SELECT /*+ PARALLEL(@degree) */ OWNER, TABLE_NAME, COLUMN_NAME,
                    DATA_TYPE,COLUMN_SIZE,COLUMN_ID FROM (
                    select /*+ PARALLEL(@degree) */ OWNER, TABLE_NAME, COLUMN_NAME, DATA_TYPE,
                    case when DATA_TYPE = 'NUMBER' AND DATA_PRECISION IS NULL AND DATA_SCALE IS NULL THEN
                    '('||to_char(DATA_LENGTH)||')'
                    WHEN DATA_TYPE IN ('VARCHAR2', 'NVARCHAR2', 'NCHAR' ) THEN '('||to_char(CHAR_LENGTH)||')'
                    when DATA_TYPE IN ('TIMESTAMP(8)', 'XMLTYPE','CHAR', 'CLOB', 'UROWID') THEN
                    '('||to_char(DATA_LENGTH)||')'
                    when DATA_TYPE IN ('DATE', 'LONG', 'TIMESTAMP(6) WITH TIME ZONE', 'TIMESTAMP(6)', 'ROWID', 'RAW',
                    'BLOB', 'OBJ_SNSDETAILS') THEN NULL
                    when DATA_TYPE = 'NUMBER' AND DATA_PRECISION IS NOT NULL AND DATA_SCALE IS NOT NULL THEN
                    '('||to_char(DATA_PRECISION)||','||to_char(DATA_SCALE)||')'
                    -- WHEN DATA_TYPE IN ('FLOAT') THEN NVL(DATA_PRECISION,'9999999')
                    END AS "COLUMN_SIZE", CHARACTER_SET_NAME
                    ,DATA_PRECISION, DATA_SCALE, CHAR_COL_DECL_LENGTH, DATA_LENGTH, COLUMN_ID
                    from all_tab_columns
                    where owner in upper('@schemaname')
                    order by owner, table_name, COLUMN_ID
                    )
                    )
                    SELECT /*+ PARALLEL(@degree) */ kk.owner,
                    kk.table_name,kk.column_name,kk.DATA_TYPE,kk.column_size,
                    kk.column_id,
                    cc.OBJECT_TYPE
                    FROM datatype kk
                    LEFT JOIN cte cc ON kk.owner = cc.owner
                    AND cc.object_name = kk.table_name
                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    SELECT /*+ PARALLEL(@degree) */
                    object_type,
                    CASE
                    WHEN object_type LIKE 'PACKAGE%' THEN object_name || '_' || Method_name
                    WHEN object_type = 'TYPE_PROCEDURE' THEN object_name || '_' || Method_name
                    WHEN object_type = 'TYPE_FUNCTION' THEN object_name || '_' || Method_name
                    WHEN object_type = 'PROCEDURE' THEN object_name
                    WHEN object_type = 'FUNCTION' THEN object_name
                    END AS Code_Object_Name
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */
                    CASE
                    WHEN p.object_type = 'PACKAGE' AND p.procedure_name IS NOT NULL AND EXISTS (
                    SELECT 1 FROM all_arguments a
                    WHERE a.owner = p.owner
                    AND a.package_name = p.object_name
                    AND a.object_name = p.procedure_name
                    AND a.argument_name IS NOT NULL
                    ) THEN 'PACKAGE_PROCEDURE'
                    WHEN p.object_type = 'PACKAGE' AND p.procedure_name IS NOT NULL THEN 'PACKAGE_FUNCTION'
                    WHEN p.object_type = 'TYPE' AND p.procedure_name IS NOT NULL THEN 'TYPE_PROCEDURE'
                    WHEN p.object_type = 'TYPE' AND p.procedure_name IS NULL THEN 'TYPE_FUNCTION'
                    ELSE p.object_type
                    END AS object_type,
                    p.object_name,
                    CASE
                    WHEN p.object_type IN ('PACKAGE', 'TYPE') THEN p.procedure_name
                    WHEN p.object_type IN ('PROCEDURE', 'FUNCTION') THEN p.object_name
                    END AS Method_name
                    FROM dba_procedures p
                    JOIN dba_objects o ON p.object_name = o.object_name AND p.object_type = o.object_type AND p.owner =
                    o.owner
                    WHERE p.owner = UPPER('@schemaname')
                    AND p.OBJECT_TYPE IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'TYPE')
                    AND o.status = 'VALID'
                    ) a
                    WHERE Method_name IS NOT NULL
                    ORDER BY object_type, Code_Object_Name
                </Code_Objects>
                <Trigger>
                    SELECT /*+ PARALLEL(@degree) */ table_owner,
                    table_name,
                    trigger_name
                    FROM DBA_TRIGGERS
                    WHERE owner = upper('@schemaname') and table_name not like '%$%'
                    ORDER BY table_name,
                    trigger_name
                </Trigger>
            </Code>
        </Target>
    </Validation_Queries>
    <Table_Validation_Queries>
        <Source>
            <Storage>
                <Sequence>
                    select /*+ PARALLEL(@degree) */ DISTINCT sequence_owner,sequence_name, TO_NUMBER(LAST_NUMBER) -
                    TO_NUMBER(INCREMENT_BY) AS sequence_value
                    from all_sequences
                    where sequence_owner in (lower('@schemaname'))
                </Sequence>
                <Table>
                    WITH cte AS (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name,
                    CASE WHEN OBJECT_TYPE = 'TABLE'
                    THEN 'TEMPORARY TABLE'
                    ELSE OBJECT_TYPE END AS OBJECT_TYPE
                    FROM dba_objects
                    WHERE TEMPORARY = 'Y'
                    AND OBJECT_TYPE = 'TABLE'
                    UNION
                    SELECT /*+ PARALLEL(@degree) */ owner,object_name,
                    CASE WHEN count_mv = 2 THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS OBJECT_TYPE
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name, count(*) AS count_mv
                    FROM dba_objects
                    WHERE OBJECT_TYPE in ('MATERIALIZED VIEW','TABLE')
                    AND TEMPORARY = 'N'
                    GROUP BY owner, object_name)
                    )
                    SELECT /*+ PARALLEL(@degree) */ * FROM cte
                    WHERE owner = upper('@schemaname')
                    AND
                    OBJECT_NAME NOT LIKE 'MLOG$%' AND OBJECT_NAME NOT LIKE 'RUPD$%'
                    ORDER BY 1,2
                </Table>
                <Partition>
                    SELECT DISTINCT /*+ PARALLEL(@degree) */
                    A.TABLE_OWNER,A.TABLE_NAME,A.PARTITION_NAME
                    FROM ALL_TAB_PARTITIONS A
                    JOIN ALL_TABLES B
                    ON A.TABLE_NAME = B.TABLE_NAME
                    WHERE TABLE_OWNER = upper('@schemaname') and A.TABLE_NAME = ('@tablename')
                </Partition>
                <Primary_Key>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner,ac.table_name,ac.constraint_name,ccl.col_list,ac.status
                    from all_constraints ac,
                    (select /*+ PARALLEL(@degree) */ acc.owner, acc.table_name, acc.constraint_name,
                    listagg(acc.column_name,',') within group (order by acc.position) as col_list from all_cons_columns
                    acc
                    group by acc.owner, acc.table_name, acc.constraint_name ) ccl
                    where ac.owner = upper('@schemaname')
                    and ac.table_name in ('@tablename')
                    and ac.constraint_type = 'P'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name
                    order by ac.owner
                </Primary_Key>
                <Unique_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner,ac.table_name,ac.constraint_name,ccl.col_list,ac.status
                    from all_constraints ac,
                    (select /*+ PARALLEL(@degree) */ acc.owner, acc.table_name, acc.constraint_name,
                    listagg(acc.column_name,',') within group (order by acc.position) as col_list from all_cons_columns
                    acc
                    group by acc.owner, acc.table_name, acc.constraint_name ) ccl
                    where ac.owner = upper('@schemaname')
                    and ac.table_name in ('@tablename')
                    and ac.constraint_type = 'U'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name
                    order by ac.owner

                </Unique_Constraint>
                <Foreign_Key>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner schema_name,ac.table_name,ac.constraint_name,'FOREIGN KEY
                    (' || acc.column_name || ')' || ' REFERENCES ' ||
                    (SELECT /*+ PARALLEL(@degree) */ LISTAGG(alc.owner || '.' || alc.table_name || '(' ||
                    alc.column_name || ')', ', ')
                    WITHIN GROUP (ORDER BY alc.position)
                    FROM all_cons_columns alc
                    WHERE alc.constraint_name = ac.r_constraint_name AND alc.owner = ac.owner) || ';',ac.status
                    FROM all_constraints ac
                    JOIN all_cons_columns acc ON ac.owner = acc.owner
                    AND ac.table_name = acc.table_name
                    AND ac.constraint_name = acc.constraint_name
                    WHERE ac.owner in (upper('@schemaname'))
                    and ac.table_name in ('@tablename')
                    AND ac.constraint_type = 'R'
                    ORDER BY ac.owner
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ OWNER,TABLE_NAME,column_name
                    FROM all_tab_columns
                    WHERE nullable = 'N'
                    AND owner = upper('@schemaname')
                    and table_name in ('@tablename')
                    order by owner, table_name, column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ OWNER,TABLE_NAME,column_name, data_default
                    FROM all_tab_columns
                    WHERE data_default IS NOT NULL
                    AND owner = upper('@schemaname')
                    and table_name in ('@tablename')
                </Default_Constraint>
                <Check_Constraint>
                    SELECT /*+ PARALLEL(@degree) */
                    ac.owner,ac.table_name,ac.constraint_name,ac.search_condition,ac.status
                    from all_constraints ac, all_cons_columns acc
                    where ac.constraint_type = 'C'
                    and ac.owner = acc.owner
                    and ac.table_name = acc.table_name
                    and ac.constraint_name = acc.constraint_name
                    AND ac.owner = upper('@schemaname')
                    and ac.table_name in ('@tablename')
                    AND (AC.TABLE_NAME||'.'||acc.column_name) NOT IN (SELECT /*+ PARALLEL(@degree) */ DISTINCT
                    (TABLE_NAME||'.'||COLUMN_NAME) FROM all_tab_columns WHERE all_tab_columns.nullable = 'N')
                    AND AC.TABLE_NAME NOT LIKE '%$%'
                    order by ac.owner
                </Check_Constraint>
                <Index>
                    SELECT /*+ PARALLEL(@degree) */
                    schema_name,
                    table_name,
                    index_name,
                    index_cols,
                    uniqueness,
                    constraint_type,
                    lower('create' || case when UNIQUENESS = 'NONUNIQUE' then ' index ' else ' UNIQUE index ' end ||
                    INDEX_NAME || ' on ' || schema_name || '.' || table_name || '(' || index_cols || ');') IDX_DEF,
                    lower(schema_name||'.'||table_name||'-'||index_name) oraConcat
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */ * from
                    (
                    WITH cols AS (
                    SELECT /*+ PARALLEL(@degree) */
                    idx.table_owner AS schema_name,
                    idx.table_name,
                    idx.index_name,
                    cols.column_name,
                    cols.column_position,
                    idx.uniqueness,
                    decode(cols.descend,'ASC','',' ' || cols.descend) descend
                    FROM
                    DBA_INDEXES idx,
                    DBA_IND_COLUMNS cols
                    WHERE
                    idx.owner = cols.index_owner
                    AND idx.table_name = cols.table_name
                    AND idx.index_name = cols.index_name
                    AND idx.table_owner = upper('@schemaname')
                    and idx.table_name in ('@tablename')
                    AND idx.table_name not like '%$%'),
                    expr AS (
                    SELECT /*+ PARALLEL(@degree) */
                    extractValue(xs.object_value,'/ROW/TABLE_NAME') AS table_name,
                    extractValue(xs.object_value,'/ROW/INDEX_NAME') AS index_name,
                    extractValue(xs.object_value,'/ROW/COLUMN_EXPRESSION') AS column_expression,
                    extractValue(xs.object_value,'/ROW/COLUMN_POSITION') AS column_position
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */
                    XMLTYPE(DBMS_XMLGEN.GETXML( 'SELECT table_name, index_name, column_expression, column_position FROM
                    DBA_IND_EXPRESSIONS WHERE table_name not like ''%$%'' '
                    || ' union all SELECT null, null, null, null FROM dual ')
                    ) AS xml FROM DUAL
                    ) x,
                    TABLE(XMLSEQUENCE(EXTRACT(x.xml,'/ROWSET/ROW'))) xs)
                    SELECT /*+ PARALLEL(@degree) */
                    cols.schema_name,
                    cols.table_name,
                    DO.OBJECT_TYPE AS table_type,
                    listagg(CASE
                    WHEN cols.column_name LIKE 'SYS_N%' THEN expr.column_expression || cols.descend
                    ELSE cols.column_name || cols.descend
                    END,
                    ', ') within group(
                    order by cols.column_position) as Index_Cols,
                    cols.index_name,
                    cols.uniqueness,
                    ac.constraint_type
                    FROM
                    cols
                    LEFT JOIN expr ON
                    cols.table_name = expr.table_name
                    AND cols.index_name = expr.index_name
                    AND cols.column_position = expr.column_position
                    LEFT JOIN all_constraints ac ON
                    cols.index_name = ac.constraint_name
                    AND cols.table_name = ac.table_name
                    AND ac.constraint_type = 'P'
                    AND ac.owner = cols.schema_name
                    LEFT JOIN DBA_OBJECTS DO ON
                    DO.OWNER = cols.schema_name
                    AND do.OBJECT_NAME = cols.table_name
                    AND do.object_type = 'MATERIALIZED VIEW'
                    LEFT JOIN dba_dependencies DD ON
                    do.OBJECT_NAME = dd.NAME
                    GROUP BY
                    cols.schema_name,
                    cols.table_name,
                    DO.OBJECT_TYPE,
                    cols.index_name,
                    cols.uniqueness,
                    ac.constraint_type
                    --, DBMS_METADATA.get_ddl ('INDEX', cols.index_name, cols.schema_name)
                    ORDER BY
                    cols.table_name,
                    cols.index_name,
                    cols.uniqueness)) res
                    WHERE upper(res.index_name) not in ( select upper(INDEX_NAME) from all_constraints where upper(owner
                    )= upper('@schemaname') and constraint_type in ( 'P', 'U'))
                    GROUP BY
                    res.schema_name,
                    res.table_name,
                    res.table_type,
                    res.index_cols,
                    res.index_name,
                    res.uniqueness,
                    res.constraint_type
                    ORDER BY 1,2
                </Index>
                <Synonym>
                    select /*+ PARALLEL(@degree) */ SYNONYM_NAME,TABLE_NAME,TABLE_OWNER from dba_synonyms where
                    owner=upper('@schemaname')
                </Synonym>
                <View>
                    SELECT /*+ PARALLEL(@degree) */ owner,view_name
                    FROM dba_views
                    WHERE owner = upper('@schemaname') and view_name not like '%$%'
                    order by view_name
                </View>
                <Datatype>
                    WITH cte AS (SELECT /*+ PARALLEL(@degree) */ * from(
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name,
                    CASE WHEN OBJECT_TYPE = 'TABLE'
                    THEN 'TEMPORARY TABLE'
                    ELSE OBJECT_TYPE END AS OBJECT_TYPE
                    FROM dba_objects
                    WHERE TEMPORARY = 'Y'
                    AND OBJECT_TYPE = 'TABLE'
                    UNION
                    SELECT /*+ PARALLEL(@degree) */ owner,object_name,
                    CASE WHEN count_mv = 2 THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS OBJECT_TYPE
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name, count(*) AS count_mv
                    FROM dba_objects
                    WHERE OBJECT_TYPE in ('MATERIALIZED VIEW','TABLE')
                    AND TEMPORARY = 'N'
                    GROUP BY owner, object_name)
                    )
                    WHERE owner = upper('@schemaname')
                    AND
                    OBJECT_NAME NOT LIKE 'MLOG$%' AND OBJECT_NAME NOT LIKE 'RUPD$%'
                    ORDER BY 1,2),
                    datatype as(SELECT /*+ PARALLEL(@degree) */ OWNER, TABLE_NAME, COLUMN_NAME,
                    DATA_TYPE,COLUMN_SIZE,COLUMN_ID FROM (
                    select /*+ PARALLEL(@degree) */ OWNER, TABLE_NAME, COLUMN_NAME, DATA_TYPE,
                    case when DATA_TYPE = 'NUMBER' AND DATA_PRECISION IS NULL AND DATA_SCALE IS NULL THEN
                    '('||to_char(DATA_LENGTH)||')'
                    WHEN DATA_TYPE IN ('VARCHAR2', 'NVARCHAR2', 'NCHAR' ) THEN '('||to_char(CHAR_LENGTH)||')'
                    when DATA_TYPE IN ('TIMESTAMP(8)', 'XMLTYPE','CHAR', 'CLOB', 'UROWID') THEN
                    '('||to_char(DATA_LENGTH)||')'
                    when DATA_TYPE IN ('DATE', 'LONG', 'TIMESTAMP(6) WITH TIME ZONE', 'TIMESTAMP(6)', 'ROWID', 'RAW',
                    'BLOB', 'OBJ_SNSDETAILS') THEN NULL
                    when DATA_TYPE = 'NUMBER' AND DATA_PRECISION IS NOT NULL AND DATA_SCALE IS NOT NULL THEN
                    '('||to_char(DATA_PRECISION)||','||to_char(DATA_SCALE)||')'
                    -- WHEN DATA_TYPE IN ('FLOAT') THEN NVL(DATA_PRECISION,'9999999')
                    END AS "COLUMN_SIZE", CHARACTER_SET_NAME
                    ,DATA_PRECISION, DATA_SCALE, CHAR_COL_DECL_LENGTH, DATA_LENGTH, COLUMN_ID
                    from all_tab_columns
                    where owner in (upper('@schemaname'))
                    and table_name in ('@tablename')
                    order by owner, table_name, COLUMN_ID
                    )
                    )
                    SELECT /*+ PARALLEL(@degree) */ kk.owner,
                    kk.table_name,kk.column_name,kk.DATA_TYPE,kk.column_size,
                    kk.column_id,
                    cc.OBJECT_TYPE
                    FROM datatype kk
                    LEFT JOIN cte cc ON kk.owner = cc.owner
                    AND cc.object_name = kk.table_name
                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    SELECT /*+ PARALLEL(@degree) */
                    object_type,
                    case
                    when object_type= 'PACKAGE' then object_name||'_'||Method_name
                    when object_type= 'TYPE' then object_name||'_'||Method_name
                    when object_type = 'PROCEDURE' then object_name
                    when object_type = 'FUNCTION' then object_name
                    END as Code_Object_Name
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */
                    object_type,
                    object_name,
                    case
                    when object_type='PACKAGE' then procedure_name
                    when object_type='TYPE' then procedure_name
                    when object_type = 'PROCEDURE' then object_name
                    when object_type = 'FUNCTION' then object_name
                    END as Method_name
                    FROM dba_procedures
                    where owner = upper('@schemaname')
                    and
                    OBJECT_TYPE IN
                    (
                    'PROCEDURE',
                    'FUNCTION',
                    'PACKAGE',
                    'TYPE'
                    )
                    )a
                    where method_name is not null
                    order by object_type, Code_Object_Name
                </Code_Objects>
                <Trigger>
                    SELECT /*+ PARALLEL(@degree) */ table_owner,
                    table_name,
                    trigger_name
                    FROM DBA_TRIGGERS
                    WHERE owner = upper('@schemaname') and table_name not like '%$%'
                    and table_name in ('@tablename')
                    ORDER BY table_name,
                    trigger_name
                </Trigger>

            </Code>
        </Source>
        <Target>
            <Storage>
                <Sequence>
                    select /*+ PARALLEL(@degree) */ DISTINCT sequence_owner,sequence_name, TO_NUMBER(LAST_NUMBER) -
                    TO_NUMBER(INCREMENT_BY) AS sequence_value
                    from all_sequences
                    where sequence_owner in (lower('@schemaname'))
                </Sequence>
                <Table>
                    WITH cte AS (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name,
                    CASE WHEN OBJECT_TYPE = 'TABLE'
                    THEN 'TEMPORARY TABLE'
                    ELSE OBJECT_TYPE END AS OBJECT_TYPE
                    FROM dba_objects
                    WHERE TEMPORARY = 'Y'
                    AND OBJECT_TYPE = 'TABLE'
                    UNION
                    SELECT /*+ PARALLEL(@degree) */ owner,object_name,
                    CASE WHEN count_mv = 2 THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS OBJECT_TYPE
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name, count(*) AS count_mv
                    FROM dba_objects
                    WHERE OBJECT_TYPE in ('MATERIALIZED VIEW','TABLE')
                    AND TEMPORARY = 'N'
                    GROUP BY owner, object_name)
                    )
                    SELECT /*+ PARALLEL(@degree) */ * FROM cte
                    WHERE owner = upper('@schemaname')
                    AND
                    OBJECT_NAME NOT LIKE 'MLOG$%' AND OBJECT_NAME NOT LIKE 'RUPD$%'
                    ORDER BY 1,2
                </Table>
                <Partition>
                    SELECT DISTINCT /*+ PARALLEL(@degree) */
                    A.TABLE_OWNER,A.TABLE_NAME,A.PARTITION_NAME
                    FROM ALL_TAB_PARTITIONS A
                    JOIN ALL_TABLES B
                    ON A.TABLE_NAME = B.TABLE_NAME
                    WHERE TABLE_OWNER = upper('@schemaname') and A.TABLE_NAME = ('@tablename')
                </Partition>
                <Primary_Key>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner,ac.table_name,ac.constraint_name,ccl.col_list,ac.status
                    from all_constraints ac,
                    (select /*+ PARALLEL(@degree) */ acc.owner, acc.table_name, acc.constraint_name,
                    listagg(acc.column_name,',') within group (order by acc.position) as col_list from all_cons_columns
                    acc
                    group by acc.owner, acc.table_name, acc.constraint_name ) ccl
                    where ac.owner = upper('@schemaname')
                    and ac.table_name in ('@tablename')
                    and ac.constraint_type = 'P'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name
                    order by ac.owner
                </Primary_Key>
                <Unique_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner,ac.table_name,ac.constraint_name,ccl.col_list,ac.status
                    from all_constraints ac,
                    (select /*+ PARALLEL(@degree) */ acc.owner, acc.table_name, acc.constraint_name,
                    listagg(acc.column_name,',') within group (order by acc.position) as col_list from all_cons_columns
                    acc
                    group by acc.owner, acc.table_name, acc.constraint_name ) ccl
                    where ac.owner = upper('@schemaname')
                    and ac.table_name in ('@tablename')
                    and ac.constraint_type = 'U'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name
                    order by ac.owner

                </Unique_Constraint>
                <Foreign_Key>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner schema_name,ac.table_name,ac.constraint_name,'FOREIGN KEY
                    (' || acc.column_name || ')' || ' REFERENCES ' ||
                    (SELECT /*+ PARALLEL(@degree) */ LISTAGG(alc.owner || '.' || alc.table_name || '(' ||
                    alc.column_name || ')', ', ')
                    WITHIN GROUP (ORDER BY alc.position)
                    FROM all_cons_columns alc
                    WHERE alc.constraint_name = ac.r_constraint_name AND alc.owner = ac.owner) || ';',ac.status
                    FROM all_constraints ac
                    JOIN all_cons_columns acc ON ac.owner = acc.owner
                    AND ac.table_name = acc.table_name
                    AND ac.constraint_name = acc.constraint_name
                    WHERE ac.owner in (upper('@schemaname'))
                    and ac.table_name in ('@tablename')
                    AND ac.constraint_type = 'R'
                    ORDER BY ac.owner
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ OWNER,TABLE_NAME,column_name
                    FROM all_tab_columns
                    WHERE nullable = 'N'
                    AND owner = upper('@schemaname')
                    and table_name in ('@tablename')
                    order by owner, table_name, column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ OWNER,TABLE_NAME,column_name, data_default
                    FROM all_tab_columns
                    WHERE data_default IS NOT NULL
                    AND owner = upper('@schemaname')
                    and table_name in ('@tablename')
                </Default_Constraint>
                <Check_Constraint>
                    SELECT /*+ PARALLEL(@degree) */
                    ac.owner,ac.table_name,ac.constraint_name,ac.search_condition,ac.status
                    from all_constraints ac, all_cons_columns acc
                    where ac.constraint_type = 'C'
                    and ac.owner = acc.owner
                    and ac.table_name = acc.table_name
                    and ac.constraint_name = acc.constraint_name
                    AND ac.owner = upper('@schemaname')
                    and ac.table_name in ('@tablename')
                    AND (AC.TABLE_NAME||'.'||acc.column_name) NOT IN (SELECT /*+ PARALLEL(@degree) */ DISTINCT
                    (TABLE_NAME||'.'||COLUMN_NAME) FROM all_tab_columns WHERE all_tab_columns.nullable = 'N')
                    AND AC.TABLE_NAME NOT LIKE '%$%'
                    order by ac.owner
                </Check_Constraint>
                <Index>
                    SELECT /*+ PARALLEL(@degree) */
                    schema_name,
                    table_name,
                    index_name,
                    index_cols,
                    uniqueness,
                    constraint_type,
                    lower('create' || case when UNIQUENESS = 'NONUNIQUE' then ' index ' else ' UNIQUE index ' end ||
                    INDEX_NAME || ' on ' || schema_name || '.' || table_name || '(' || index_cols || ');') IDX_DEF,
                    lower(schema_name||'.'||table_name||'-'||index_name) oraConcat
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */ * from
                    (
                    WITH cols AS (
                    SELECT /*+ PARALLEL(@degree) */
                    idx.table_owner AS schema_name,
                    idx.table_name,
                    idx.index_name,
                    cols.column_name,
                    cols.column_position,
                    idx.uniqueness,
                    decode(cols.descend,'ASC','',' ' || cols.descend) descend
                    FROM
                    DBA_INDEXES idx,
                    DBA_IND_COLUMNS cols
                    WHERE
                    idx.owner = cols.index_owner
                    AND idx.table_name = cols.table_name
                    AND idx.index_name = cols.index_name
                    AND idx.table_owner = upper('@schemaname')
                    and idx.table_name in ('@tablename')
                    AND idx.table_name not like '%$%'),
                    expr AS (
                    SELECT /*+ PARALLEL(@degree) */
                    extractValue(xs.object_value,'/ROW/TABLE_NAME') AS table_name,
                    extractValue(xs.object_value,'/ROW/INDEX_NAME') AS index_name,
                    extractValue(xs.object_value,'/ROW/COLUMN_EXPRESSION') AS column_expression,
                    extractValue(xs.object_value,'/ROW/COLUMN_POSITION') AS column_position
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */
                    XMLTYPE(DBMS_XMLGEN.GETXML( 'SELECT table_name, index_name, column_expression, column_position FROM
                    DBA_IND_EXPRESSIONS WHERE table_name not like ''%$%'' '
                    || ' union all SELECT null, null, null, null FROM dual ')
                    ) AS xml FROM DUAL
                    ) x,
                    TABLE(XMLSEQUENCE(EXTRACT(x.xml,'/ROWSET/ROW'))) xs)
                    SELECT /*+ PARALLEL(@degree) */
                    cols.schema_name,
                    cols.table_name,
                    DO.OBJECT_TYPE AS table_type,
                    listagg(CASE
                    WHEN cols.column_name LIKE 'SYS_N%' THEN expr.column_expression || cols.descend
                    ELSE cols.column_name || cols.descend
                    END,
                    ', ') within group(
                    order by cols.column_position) as Index_Cols,
                    cols.index_name,
                    cols.uniqueness,
                    ac.constraint_type
                    FROM
                    cols
                    LEFT JOIN expr ON
                    cols.table_name = expr.table_name
                    AND cols.index_name = expr.index_name
                    AND cols.column_position = expr.column_position
                    LEFT JOIN all_constraints ac ON
                    cols.index_name = ac.constraint_name
                    AND cols.table_name = ac.table_name
                    AND ac.constraint_type = 'P'
                    AND ac.owner = cols.schema_name
                    LEFT JOIN DBA_OBJECTS DO ON
                    DO.OWNER = cols.schema_name
                    AND do.OBJECT_NAME = cols.table_name
                    AND do.object_type = 'MATERIALIZED VIEW'
                    LEFT JOIN dba_dependencies DD ON
                    do.OBJECT_NAME = dd.NAME
                    GROUP BY
                    cols.schema_name,
                    cols.table_name,
                    DO.OBJECT_TYPE,
                    cols.index_name,
                    cols.uniqueness,
                    ac.constraint_type
                    --, DBMS_METADATA.get_ddl ('INDEX', cols.index_name, cols.schema_name)
                    ORDER BY
                    cols.table_name,
                    cols.index_name,
                    cols.uniqueness)) res
                    WHERE upper(res.index_name) not in ( select upper(INDEX_NAME) from all_constraints where upper(owner
                    )= upper('@schemaname') and constraint_type in ( 'P', 'U'))
                    GROUP BY
                    res.schema_name,
                    res.table_name,
                    res.table_type,
                    res.index_cols,
                    res.index_name,
                    res.uniqueness,
                    res.constraint_type
                    ORDER BY 1,2
                </Index>
                <Synonym>
                    select /*+ PARALLEL(@degree) */ SYNONYM_NAME,TABLE_NAME,TABLE_OWNER from dba_synonyms where
                    owner=upper('@schemaname')
                </Synonym>
                <View>
                    SELECT /*+ PARALLEL(@degree) */ owner,view_name
                    FROM dba_views
                    WHERE owner = upper('@schemaname') and view_name not like '%$%'
                    order by view_name
                </View>
                <Datatype>
                    WITH cte AS (SELECT /*+ PARALLEL(@degree) */ * from(
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name,
                    CASE WHEN OBJECT_TYPE = 'TABLE'
                    THEN 'TEMPORARY TABLE'
                    ELSE OBJECT_TYPE END AS OBJECT_TYPE
                    FROM dba_objects
                    WHERE TEMPORARY = 'Y'
                    AND OBJECT_TYPE = 'TABLE'
                    UNION
                    SELECT /*+ PARALLEL(@degree) */ owner,object_name,
                    CASE WHEN count_mv = 2 THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS OBJECT_TYPE
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name, count(*) AS count_mv
                    FROM dba_objects
                    WHERE OBJECT_TYPE in ('MATERIALIZED VIEW','TABLE')
                    AND TEMPORARY = 'N'
                    GROUP BY owner, object_name)
                    )
                    WHERE owner = upper('@schemaname')
                    AND
                    OBJECT_NAME NOT LIKE 'MLOG$%' AND OBJECT_NAME NOT LIKE 'RUPD$%'
                    ORDER BY 1,2),
                    datatype as(SELECT /*+ PARALLEL(@degree) */ OWNER, TABLE_NAME, COLUMN_NAME,
                    DATA_TYPE,COLUMN_SIZE,COLUMN_ID FROM (
                    select /*+ PARALLEL(@degree) */ OWNER, TABLE_NAME, COLUMN_NAME, DATA_TYPE,
                    case when DATA_TYPE = 'NUMBER' AND DATA_PRECISION IS NULL AND DATA_SCALE IS NULL THEN
                    '('||to_char(DATA_LENGTH)||')'
                    WHEN DATA_TYPE IN ('VARCHAR2', 'NVARCHAR2', 'NCHAR' ) THEN '('||to_char(CHAR_LENGTH)||')'
                    when DATA_TYPE IN ('TIMESTAMP(8)', 'XMLTYPE','CHAR', 'CLOB', 'UROWID') THEN
                    '('||to_char(DATA_LENGTH)||')'
                    when DATA_TYPE IN ('DATE', 'LONG', 'TIMESTAMP(6) WITH TIME ZONE', 'TIMESTAMP(6)', 'ROWID', 'RAW',
                    'BLOB', 'OBJ_SNSDETAILS') THEN NULL
                    when DATA_TYPE = 'NUMBER' AND DATA_PRECISION IS NOT NULL AND DATA_SCALE IS NOT NULL THEN
                    '('||to_char(DATA_PRECISION)||','||to_char(DATA_SCALE)||')'
                    -- WHEN DATA_TYPE IN ('FLOAT') THEN NVL(DATA_PRECISION,'9999999')
                    END AS "COLUMN_SIZE", CHARACTER_SET_NAME
                    ,DATA_PRECISION, DATA_SCALE, CHAR_COL_DECL_LENGTH, DATA_LENGTH, COLUMN_ID
                    from all_tab_columns
                    where owner in (upper('@schemaname'))
                    and table_name in ('@tablename')
                    order by owner, table_name, COLUMN_ID
                    )
                    )
                    SELECT /*+ PARALLEL(@degree) */ kk.owner,
                    kk.table_name,kk.column_name,kk.DATA_TYPE,kk.column_size,
                    kk.column_id,
                    cc.OBJECT_TYPE
                    FROM datatype kk
                    LEFT JOIN cte cc ON kk.owner = cc.owner
                    AND cc.object_name = kk.table_name
                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    SELECT /*+ PARALLEL(@degree) */
                    object_type,
                    case
                    when object_type= 'PACKAGE' then object_name||'_'||Method_name
                    when object_type= 'TYPE' then object_name||'_'||Method_name
                    when object_type = 'PROCEDURE' then object_name
                    when object_type = 'FUNCTION' then object_name
                    END as Code_Object_Name
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */
                    object_type,
                    object_name,
                    case
                    when object_type='PACKAGE' then procedure_name
                    when object_type='TYPE' then procedure_name
                    when object_type = 'PROCEDURE' then object_name
                    when object_type = 'FUNCTION' then object_name
                    END as Method_name
                    FROM dba_procedures
                    where owner = upper('@schemaname')
                    and
                    OBJECT_TYPE IN
                    (
                    'PROCEDURE',
                    'FUNCTION',
                    'PACKAGE',
                    'TYPE'
                    )
                    )a
                    where method_name is not null
                    order by object_type, Code_Object_Name
                </Code_Objects>
                <Trigger>
                    SELECT /*+ PARALLEL(@degree) */ table_owner,
                    table_name,
                    trigger_name
                    FROM DBA_TRIGGERS
                    WHERE owner = upper('@schemaname') and table_name not like '%$%'
                    and table_name in ('@tablename')
                    ORDER BY table_name,
                    trigger_name
                </Trigger>

            </Code>
        </Target>
    </Table_Validation_Queries>
    <Extract_Tables>
        select DISTINCT OBJECT_NAME,STATUS from dba_objects a where NOT exists (select 1 from dba_mviews MV
        where
        MV.MVIEW_NAME=a.object_name AND MV.OWNER=upper('@schemaname')) AND A.OWNER=upper('@schemaname') and
        OBJECT_NAME not like '%$%' and OBJECT_NAME not like 'SYS_%' AND A.OBJECT_TYPE='TABLE' and
        TEMPORARY='N' ORDER BY 1
    </Extract_Tables>
</Queries>