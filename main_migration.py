import argparse, os, sys
from import_file import import_file
from common_modules.api import api_authentication, decrypt_file, delete_files_in_directory

parser = argparse.ArgumentParser()

parser.add_argument('-task', '--task_name',
                    help="Provide task name", nargs='?', const='', default='')
parser.add_argument('-project_id', '--project_id',
                    help="Provide project id", nargs='?', const='', default='')
parser.add_argument('-mig_name', '--migration_name',
                    help="Provide migration name", nargs='?', const='', default='')
parser.add_argument('-source_connection_id', '--source_connection_id',
                    help="Provide source connection id", nargs='?', const='', default='')
parser.add_argument('-target_connection_id', '--target_connection_id',
                    help="Provide target connection id", nargs='?', const='', default='')
parser.add_argument('-cloud_category', '--cloud_category',
                    help="Provide cloud category", nargs='?', const='', default='')
parser.add_argument('-schema', '--schema_name',
                    help="Provide schema name", nargs='?', const='', default='')
parser.add_argument('-target_schema', '--target_schema',
                    help="Provide target schema name", nargs='?', const='', default='')
parser.add_argument('-table_name', '--table_name',
                    help="Provide table_name", nargs='?', const='', default='')
parser.add_argument('-request_cpu', '--request_cpu',
                    help="Provide requests cpu", nargs='?', const='', default='')
parser.add_argument('-limit_cpu', '--limit_cpu',
                    help="Provide limit cpu", nargs='?', const='', default='')
parser.add_argument('-file_name', '--file_name',
                    help="Provide file_name", nargs='?', const='', default='')
parser.add_argument('-data_load_type', '--data_load_type',
                    help="Provide data_load_type", nargs='?', const='', default='')
parser.add_argument('-cdc_load_type', '--cdc_load_type',
                    help="Provide cdc_load_type", nargs='?', const='', default='')
parser.add_argument('-dag_execute_type', '--dag_execute_type',
                    help="Provide dag_execute_type", nargs='?', const='', default='')
parser.add_argument('-compare_type', '--compare_type',
                    help="Provide compare_type", nargs='?', const='', default='')

args = parser.parse_args()

task_name = args.task_name
project_id = args.project_id
migration_name = args.migration_name
source_connection_id = args.source_connection_id
target_connection_id = args.target_connection_id
cloud_category = args.cloud_category
schema_name = args.schema_name
target_schema = args.target_schema
request_cpu = args.request_cpu
limit_cpu = args.limit_cpu
table_name = args.table_name
data_load_type = args.data_load_type
dag_execute_type = args.dag_execute_type
cdc_load_type = args.cdc_load_type
compare_type = args.compare_type
file_name = args.file_name

local_root_path = os.path.dirname(os.path.realpath(__file__))
config_path = local_root_path + '/' + 'config.py'
sys.path.append(config_path)
import_object = import_file(config_path)
working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')

token_data = api_authentication()

migration_config_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'Config_Modules'
migration_dags_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'Dag_Files'

if __name__ == "__main__":
    if task_name in ['Initial_Data_Load', 'E2E_Data_Load']:
        file_path = migration_config_path + '/' + 'data_load_config.py'
        decrypt_file(token_data, file_path, cloud_category)
        from tmp.data_load_config import data_load_trigger

        data_load_trigger = data_load_trigger
        delete_files_in_directory(working_directory_path)
        data_load_trigger(task_name, project_id, migration_name, source_connection_id, target_connection_id,
                          schema_name, target_schema, table_name, request_cpu, limit_cpu, data_load_type, cdc_load_type,
                          file_name, cloud_category)
        delete_files_in_directory(working_directory_path)

    elif task_name in ['CDC']:
        file_path = migration_config_path + '/' + 'cdc_config.py'
        decrypt_file(token_data, file_path, cloud_category)
        from tmp.cdc_config import cdc_load_trigger

        cdc_load_trigger = cdc_load_trigger
        delete_files_in_directory(working_directory_path)
        cdc_load_trigger(task_name, project_id, migration_name, source_connection_id, target_connection_id, schema_name,
                         target_schema, table_name, cdc_load_type, file_name, cloud_category)
        delete_files_in_directory(working_directory_path)

    elif task_name in ['Load_Files']:
        file_path = migration_config_path + '/' + 'load_files_config.py'
        decrypt_file(token_data, file_path, cloud_category)
        from tmp.load_files_config import load_files_trigger

        load_files_trigger = load_files_trigger
        delete_files_in_directory(working_directory_path)
        load_files_trigger(task_name, project_id, migration_name, source_connection_id, target_connection_id, file_name,
                           cloud_category)
        delete_files_in_directory(working_directory_path)

    elif task_name in ['Job_Agent']:
        file_path = migration_config_path + '/' + 'job_agent.py'
        print(file_path,'===filepath==')
        decrypt_file(token_data, file_path, cloud_category)
        from tmp.job_agent import job_agent_trigger

        job_agent_trigger = job_agent_trigger
        delete_files_in_directory(working_directory_path)
        job_agent_trigger(task_name, project_id, migration_name, source_connection_id, target_connection_id,
                          cloud_category)
        delete_files_in_directory(working_directory_path)


    elif task_name in ['Pre_Validation', 'Post_Validation', 'Complete_Validation']:
        file_path = migration_config_path + '/' + 'validation_config.py'
        decrypt_file(token_data, file_path, cloud_category)
        from tmp.validation_config import validation_load_trigger

        validation_load_trigger = validation_load_trigger
        delete_files_in_directory(working_directory_path)
        validation_load_trigger(task_name, project_id, migration_name, source_connection_id, target_connection_id,
                                schema_name, target_schema, file_name, cloud_category)
        delete_files_in_directory(working_directory_path)

    elif task_name in ['Data_Compare']:
        file_path = migration_config_path + '/' + 'data_compare_config.py'
        decrypt_file(token_data, file_path, cloud_category)
        from tmp.data_compare_config import data_compare_trigger

        data_compare_trigger = data_compare_trigger
        delete_files_in_directory(working_directory_path)
        data_compare_trigger(task_name, project_id, migration_name, source_connection_id, target_connection_id,
                             schema_name, target_schema, table_name, compare_type, request_cpu, limit_cpu, file_name,
                             cloud_category)
        delete_files_in_directory(working_directory_path)

    elif task_name in ['CPU_Memory_Utilization']:
        file_path = migration_config_path + '/' + 'cpu_memory_config.py'
        decrypt_file(token_data, file_path, cloud_category)
        from tmp.cpu_memory_trigger import cpu_memory_trigger

        cpu_memory_trigger = cpu_memory_trigger
        delete_files_in_directory(working_directory_path)
        cpu_memory_trigger(task_name, project_id, migration_name, source_connection_id, target_connection_id,cloud_category)

