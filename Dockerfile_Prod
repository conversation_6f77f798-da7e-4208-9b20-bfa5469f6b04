FROM python:3.9-slim

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

COPY . /app
WORKDIR /app

ARG MIG
ARG MIG_ID

ARG GID
ARG UID

ENV UID=${UID} \
    GID=${GID}

RUN echo "MIG Name: $MIG" && \
    echo "MIG ID: $MIG_ID" && \
    echo "GID: $GID" && \
    echo "UID: $UID"

RUN apt-get update && \
    apt-get -y install libpq-dev gcc libaio1 wget unzip && \
    apt-get clean


RUN if [ "$MIG" = "Oracle_Postgres14" ] || [ "$MIG" = "Oracle_MongoDB" ] || [ "$MIG" = "Oracle_SQL" ] || [ "$MIG" = "Oracle_Oracle" ]; then \
        mkdir -p /opt/oracle && \
        cd /opt/oracle && \
        apt-get update && \
        apt-get install -y libaio1 wget unzip && \
        wget https://download.oracle.com/otn_software/linux/instantclient/instantclient-basiclite-linuxx64.zip && \
        unzip instantclient-basiclite-linuxx64.zip && \
        rm -f instantclient-basiclite-linuxx64.zip && \
        cd /opt/oracle/instantclient* && \
        rm -f *jdbc* *occi* *mysql* *README *jar uidrvci genezi adrci && \
        echo /opt/oracle/instantclient* > /etc/ld.so.conf.d/oracle-instantclient.conf && \
        ldconfig; \
    elif [ "$MIG" = "Oracle_Fabrics" ]; then \
        mkdir -p /opt/oracle && \
        cd /opt/oracle && \
        apt-get update && \
        apt-get install -y libaio1 wget unzip && \
        wget https://download.oracle.com/otn_software/linux/instantclient/instantclient-basiclite-linuxx64.zip && \
        unzip instantclient-basiclite-linuxx64.zip && \
        rm -f instantclient-basiclite-linuxx64.zip && \
        cd /opt/oracle/instantclient* && \
        rm -f *jdbc* *occi* *mysql* *README *jar uidrvci genezi adrci && \
        echo /opt/oracle/instantclient* > /etc/ld.so.conf.d/oracle-instantclient.conf && \
        ldconfig; \

        mkdir -p /opt/oracle && \
        cd /opt/oracle && \
        wget https://download.oracle.com/otn_software/linux/instantclient/instantclient-basiclite-linuxx64.zip && \
        wget https://download.oracle.com/otn_software/linux/instantclient/instantclient-tools-linuxx64.zip && \
        unzip -o -q instantclient-basiclite-linuxx64.zip -d /opt/oracle && \
        unzip -o -q instantclient-tools-linuxx64.zip -d /opt/oracle && \
        echo "Unzipping completed." && \
        rm -f instantclient-basiclite-linuxx64.zip instantclient-tools-linuxx64.zip && \
        echo "Cleanup completed." && \
        mv /opt/oracle/instantclient* /opt/oracle/instantclient_21_1 && \
        rm -rf /opt/oracle/instantclient_21_1/*jdbc* /opt/oracle/instantclient_21_1/*occi* /opt/oracle/instantclient_21_1/*mysql* /opt/oracle/instantclient_21_1/README /opt/oracle/instantclient_21_1/*jar /opt/oracle/instantclient_21_1/uidrvci /opt/oracle/instantclient_21_1/genezi /opt/oracle/instantclient_21_1/adrci && \
        echo "/opt/oracle/instantclient_21_1" > /etc/ld.so.conf.d/oracle-instantclient.conf && \
        ldconfig && \
        echo "Oracle Instant Client setup completed." && \
        export ORACLE_HOME=/opt/oracle/instantclient_21_1 && \
        export PATH=$ORACLE_HOME:$PATH && \
        export LD_LIBRARY_PATH=$ORACLE_HOME; \
    elif [ "$MIG" = "MSSQL_Oracle" ]; then \
        mkdir -p /opt/oracle && \
        cd /opt/oracle && \
        wget https://download.oracle.com/otn_software/linux/instantclient/instantclient-basiclite-linuxx64.zip && \
        wget https://download.oracle.com/otn_software/linux/instantclient/instantclient-tools-linuxx64.zip && \
        unzip -o -q instantclient-basiclite-linuxx64.zip -d /opt/oracle && \
        unzip -o -q instantclient-tools-linuxx64.zip -d /opt/oracle && \
        echo "Unzipping completed." && \
        rm -f instantclient-basiclite-linuxx64.zip instantclient-tools-linuxx64.zip && \
        echo "Cleanup completed." && \
        mv /opt/oracle/instantclient* /opt/oracle/instantclient_21_1 && \
        rm -rf /opt/oracle/instantclient_21_1/*jdbc* /opt/oracle/instantclient_21_1/*occi* /opt/oracle/instantclient_21_1/*mysql* /opt/oracle/instantclient_21_1/README /opt/oracle/instantclient_21_1/*jar /opt/oracle/instantclient_21_1/uidrvci /opt/oracle/instantclient_21_1/genezi /opt/oracle/instantclient_21_1/adrci && \
        echo "/opt/oracle/instantclient_21_1" > /etc/ld.so.conf.d/oracle-instantclient.conf && \
        ldconfig && \
        echo "Oracle Instant Client setup completed." && \
        export ORACLE_HOME=/opt/oracle/instantclient_21_1 && \
        export PATH=$ORACLE_HOME:$PATH && \
        export LD_LIBRARY_PATH=$ORACLE_HOME; \
    elif [ "$MIG" = "DB2_Mysql" ]; then \
        apt-get install -y default-mysql-client && \
        wget -O v11.5fp11_linuxx64_dsdriver.tar.gz https://public.dhe.ibm.com/ibmdl/export/pub/software/data/db2/drivers/odbc_cli/linuxx64_odbc_cli.tar.gz && \
        tar -xzvf v11.5fp11_linuxx64_dsdriver.tar.gz && \
        mkdir -p /opt/ibm && \
        mv ./clidriver /opt/ibm && \
        rm v11.5fp11_linuxx64_dsdriver.tar.gz && \
        export IBM_DB_HOME=/opt/ibm && \
        export LD_LIBRARY_PATH=/opt/ibm/lib64:$LD_LIBRARY_PATH && \
        export PATH=/opt/ibm/clidriver/bin:$PATH; \
    elif [ "$MIG" = "DB2_Fabric" ]; then \
        apt-get install -y wget tar libaio1 unixodbc && \
        wget -O v11.5fp11_linuxx64_dsdriver.tar.gz https://public.dhe.ibm.com/ibmdl/export/pub/software/data/db2/drivers/odbc_cli/linuxx64_odbc_cli.tar.gz && \
        tar -xzvf v11.5fp11_linuxx64_dsdriver.tar.gz && \
        mkdir -p /opt/ibm && \
        mv ./clidriver /opt/ibm && \
        rm v11.5fp11_linuxx64_dsdriver.tar.gz && \
        export IBM_DB_HOME=/opt/ibm && \
        export LD_LIBRARY_PATH=/opt/ibm/lib64:$LD_LIBRARY_PATH && \
        export PATH=/opt/ibm/clidriver/bin:$PATH; \
    elif [ "$MIG" = "DynamoDB_CosmosNosql" ]; then \
        echo "There are no specific dependencies for this $MIG"; \
    elif [ "$MIG" = "DynamoDB_Postgres" ]; then \
        echo "There are no specific dependencies for this $MIG"; \
    elif [ "$MIG" = "Mariadb_Mysql" ]; then \
        apt-get install -y mariadb-client && \
        apt-get install -y mydumper && \
        apt-get install -y default-mysql-client && \
        apt-get install -y libmariadb-dev && \
        apt-get clean; \
    elif [ "$MIG" = "Mysql_Mysql" ]; then \
        apt-get install -y default-mysql-client && \
        apt-get clean; \
    elif [ "$MIG" = "Postgres_Postgres" ]; then \
        echo "There are no specific dependencies for this $MIG"; \
    fi

WORKDIR /app
# RUN python3 -m pip install -r /app/Migrations/"$MIG"/requirements.txt
RUN if [ "$MIG" = "Oracle_Fabrics" ]; then \
      python3 -m pip install -r /app/Migrations/MSSQL_Fabrics/requirements.txt &&\
      python3 -m pip install -r /app/Migrations/"$MIG"/requirements.txt ; \
    else \
      python3 -m pip install -r /app/Migrations/"$MIG"/requirements.txt ; \
    fi
RUN pip3 install --upgrade setuptools

# RUN find /app/Migrations -mindepth 1 -maxdepth 1 -not -name "$MIG" -exec rm -rf {} \;
RUN if [ "$MIG" = "Oracle_Fabrics" ]; then \
      find /app/Migrations -mindepth 1 -maxdepth 1 \
        -not -name "Oracle_Fabrics" -not -name "MSSQL_Fabrics" \
        -exec rm -rf {} \; ; \
    else \
      find /app/Migrations -mindepth 1 -maxdepth 1 \
        -not -name "$MIG" \
        -exec rm -rf {} \; ; \
    fi

# RUN python3 pipeline_script.py -migid "$MIG_ID" -mig_name "$MIG"
RUN if [ "$MIG" = "Oracle_Fabrics" ]; then \
      python3 pipeline_script.py -migid "40" -mig_name MSSQL_Fabrics && \
      python3 pipeline_script.py -migid "$MIG_ID" -mig_name "$MIG" ; \
    else \
      python3 pipeline_script.py -migid "$MIG_ID" -mig_name "$MIG" ; \
    fi
RUN rm -rf Migrations_Pipeline_Test Dockerfile_Test pipeline_script.py

RUN if [ "${GID}" != "0" ]; then \
    groupadd --system --gid "${GID}" "app"; \
fi
# RUN groupadd --system --gid 4000 app
# RUN adduser --uid 5000 --ingroup app --disabled-password --gecos "" appuser && chown -R appuser /app
# USER appuser

RUN adduser --gid "${GID}" --disabled-password --gecos "" --uid "${UID}" appuser && chown -R appuser /app
USER appuser
CMD ["python", "redis_migration.py"]