import psycopg2

def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


def DB_connection(database_data):
    host_name = database_data['host']
    port = database_data['port']
    db_name = database_data['db_name']
    user_name = database_data['name']
    password = database_data['password']
    try:
        connection = psycopg2.connect(user=user_name, password=password,
                                      host=host_name, database=db_name,
                                      port=port)
        error = ''
    except psycopg2.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near database connection", e)
    return connection,error



def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        data = cursor.fetchall()
    except psycopg2.DatabaseError as e:
        print("Issue found near Target database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data

