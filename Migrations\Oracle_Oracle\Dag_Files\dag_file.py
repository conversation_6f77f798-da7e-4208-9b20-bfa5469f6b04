import os, sys, requests, json, math, re
import pandas as pd
from import_file import import_file
from datetime import datetime, timedelta
from kubernetes.client import models as k8s
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.models.baseoperator import chain
from airflow.exceptions import AirflowFailException

connection_url = os.getenv("API_HOST", 'http://qmig-eng.qmig-ns.svc.cluster.local:8080')
connection_url = connection_url + '/api/v1/'


def api_authentication():
    url = connection_url + 'Common/Security/VerifyPython'
    project_id = os.environ['PROJECT_ID']
    params = {
        "projectId": str(project_id),
        "content": "8/2DlAH8q1R+untey0ZuqMuD89WX4m2XoSCWhxQ+UOk="}
    response = requests.get(url, params=params)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = json.loads(status_response['text'])
        token_data = data['token']
        return token_data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def get_project_DB_info(token_data, project_id):
    url = connection_url + 'Common/Master/GetProjectMigDetailSelect'
    payload = {
        "projectId": str(project_id),
        "migsrcType": "D"
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.post(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = data_text['jsonResponseData']
        data_text = [dict for dict in data_text['Table1'] if dict['dbconname'] == 'PRJDB' + str(project_id)][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def decrypt_string(encrypted_str, token_data):
    application_url = connection_url + 'Common/Cryptography/DecryptData'
    payload = {
        "NeedToDecrypt": encrypted_str
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response = requests.post(application_url, data=json.dumps(payload), headers=headers)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = response.text
        return data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def decrypt_database_details(token_data, project_id, category, connection_id):
    prj_data = {}
    prj_db_data = {}
    if category == 'Project':
        prj_db_data = get_project_DB_info(token_data, project_id)
    password = decrypt_string(prj_db_data['dbpassword'], token_data)
    userid = decrypt_string(prj_db_data['dbuserid'], token_data)
    userid = re.split('\s+', re.sub(r"[\x00-\x1F\x7F]", ' ', userid))
    userid = ' '.join(userid).strip()
    password = re.split('\s+', re.sub(r"[\x00-\x1F\x7F]", ' ', password))
    password = ' '.join(password).strip()
    port = prj_db_data['dbport']
    host = prj_db_data['dbhost']
    if category == 'Project':
        service_name = ''
        parallelprocess = ''
    else:
        service_name = prj_db_data['service_name']
        parallelprocess = prj_db_data['parallelprocess']
    dbname = prj_db_data['dbname']
    prj_data['password'] = password
    prj_data['name'] = userid
    prj_data['port'] = port
    prj_data['host'] = host
    prj_data['db_name'] = dbname
    prj_data['service_name'] = service_name
    prj_data['parallelprocess'] = parallelprocess
    return prj_data


def api_decrypt(data, token_data):
    url = connection_url + 'Common/Cryptography/DecryptData'
    payload = {
        "NeedToDecrypt": data,
        "user": "Python"
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.post(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    encrypted_source = status_response1['text']
    return encrypted_source


def decrypt_file(token_data, file_path, decryption_path, dag_name, task_name):
    with open(file_path, 'r') as encrypted_file:
        encrypted = encrypted_file.read()
    decrypted = api_decrypt(encrypted, token_data)

    if not os.path.exists(decryption_path):
        os.makedirs(decryption_path)

    file_name = file_path.split('/')[-1]
    with open(decryption_path + '/' + dag_name + '_' + task_name + '_' + file_name, 'w') as decrypted_file:
        decrypted_file.write(decrypted)


def delete_files_in_directory(file_path):
    if os.path.isfile(file_path):
        os.remove(file_path)


def pre_validation_task_trigger(ti, **kwargs):
    try:
        token_data = kwargs['token_data']
        dag_name = kwargs['dag_id']
        task_name = kwargs['task_name']

        decryption_path = os.environ['EXTRA_FOLDER'] + '/' + 'tmp'
        sys.path.append(decryption_path)

        current_directory = os.getcwd() + '/' + 'dags'

        decrypt_file(token_data, current_directory + '/' + 'dag_triggers.py', decryption_path, dag_name, task_name)
        import_object = import_file(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')
        delete_files_in_directory(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')

        function_name = [i for i in dir(import_object) if i.lower() == str('pre_validation_trigger').lower()][0]
        pre_validation_function_call = getattr(import_object, function_name.strip())
        validation_flag = pre_validation_function_call(ti, **kwargs)

        if validation_flag:
            raise AirflowFailException('Pre Validation Failed')
    except Exception as error:
        print(f"Error at Pre Validation Task: {str(error)}")
        raise AirflowFailException('Error at Pre Validation Task')


def table_migration(ti, **kwargs):
    token_data = kwargs['token_data']
    dag_name = kwargs['dag_id']
    task_name = kwargs['task_name']
    data_load_type = kwargs['data_load_type']

    decryption_path = os.environ['EXTRA_FOLDER'] + '/tmp'
    sys.path.append(decryption_path)

    current_directory = os.getcwd() + '/' + 'dags'

    decrypt_file(token_data, current_directory + '/' + 'dag_triggers.py', decryption_path, dag_name, task_name)
    import_object = import_file(
        decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')
    delete_files_in_directory(
        decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')
    if data_load_type == 'Data_Pump':
        function_name = \
            [i for i in dir(import_object) if i.lower() == str('e2e_migration').lower()][0]
    else:
        function_name = \
            [i for i in dir(import_object) if i.lower() == str('table_migration').lower()][0]
    function_call = getattr(import_object, function_name.strip())
    function_call(ti, **kwargs)


def post_validation_task_trigger(ti, **kwargs):
    try:
        token_data = kwargs['token_data']
        dag_name = kwargs['dag_id']
        task_name = kwargs['task_name']

        decryption_path = os.environ['EXTRA_FOLDER'] + '/tmp'
        sys.path.append(decryption_path)

        current_directory = os.getcwd() + '/' + 'dags'

        decrypt_file(token_data, current_directory + '/' + 'dag_triggers.py', decryption_path, dag_name, task_name)
        import_object = import_file(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')
        delete_files_in_directory(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')

        function_name = [i for i in dir(import_object) if i.lower() == str('post_validation_trigger').lower()][0]
        post_validation_function_call = getattr(import_object, function_name.strip())
        validation_flag = post_validation_function_call(ti, **kwargs)

        if validation_flag:
            raise AirflowFailException('Post Validation Failed')
    except Exception as error:
        print(f"Error at Post Validation Task: {str(error)}")
        raise AirflowFailException('Error at Post Validation Task')


def deploy_table_constraints_task_trigger(ti, **kwargs):
    try:
        token_data = kwargs['token_data']
        dag_name = kwargs['dag_id']
        task_name = kwargs['task_name']
        decryption_path = os.environ['EXTRA_FOLDER'] + '/tmp'
        sys.path.append(decryption_path)

        current_directory = os.getcwd() + '/' + 'dags'

        decrypt_file(token_data, current_directory + '/' + 'dag_triggers.py', decryption_path, dag_name, task_name)
        import_object = import_file(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')
        delete_files_in_directory(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')

        function_name = [i for i in dir(import_object) if i.lower() == str('deploy_table_constraints_trigger').lower()][
            0]
        deploy_constraints_function_call = getattr(import_object, function_name.strip())
        deploy_constraints_function_call(ti, **kwargs)
    except Exception as error:
        raise AirflowFailException(f'Error at deploy_constraints_task: {str(error)}')


def complete_validation_task_trigger(ti, **kwargs):
    try:
        token_data = kwargs['token_data']
        dag_name = kwargs['dag_id']
        task_name = kwargs['task_name']
        decryption_path = os.environ['EXTRA_FOLDER'] + '/tmp'
        sys.path.append(decryption_path)

        current_directory = os.getcwd() + '/' + 'dags'

        decrypt_file(token_data, current_directory + '/' + 'dag_triggers.py', decryption_path, dag_name, task_name)
        import_object = import_file(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')
        delete_files_in_directory(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')

        function_name = [i for i in dir(import_object) if i.lower() == str('complete_validation_trigger').lower()][0]
        complete_validation_function_call = getattr(import_object, function_name.strip())
        validation_flag = complete_validation_function_call(ti, **kwargs)

        if validation_flag:
            raise AirflowFailException('Complete Validation Failed')
    except Exception as error:
        print(f"Error at Complete Validation Task: {str(error)}")
        raise AirflowFailException('Error at Complete Validation Task')


def cdc_task_trigger(**kwargs):
    token_data = kwargs['token_data']
    dag_name = kwargs['dag_id']
    task_name = kwargs['task_name']

    decryption_path = os.environ['EXTRA_FOLDER'] + '/tmp'
    sys.path.append(decryption_path)

    current_directory = os.getcwd() + '/' + 'dags'

    decrypt_file(token_data, current_directory + '/' + 'dag_triggers.py', decryption_path, dag_name, task_name)
    import_object = import_file(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')
    delete_files_in_directory(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')

    function_name = [i for i in dir(import_object) if i.lower() == str('cdc_trigger').lower()][0]
    cdc_function_call = getattr(import_object, function_name.strip())
    cdc_function_call(**kwargs)


def load_files_task_trigger(**kwargs):
    token_data = kwargs['token_data']
    dag_name = kwargs['dag_id']
    task_name = kwargs['task_name']

    decryption_path = os.environ['EXTRA_FOLDER'] + '/tmp'
    sys.path.append(decryption_path)

    current_directory = os.getcwd() + '/' + 'dags'

    decrypt_file(token_data, current_directory + '/' + 'dag_triggers.py', decryption_path, dag_name, task_name)
    import_object = import_file(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')
    delete_files_in_directory(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'dag_triggers.py')

    function_name = [i for i in dir(import_object) if i.lower() == str('load_files_trigger').lower()][0]
    load_files_function_call = getattr(import_object, function_name.strip())
    load_files_function_call(**kwargs)


def create_dag(dag_id, tag_name, schedule_interval, pause_flag, default_args, max_active_runs, process_type, project_id,
               source_connection_id, target_connection_id, target_schema, dag_tables_list, data_load_type,
               cdc_load_type, request_cpu, limit_cpu, file_name, token_data, priority_dict, chunk_configuration_dict):
    dag = DAG(dag_id, schedule_interval=schedule_interval, default_args=default_args,
              render_template_as_native_obj=True, max_active_runs=max_active_runs,
              concurrency=int(chunk_configuration_dict['Concurrency']),
              tags=[tag_name], is_paused_upon_creation=pause_flag)

    schema_name = dag_tables_list[0][0]
    table_name = dag_tables_list[0][1]

    if target_schema in ['', None]:
        target_schema = schema_name

    if str(dag_name).startswith('CDC_'):
        cdc_task = PythonOperator(
            task_id='cdc_' + str(file_name),
            op_kwargs={'dag_id': dag_id, 'project_id': project_id, 'process_type': process_type,
                       'source_connection_id': source_connection_id,
                       'target_connection_id': target_connection_id, 'schema': schema_name,
                       'target_schema': target_schema, 'table_name': table_name, 'token_data': token_data,
                       'task_name': dag_tables_list[0][4], 'file_name': file_name, 'cdc_load_type': cdc_load_type},
            python_callable=cdc_task_trigger,
            priority_weight=priority_dict[dag_id],
            weight_rule='absolute',
            trigger_rule='all_success',
            dag=dag)
        cdc_task

    elif str(dag_name).startswith('Load_Files_'):
        load_files_task = PythonOperator(
            task_id='load_files_' + str(file_name),
            op_kwargs={'dag_id': dag_id, 'project_id': project_id, 'process_type': process_type,
                       'source_connection_id': source_connection_id,
                       'target_connection_id': target_connection_id, 'token_data': token_data,
                       'task_name': dag_tables_list[0][4], 'file_name': file_name},
            python_callable=load_files_task_trigger,
            priority_weight=priority_dict[dag_id],
            weight_rule='absolute',
            trigger_rule='all_success',
            dag=dag)
        load_files_task

    elif str(dag_name).startswith('E2E_Migration_'):
        pre_validation_task = PythonOperator(
            task_id='pre_validation_task',
            op_kwargs={'dag_id': dag_id, 'project_id': project_id, 'source_connection_id': source_connection_id,
                       'target_connection_id': target_connection_id, 'schema': schema_name,
                       'target_schema': target_schema, 'table_name': table_name, 'token_data': token_data,
                       'task_name': 'pre_validation_' + str(file_name), 'file_name': file_name,
                       'chunk_size': None, 'table_size': None, 'chunk_parts': 1, 'concurrency': None,
                       'process_type': process_type, 'data_load_type': data_load_type,
                       'table_category': 'E2E_Migration'},
            python_callable=pre_validation_task_trigger,
            priority_weight=priority_dict[dag_id],
            weight_rule='absolute',
            trigger_rule='all_success',
            dag=dag)

        executor_config_memory = {
            "pod_override": k8s.V1Pod(
                spec=k8s.V1PodSpec(
                    containers=[
                        k8s.V1Container(
                            name='base',
                            resources=k8s.V1ResourceRequirements(
                                requests={'cpu': request_cpu, 'memory': '4000Mi'},
                                limits={'cpu': limit_cpu, 'memory': '4000Mi'})
                        )
                    ]
                )
            ),
        }

        e2e_migration_task = PythonOperator(
            task_id='e2e_migration_' + str(file_name),
            op_kwargs={'dag_id': dag_id, 'task_name': 'e2e_migration_' + str(file_name),
                       'process_type': process_type, 'project_id': project_id,
                       'source_connection_id': source_connection_id, 'target_connection_id': target_connection_id,
                       'schema': schema_name, 'table_name': table_name, 'target_schema': target_schema,
                       'token_data': token_data, 'request_memory': '4000Mi', 'limit_memory': '4000Mi',
                       'data_load_type': data_load_type, 'file_name': file_name},
            executor_config=executor_config_memory,
            python_callable=table_migration,
            priority_weight=priority_dict[dag_id],
            weight_rule='absolute',
            trigger_rule='all_success',
            dag=dag)
        chain(pre_validation_task, e2e_migration_task)
    else:
        table_size = sum(item[5] for item in dag_tables_list)

        post_validation_task, deploy_table_constraints_task = '', ''

        pre_validation_task = PythonOperator(
            task_id='pre_validation_task',
            op_kwargs={'dag_id': dag_id, 'project_id': project_id, 'source_connection_id': source_connection_id,
                       'target_connection_id': target_connection_id, 'schema': schema_name,
                       'target_schema': target_schema, 'table_name': table_name, 'token_data': token_data,
                       'task_name': 'pre_validation_' + str(table_name), 'file_name': file_name,
                       'chunk_size': chunk_configuration_dict['Chunk_Size'], 'table_size': table_size,
                       'chunk_parts': len(dag_tables_list), 'concurrency': chunk_configuration_dict['Concurrency'],
                       'process_type': process_type, 'data_load_type': data_load_type,
                       'table_category': dag_tables_list[0][2]},
            python_callable=pre_validation_task_trigger,
            priority_weight=priority_dict[dag_id],
            weight_rule='absolute',
            trigger_rule='all_success',
            dag=dag)

        intial_load_tasks = []
        for index, table_list in enumerate(dag_tables_list):
            chunk_size = math.ceil(table_list[5])
            if 150 < chunk_size < 600:
                request_memory = '10000Mi'
                limit_memory = '10000Mi'
            elif 600 < chunk_size < 1000:
                request_memory = '15000Mi'
                limit_memory = '15000Mi'
            elif chunk_size > 1000:
                request_memory = f'{chunk_size + 15000}Mi'
                limit_memory = f'{chunk_size + 15000}Mi'
            else:
                request_memory = '3000Mi'
                limit_memory = '3000Mi'

            executor_config_memory = {
                "pod_override": k8s.V1Pod(
                    spec=k8s.V1PodSpec(
                        containers=[
                            k8s.V1Container(
                                name='base',
                                resources=k8s.V1ResourceRequirements(
                                    requests={'cpu': request_cpu, 'memory': request_memory},
                                    limits={'cpu': limit_cpu, 'memory': limit_memory})
                            )
                        ]
                    )
                ),
            }
            migration_task = PythonOperator(
                task_id=str(table_list[1]) + '_' + str(table_list[4]),
                op_kwargs={'dag_id': dag_id, 'task_name': str(table_list[4]),
                           'process_type': process_type, 'project_id': project_id,
                           'source_connection_id': source_connection_id, 'target_connection_id': target_connection_id,
                           'schema': schema_name, 'table_name': table_name, 'target_schema': target_schema,
                           'lower_bound': table_list[6], 'upper_bound': table_list[7], 'token_data': token_data,
                           'table_category': table_list[2], 'request_memory': request_memory,
                           'limit_memory': limit_memory, 'data_load_type': data_load_type},
                executor_config=executor_config_memory,
                python_callable=table_migration,
                priority_weight=priority_dict[dag_id],
                weight_rule='absolute',
                dag=dag)
            intial_load_tasks.append(migration_task)

        if target_connection_id not in ['', None]:
            post_validation_task = PythonOperator(
                task_id='post_validation_task',
                op_kwargs={'dag_id': dag_id, 'project_id': project_id, 'source_connection_id': source_connection_id,
                           'target_connection_id': target_connection_id, 'schema': schema_name,
                           'target_schema': target_schema, 'table_name': table_name, 'token_data': token_data,
                           'task_name': 'post_validation_' + str(table_name)},
                python_callable=post_validation_task_trigger,
                priority_weight=priority_dict[dag_id],
                weight_rule='absolute',
                trigger_rule='all_done',
                dag=dag)

            deploy_table_constraints_task = PythonOperator(
                task_id='deploy_constraints_task',
                op_kwargs={'dag_id': dag_id, 'project_id': project_id, 'source_connection_id': source_connection_id,
                           'target_connection_id': target_connection_id, 'schema': schema_name,
                           'target_schema': target_schema, 'table_name': table_name, 'token_data': token_data,
                           'task_name': 'deploy_table_constraints_' + str(table_name)},
                python_callable=deploy_table_constraints_task_trigger,
                priority_weight=priority_dict[dag_id],
                weight_rule='absolute',
                trigger_rule='all_success',
                dag=dag)

        complete_validation_task = PythonOperator(
            task_id='complete_validation_task',
            op_kwargs={'dag_id': dag_id, 'project_id': project_id, 'source_connection_id': source_connection_id,
                       'target_connection_id': target_connection_id, 'schema': schema_name,
                       'target_schema': target_schema, 'table_name': table_name, 'token_data': token_data,
                       'file_name': file_name, 'task_name': 'complete_validation_' + str(table_name),
                       'process_type': process_type},
            python_callable=complete_validation_task_trigger,
            priority_weight=priority_dict[dag_id],
            weight_rule='absolute',
            trigger_rule='all_success',
            dag=dag)

        if target_connection_id not in ['', None]:
            chain(pre_validation_task, intial_load_tasks, post_validation_task, deploy_table_constraints_task,
                  complete_validation_task)
        else:
            chain(pre_validation_task, intial_load_tasks, complete_validation_task)
    return dag


extra_path = os.environ['EXTRA_FOLDER']
excel_file = '{0}/@Config_File_Path'.format(extra_path)

config_read = pd.read_excel(excel_file, sheet_name='Configuration')
config_read.fillna('', inplace=True)
config_list = config_read.to_dict(orient='records')

config_data = {}
for i in config_list: config_data.update({i['Parameter']: i['Value']})

priority_read = pd.read_excel(excel_file, sheet_name='Priority_Weights')
priority_list = priority_read.to_dict(orient='records')
priority_dict = {}
for i in priority_list: priority_dict.update({i['Dag Name']: i['Priority_Weight']})

chunk_configuration_dict = {}
if os.path.isfile(f'{extra_path}/@Config_File_Folder/chunk_configuration.json'):
    with open(f'{extra_path}/@Config_File_Folder/chunk_configuration.json', 'r') as f:
        chunk_configuration_dict = json.loads(f.read())

token_data = api_authentication()

schedule_interval = None
pause_flag = True

dags_df = pd.read_excel(excel_file, sheet_name='Dags')
dags_df = dags_df.groupby('Dag Name')

for dag_name, dag_tables_list in dags_df:
    default_args = {'owner': 'airflow',
                    'start_date': datetime(2023, 9, 11, 7, 00, 00),
                    'retries': 3,
                    'retry_delay': timedelta(minutes=4)
                    }
    max_active_runs = 1

    if str(dag_name).startswith('CDC_'):
        tag_name = f"{config_data['File_Name']} CDC"
    elif str(dag_name).startswith('Load_Files_'):
        tag_name = f"{config_data['File_Name']} Load Files"
    elif str(dag_name).startswith('E2E_Migration_'):
        tag_name = f"{config_data['File_Name']} E2E Migration"
    else:
        schema_name = dag_tables_list['Schema_Name'].values[0]
        table_name = dag_tables_list['Table_Name'].values[0]
        tag_name = f"{schema_name}.{table_name} Data Migration"

    globals()[dag_name] = create_dag(dag_name, tag_name, schedule_interval, pause_flag, default_args, max_active_runs,
                                     config_data['Process_Type'], config_data['Project_Id'],
                                     config_data['Source_Connection_Id'], config_data['Target_Connection_Id'],
                                     config_data['Target_Schema'], dag_tables_list.values.tolist(),
                                     config_data['Data_Load_Type'], config_data['CDC_Load_Type'],
                                     config_data['Request_CPU'], config_data['Limit_CPU'], config_data['File_Name'],
                                     token_data, priority_dict, chunk_configuration_dict)
