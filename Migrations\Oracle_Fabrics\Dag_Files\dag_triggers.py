import os, re, requests, json, psycopg2, cx_Oracle, logging, csv, struct, pyodbc, sys
from datetime import datetime
import pandas as pd
from azure.identity import ClientSecretCredential
from azure.storage.blob import BlobServiceClient, ContentSettings
from deltalake import write_deltalake, DeltaTable, <PERSON>hem<PERSON>, Field
from azure.storage.filedatalake import DataLakeServiceClient
from itertools import chain, repeat
from pyspark.sql import SparkSession
import pyarrow
from io import BytesIO,StringIO
import ast
from databricks import sql
import base64


connection_url = os.getenv("API_HOST", 'http://qmig-eng.qmig-ns.svc.cluster.local:8080')
connection_url = connection_url + '/api/v1/'


def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


def DB_connection(database_data):
    host_name = database_data['host']
    port = database_data['port']
    service_name = database_data['db_name']
    user_name = database_data['name']
    password = database_data['password']
    try:
        dsn = cx_Oracle.makedsn(host=host_name, port=port, service_name=service_name)
        connection = cx_Oracle.Connection(user=user_name, password=password, dsn=dsn)
        error = ''
    except cx_Oracle.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near database connection", e)
    return connection, error


def target_DB_connection(db_data):
    try:
        connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                      host=db_data['host'], database=db_data['db_name'],
                                      port=db_data['port'])
        error = ''
    except psycopg2.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near target database connection", e)
    return connection, error


def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        data = cursor.fetchall()
        data = [[str(value) if isinstance(value, cx_Oracle.LOB) else value for value in row] for row in data]
    except cx_Oracle.DatabaseError as e:
        print("Issue found near database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data


def api_authentication():
    url = connection_url + 'Common/Security/VerifyPython'
    project_id = os.environ['PROJECT_ID']
    params = {
        "projectId": str(project_id),
        "content": "8/2DlAH8q1R+untey0ZuqMuD89WX4m2XoSCWhxQ+UOk="}
    response = requests.get(url, params=params)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = json.loads(status_response['text'])
        token_data = data['token']
        return token_data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def get_project_DB_info(token_data, project_id):
    url = connection_url + 'Common/Master/GetProjectMigDetailSelect'
    payload = {
        "projectId": str(project_id),
        "migsrcType": "D"
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.post(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = data_text['jsonResponseData']
        data_text = [dict for dict in data_text['Table1'] if dict['dbconname'] == 'PRJDB' + str(project_id)][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def get_source_DB_info(token_data, project_id, source_connection_id):
    url = connection_url + 'Common/Master/GetDBConnections'
    payload = {
        "projectId": str(project_id),
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.get(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = [dict for dict in data_text['Table1'] if
                     dict['migsrctgt'] == 'S' and dict['Connection_ID'] == source_connection_id][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def get_target_DB_info(token_data, project_id, target_connection_id):
    url = connection_url + 'Common/Master/GetDBConnections'
    payload = {
        "projectId": str(project_id),
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.get(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = [dict for dict in data_text['Table1'] if
                     dict['migsrctgt'] == 'T' and dict['Connection_ID'] == target_connection_id][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def decrypt_string(encrypted_str, token_data):
    application_url = connection_url + 'Common/Cryptography/DecryptData'
    payload = {
        "NeedToDecrypt": encrypted_str
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response = requests.post(application_url, data=json.dumps(payload), headers=headers)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = response.text
        return data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def decrypt_database_details(token_data, project_id, category, connection_id):
    prj_data = {}
    prj_db_data = {}
    if category == 'Project':
        prj_db_data = get_project_DB_info(token_data, project_id)
    elif category == 'Source':
        prj_db_data = get_source_DB_info(token_data, project_id, connection_id)
    elif category == 'Target':
        prj_db_data = get_target_DB_info(token_data, project_id, connection_id)
    password = decrypt_string(prj_db_data['dbpassword'], token_data)
    userid = decrypt_string(prj_db_data['dbuserid'], token_data)
    userid = re.split('\s+', re.sub(r"[\x00-\x1F\x7F]", ' ', userid))
    userid = ' '.join(userid).strip()
    password = re.split('\s+', re.sub(r"[\x00-\x1F\x7F]", ' ', password))
    password = ' '.join(password).strip()
    port = prj_db_data['dbport']
    host = prj_db_data['dbhost']
    if category == 'Project':
        service_name = ''
        parallelprocess = ''
    else:
        service_name = prj_db_data['service_name']
        parallelprocess = prj_db_data['parallelprocess']
    dbname = prj_db_data['dbname']
    prj_data['password'] = password
    prj_data['name'] = userid
    prj_data['port'] = port
    prj_data['host'] = host
    prj_data['db_name'] = dbname
    prj_data['service_name'] = service_name
    prj_data['parallelprocess'] = parallelprocess
    return prj_data


def get_config_id(connection, file_name, process_type):
    cursor = connection.cursor()
    try:
        cursor.execute(
            f"select config_id, transaction_number, transaction_file, config_status from audit_config where config_name = '{file_name}' and process_type = '{process_type}'")
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def transaction_insert(connection, config_id, transaction_number, transaction_file, config_status):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_transaction_insert(%s,%s,%s,%s,%s);fetch all in "dataset";',
                       (config_id, transaction_number, transaction_file,config_status, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data

def dag_insert(connection, dag_name, dag_type, schema_name, target_schema, table_name,
               table_size, concurrency, chunk_size, chunk_parts, dag_status, config_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_dags_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (dag_name, dag_type, schema_name, target_schema, table_name, table_size, concurrency, chunk_size,
             chunk_parts, dag_status, config_id, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def dag_update(connection, dag_id, dag_status, dag_end_time):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_audit_dags_update(%s,%s,%s,%s);fetch all in "dataset";',
                       (dag_id, dag_status,dag_end_time, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def task_insert(connection, task_name, task_type, attempt, lower_bound, upper_bound, request_memory, limit_memory, task_start_time,
                task_status, dag_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_task_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (task_name, task_type, attempt, str(lower_bound), str(upper_bound), request_memory, limit_memory, str(task_start_time),
             task_status, dag_id,'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def task_update(connection, task_id, task_row_count, task_status, task_error, task_end_time, extraction_time, transform_time, load_time):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_task_update(%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (task_id, task_row_count, task_status, task_error, task_end_time, extraction_time, transform_time, load_time,'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def parse_where_clause_to_df(where_clause: str,operatition: str) -> pd.DataFrame:
    # Extract column-value pairs
    pattern = r'"([^"]+)"\s*(=|IS)\s*(NULL|\'[^\']*\'|[\d.]+)'
    matches = re.findall(pattern, where_clause)

    data = {}
    for column, operator, value in matches:
        if value == 'NULL':
            data[column] = None
        elif value.startswith("'") and value.endswith("'"):
            data[column] = value.strip("'")
        else:
            # Try converting to int, if fails, keep as string (could be float)
            try:
                data[column] = int(value)
            except ValueError:
                data[column] = value
    
    if operatition == 'UPDATE':
        data['__rowMarker__'] = 1
    else:
        data['__rowMarker__'] = 2

    return pd.DataFrame([data])


def replace_where_values(where, set_dict):
    def replacer(match):
        column = match.group(1)
        operator = match.group(2)
        if column in set_dict:
            return f'"{column}" {operator} {set_dict[column]}'
        return match.group(0)

    # Handles = or IS and strings, numbers, NULL, or TIMESTAMP (will match them all now)
    pattern = r'"([^"]+)"\s*(=|IS)\s*(TIMESTAMP\s*\'[^\']+\'|NULL|\'[^\']+\'|\d+)'
    return re.sub(pattern, replacer, where)

def pre_validation_trigger(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    table_name = kwargs['table_name']
    file_name = kwargs['file_name']
    token_data = kwargs['token_data']
    dag_name = kwargs['dag_id']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
    source_connection, error = DB_connection(source_DB_details)

    process_type = 'Initial_Data_Load' if file_name.startswith('init_') else 'E2E_Data_Load'
    config_data = get_config_id(project_connection, file_name, process_type)
    config_id = config_data[0][0]

    if config_data[0][1] in ['', None]:
        scn_fetch_query = "SELECT CURRENT_SCN FROM V$DATABASE"
        scn_data = execute_query(source_connection, scn_fetch_query)
        scn_number = scn_data[0][0]

        transaction_insert(project_connection, config_id, scn_number, None, 'Running')
    else:
        scn_number = config_data[0][1]

    dag_id = dag_insert(project_connection, dag_name, 'Data_Migration', schema_name, target_schema, table_name, kwargs['table_size'],
                        kwargs['concurrency'], kwargs['chunk_size'], kwargs['chunk_parts'],
                        'Running', config_id)
    dag_id = dag_id[0][0]
    ti.xcom_push(key='dag_config', value={'dag_id': dag_id, 'config_id': config_id, 'scn_number': scn_number})

    task_start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_id = task_insert(project_connection, 'pre_validation_task', 'Pre_Validation', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    extra_path = os.environ['EXTRA_FOLDER']
    if target_connection_id in ['', None]:
        root_folder = extra_path + '/' + str(source_connection_id)
    else:
        root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

    scn_files_folder = root_folder + '/' + 'SCN_Files' + '/' + file_name.lower()
    if not os.path.exists(scn_files_folder):
        os.makedirs(scn_files_folder)

    current_scn_file = scn_files_folder + '/' + 'current_scn_file.txt'
    with open(current_scn_file, 'w') as cscn:
        cscn.write(str(scn_number))

    open_scn_file = scn_files_folder + '/' + 'open_scn_file.txt'
    with open(open_scn_file, 'w') as file_openscn:
        file_openscn.write('0')

    commit_scn_file = scn_files_folder + '/' + 'commit_scn_file.txt'
    with open(commit_scn_file, 'w') as file_csn:
        file_csn.write('0')

    task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_update(project_connection, task_id, None, 'Success', str(error), task_end_time, None, None, None)
    print(f"Pre validation completed for {table_name}")
    return False

def map_datatype(dtype, *args):
    oracle_to_databricks = {
        'NUMBER': lambda *_: 'DOUBLE',
        'INTEGER': lambda *_: 'INT',
        'FLOAT': lambda *_: 'FLOAT',
        'BINARY_FLOAT': lambda *_: 'FLOAT',
        'BINARY_DOUBLE': lambda *_: 'DOUBLE',
        'VARCHAR2': lambda *_: 'STRING',
        'VARCHAR': lambda *_: 'STRING',
        'CHAR': lambda *_: 'STRING',
        'NCHAR': lambda *_: 'STRING',
        'NVARCHAR2': lambda *_: 'STRING',
        'CLOB': lambda *_: 'STRING',
        'BLOB': lambda *_: 'BINARY',
        'RAW': lambda *_: 'BINARY',
        'DATE': lambda *_: 'DATE',
        'TIMESTAMP': lambda *_: 'TIMESTAMP',
        'TIMESTAMP WITH TIME ZONE': lambda *_: 'TIMESTAMP',
        'BOOLEAN': lambda *_: 'BOOLEAN',
        'LONG': lambda *_: 'STRING',
        'INTERVAL': lambda *_: 'STRING',
        'XMLTYPE': lambda *_: 'STRING',
        'ROWID': lambda *_: 'STRING',
        'UROWID': lambda *_: 'STRING'
    }
    mapper = oracle_to_databricks.get(dtype.upper(), lambda *_: 'STRING')
    return mapper()

def table_migration(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    table_name = kwargs['table_name']
    part_name = kwargs['task_name']
    token_data = kwargs['token_data']
    lower_bound = kwargs['lower_bound']
    upper_bound = kwargs['upper_bound']
    request_memory = kwargs['request_memory']
    limit_memory = kwargs['limit_memory']

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']
    scn_number = dag_config['scn_number']
    attempt = ti.try_number

    task_error = ''
    extraction_time = None
    transform_time = None
    load_time = None

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)
    task_start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_id = task_insert(project_connection, str(table_name).lower() + '_' + part_name.lower(), 'Data_Migration',
                          attempt,
                          lower_bound, upper_bound, request_memory, limit_memory, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]
    try:
        extra_path = os.environ['EXTRA_FOLDER']
        if target_connection_id in ['', None]:
            root_folder = extra_path + '/' + str(source_connection_id)
        else:
            root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

        log_path = root_folder + '/' + 'Data_Migration_Logs'
        if not os.path.exists(log_path):
            os.makedirs(log_path)
        task_logger = logging.getLogger(__name__)
        task_logger.setLevel(logging.DEBUG)
        log_handler = logging.FileHandler(log_path + '/' + str(table_name).lower() + '_' + part_name.lower() + '.log')
        log_handler.setFormatter(logging.Formatter("%(asctime)s [%(levelname)s] - %(message)s"))
        log_handler.addFilter(lambda record: record.levelno != logging.WARNING)
        task_logger.addHandler(log_handler)

        task_logger.debug(table_name)
        task_logger.debug('Table execution started')
        task_logger.debug(f'scn_number=={scn_number}')

        source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
        source_connection, error = DB_connection(source_DB_details)

        datatypes_fetch_query = "SELECT COLUMN_NAME,DATA_TYPE FROM ALL_TAB_COLUMNS WHERE table_name = '{0}' AND OWNER = '{1}' ORDER BY COLUMN_ID".format(
            table_name.upper(),
            schema_name.upper())
        datatypes_data = execute_query(source_connection, datatypes_fetch_query)

        target_datatypes_data = []
        for row in datatypes_data:
            if row[1].upper() == 'NUMBER':
                target_datatypes_data.append((row[0], 'double'))
            elif str(row[1]).upper() == 'FLOAT':
                target_datatypes_data.append((row[0], 'float'))
            elif str(row[1]).upper() == 'INTEGER':
                target_datatypes_data.append((row[0], 'int'))
            elif str(row[1]).upper() == 'DATE':
                target_datatypes_data.append((row[0], 'timestamp'))
            elif 'TIMESTAMP' in str(row[1]).upper():
                target_datatypes_data.append((row[0], 'timestamp'))
            else:
                target_datatypes_data.append((row[0], 'string'))

        column_names_list = [row[0] for row in datatypes_data]
        source_cursor = source_connection.cursor()
        source_cursor.execute(f"""SELECT cols.column_name FROM all_cons_columns cols JOIN all_constraints cons ON cons.constraint_name = cols.constraint_name WHERE cons.constraint_type = 'P' AND cons.owner = '{schema_name.upper()}' AND cons.table_name = '{table_name.upper()}'""")
        unqiue_columns = source_cursor.fetchall()
        unqiue_columns_string = unqiue_columns[0][0]
    
        source_data_query = """select /*+ rowid({1}) */ * from {0}.{1} AS OF SCN {2} where rowid >= '{3}' and rowid <= '{4}'""".format(
            schema_name.upper(), table_name.upper(), scn_number, str(lower_bound), str(upper_bound))
        task_logger.debug(source_data_query)

        extraction_start_time = datetime.now()
        
        source_cursor.arraysize = 100000
        source_cursor.prefetchrows = 100001
        source_cursor.execute(source_data_query)
        table_data_list = source_cursor.fetchall()
        source_cursor.close()
        extraction_end_time = datetime.now()
        extraction_time = (extraction_end_time - extraction_start_time).total_seconds() / 60

        print(f"Extracted records: {len(table_data_list)}")

        if table_data_list:
            transform_start_time = datetime.now()
            df = pd.DataFrame(table_data_list, columns=column_names_list)
            transform_end_time = datetime.now()
            transform_time = (transform_end_time - transform_start_time).total_seconds() / 60

            target_DB_details = decrypt_database_details(token_data, project_id, 'Target',
                                                         str(target_connection_id))
            credential = ClientSecretCredential(target_DB_details['service_name'], target_DB_details['name'],
                                                target_DB_details['password'])
            if 'abfss://' in target_DB_details['host'] and 'Tables' in target_DB_details['host']:
                delta_token = credential.get_token("https://storage.azure.com/.default").token
                storage_options = {"bearer_token": delta_token, "use_fabric_endpoint": "true"}
                delta_table_path = target_DB_details['host']

                fields = [Field(name, data_type) for name, data_type in target_datatypes_data]
                schema_object = Schema(fields)

                load_start_time = datetime.now()
                DeltaTable.create(table_uri=f"{delta_table_path}/{schema_name}/{table_name}",
                                  schema=schema_object.to_pyarrow(),
                                  storage_options=storage_options,
                                  mode='ignore')
                print(f"{delta_table_path}/{schema_name}/{table_name}")
                write_deltalake(f"{delta_table_path}/{schema_name}/{table_name}", df, schema=schema_object.to_pyarrow(),
                                mode='append',
                                storage_options=storage_options)
                load_end_time = datetime.now()

            elif 'https://' in target_DB_details['host'] and 'Files' in target_DB_details['host']:
                extra_path = os.environ['EXTRA_FOLDER']
                file_path = extra_path + f"/Data_Migration_Files/{table_name}"
                file_name = f"{str(part_name.split('Part_')[1]).zfill(20)}.parquet"
                metadata_file_name = file_path+"/_metadata.json"
                final_sheet_name = file_path +"/"+file_name

                onelake_account_url = "https://onelake.dfs.fabric.microsoft.com"
                file_system_name = target_DB_details['host'].split('onelake.dfs.fabric.microsoft.com/')[1]
                oneLake_file_name = f"{table_name}/{str(part_name.split('Part_')[1]).zfill(20)}.parquet"
                load_start_time = datetime.now()
                service_client = DataLakeServiceClient(account_url=onelake_account_url, credential=credential)
                file_system_client = service_client.get_file_system_client(file_system_name)
                file_client = file_system_client.get_file_client(oneLake_file_name)
                oneLake_metadata_file_name = f"{table_name}/_metadata.json"
                metadata_file_client = file_system_client.get_file_client(oneLake_metadata_file_name)


                if not os.path.exists(file_path):
                    os.makedirs(file_path)
                print(metadata_file_name)
                if not os.path.isfile(metadata_file_name):
                    print(metadata_file_name)
                    metadata_content = {"keyColumns": [unqiue_columns_string]}
                    json_object = json.dumps(metadata_content)
                    with open(metadata_file_name, "w") as outfile:
                        outfile.write(json_object)
                    with open(metadata_file_name,"rb") as data:
                        metadata_file_client.upload_data(data, overwrite=True)
                    
                df.to_parquet(final_sheet_name)
                
                
                with open(final_sheet_name,"rb") as data:
                    file_client.upload_data(data, overwrite=True)
               
                load_end_time = datetime.now()

            elif 'azuredatabricks.net' in target_DB_details['host']:
                print('Datatype test=====593',datatypes_data)
                ddl = f"CREATE TABLE IF NOT EXISTS {target_DB_details['name']}.{schema_name}_{table_name} (\n"
                ddl += ",\n".join([
                    f"  {col[0].lower()} {map_datatype(col[1])}" for col in datatypes_data
                ])
                ddl += "\n) USING DELTA;"
                transform_start_time = datetime.now()
                df = pd.DataFrame(table_data_list, columns=column_names_list)
                column_names_string = ",".join(column_names_list)
                
                table_data = df.to_csv(index=False, encoding="utf-8",header=False)
                
                transform_end_time = datetime.now()
                transform_time = (transform_end_time - transform_start_time).total_seconds() / 60

                target_DB_details = decrypt_database_details(token_data, project_id, 'Target',
                                                         str(target_connection_id))
                
                connection_string = "DefaultEndpointsProtocol=https;AccountName=qmigstg1137;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
                container_name = 'databricks'
                blob_service_client = BlobServiceClient.from_connection_string(connection_string)
                container_client = blob_service_client.get_container_client(container_name)
                if not container_client.exists():
                    container_client.create_container()
                blob_client = container_client.get_blob_client(f"{schema_name}_{table_name}.csv")
                blob_client.upload_blob(table_data, overwrite=True,
                                        content_settings=ContentSettings(content_type="text/csv"))

                target_connection = sql.connect(
                        server_hostname = f"{target_DB_details['host']}", 
                        http_path = f"{target_DB_details['db_name']}", 
                        access_token = f"{target_DB_details['service_name']}") 
                        

                target_cursor = target_connection.cursor()
                data_load_query = f"""COPY INTO {target_DB_details['name']}.{schema_name}_{table_name} ({column_names_string})
                        FROM '/mnt/datamigration/{schema_name}_{table_name}.csv'
                        FILEFORMAT = CSV
                       FORMAT_OPTIONS ('header' = 'false')
                        """
                
                load_start_time = datetime.now()
                target_cursor.execute(ddl)
                target_cursor.execute(data_load_query)
                target_connection.commit()
                target_connection.close()
                
                blob_client.delete_blob()

                load_end_time = datetime.now()


            else:
                if target_connection_id in ['', None]:
                    root_folder = str(source_connection_id)
                else:
                    root_folder = str(source_connection_id) + '/' + str(target_connection_id)
                data_files_path = f"{root_folder}/Data_Files/{schema_name.lower()}/{table_name.lower()}"
                file_name = data_files_path + '/' + table_name.lower() + '_' + part_name.lower() + '.csv'

                csv_data = df.to_csv(index=False, encoding="utf-8")

                connection_string = "DefaultEndpointsProtocol=https;AccountName=qmigstg1137;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
                container_name = 'qmigrator'
                blob_service_client = BlobServiceClient.from_connection_string(connection_string)
                container_client = blob_service_client.get_container_client(container_name)
                if not container_client.exists():
                    container_client.create_container()
                blob_client = container_client.get_blob_client(file_name)
                blob_client.upload_blob(csv_data, overwrite=True,
                                        content_settings=ContentSettings(content_type="text/csv"))

                connection_string = f"Driver={{ODBC Driver 18 for SQL Server}};Server={target_DB_details['host']},1433;Database={target_DB_details['db_name']};Encrypt=Yes;TrustServerCertificate=No"
                token_object = credential.get_token("https://database.windows.net//.default")
                token_as_bytes = bytes(token_object.token, "UTF-8")
                encoded_bytes = bytes(chain.from_iterable(zip(token_as_bytes, repeat(0))))
                token_bytes = struct.pack("<i", len(encoded_bytes)) + encoded_bytes
                attrs_before = {1256: token_bytes}

                if 'datawarehouse.fabric.microsoft.com' in target_DB_details['host']:
                    query = f"""COPY INTO {schema_name.upper()}.{table_name.upper()}
                        FROM 'https://qmigstg1137.blob.core.windows.net/{container_name}/{file_name}'
                        WITH (
                        FILE_TYPE = 'CSV',
                        CREDENTIAL=(IDENTITY= 'Storage Account Key', SECRET='****************************************************************************************'),
                        FIRSTROW =2
                        )
                        """
                else:
                    query = f"""BULK INSERT {schema_name.upper()}.{table_name.upper()}
                        FROM '{file_name}'
                        WITH (
                            DATA_SOURCE = 'MyBlobStorage',
                            FIELDTERMINATOR = ',', 
                            ROWTERMINATOR = '\n', 
                            FIRSTROW = 2  
                        );
                        """

                load_start_time = datetime.now()
                target_connection = pyodbc.connect(connection_string, attrs_before=attrs_before)
                target_cursor = target_connection.cursor()
                target_cursor.execute(query)
                target_connection.commit()
                target_cursor.close()
                target_connection.close()
                load_end_time = datetime.now()

            load_time = (load_end_time - load_start_time).total_seconds() / 60

            task_status = 'Success'
            task_logger.debug('Table execution ended')

            task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')

            project_connection = connect_database(project_DB_details)
            task_update(project_connection, task_id, len(table_data_list), task_status, None, task_end_time,
                        extraction_time, transform_time, load_time)
    except Exception as e:
        task_status = 'Fail'
        task_error = str(e)

        task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        project_connection = connect_database(project_DB_details)
        task_update(project_connection, task_id, None, task_status, task_error, task_end_time, extraction_time, transform_time,
                    load_time)
        ti.UP_FOR_RETRY


def complete_validation_trigger(ti, **kwargs):
    process_type = kwargs['process_type']
    project_id = kwargs['project_id']
    token_data = kwargs['token_data']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    table_name = kwargs['table_name']
    schema_name = kwargs['schema']

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)
    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
    source_connection, error = DB_connection(source_DB_details)
    source_row_cnt_query = "SELECT count(*) from {1}.{0}".format(
        table_name.upper(),
        schema_name.upper())
    source_row_cnt1 = execute_query(source_connection, source_row_cnt_query)
    print(source_row_cnt1)
    source_row_cnt = source_row_cnt1[0][0]
    target_row_cnt = source_row_cnt1[0][0]
    diff_count = target_row_cnt - source_row_cnt
    table_dict = {"Schema_name_Source":schema_name,"Table_Name": table_name,"Table_Type" : "Table","Schema_name_Target": schema_name,"Status" : "Available in both Oracle and Fabric"}
    table_df = pd.DataFrame([table_dict])
    table_row_count_dict ={"Source_Schema":schema_name,"Target_Schema": schema_name,"Table_Name": table_name,"Source_Row_Count":source_row_cnt,"Target_Row_Count": target_row_cnt,"Difference_Count": diff_count }
    table_row_count_df = pd.DataFrame([table_row_count_dict])
    extra_path = os.environ['EXTRA_FOLDER']
    file_path = extra_path +"/Validation_Reports"
    file_cretion_time =datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    file_name = f"Post_Validation_report_{file_cretion_time}.xlsx"
    final_sheet_name = file_path +"/"+file_name
    table_df.to_excel(final_sheet_name, sheet_name="Table")
    table_row_count_df.to_excel(final_sheet_name, sheet_name="Table_Row_Count")


    task_start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_id = task_insert(project_connection, 'complete_validation_task', 'Complete_Validation', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]
    
   
    task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_update(project_connection, task_id, None, 'Success', None, task_end_time, None, None, None)

    if process_type in ('Initial_Data_Load','E2E_Data_Load'):
        dag_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        dag_update(project_connection, dag_id, 'Success', dag_end_time)
    return False


def remove_double_quotes(sql):
    in_single_quote = False
    to_lowercase = False
    result = []
    buffer = []
    for char in sql:
        if char == "'" and not in_single_quote:
            in_single_quote = True
            segment = ''.join(buffer)
            if to_lowercase:
                segment = re.sub(r'\"([^\"]+)\"', lambda m: m.group(1).lower(), segment)
            else:
                segment = segment.replace('"', '')
            result.append(segment)
            buffer = []
        elif char == "'" and in_single_quote:
            in_single_quote = False

        if in_single_quote:
            result.append(''.join(buffer) + char)
            buffer = []
        else:
            buffer.append(char)
    if buffer:
        segment = ''.join(buffer)
        if to_lowercase:
            segment = re.sub(r'\"([^\"]+)\"', lambda m: m.group(1).lower(), segment)
        else:
            segment = segment.replace('"', '')
        result.append(segment)
    return ''.join(result)


def transform_statement(batch_statement, target_host):
    if re.match(r"^INSERT", batch_statement, re.IGNORECASE):
        before_values = re.split(r'(?i)values', batch_statement)[0]
        before_values = before_values.replace('.', '/').replace('(', '`(')
        before_values = re.sub(r'(?i)INSERT INTO ', f'INSERT INTO delta.`{target_host}/', before_values,
                               flags=re.IGNORECASE | re.DOTALL)

        after_values = re.split(r'(?i)values', batch_statement)[1]

        batch_statement = before_values + ' VALUES ' + after_values

    elif re.match(r"^UPDATE", batch_statement, re.IGNORECASE):
        before_set = re.split(r'(?i) SET', batch_statement)[0]
        before_set = before_set.replace('.', '/')
        before_set = re.sub(r'(?i)UPDATE ', f'UPDATE delta.`{target_host}/', before_set, flags=re.IGNORECASE | re.DOTALL)

        after_set = re.split(r'(?i) SET', batch_statement)[1]

        batch_statement = before_set + '` SET ' + after_set

    elif re.match(r"^DELETE", batch_statement, re.IGNORECASE):
        before_where = re.split(r'(?i) WHERE', batch_statement)[0]
        before_where = before_where.replace('.', '/')
        before_where = re.sub(r'(?i)DELETE FROM ', f'DELETE FROM delta.`{target_host}/', before_where, flags=re.IGNORECASE | re.DOTALL)

        after_where = re.split(r'(?i) WHERE', batch_statement)[1]
        batch_statement = before_where + '` WHERE ' + after_where

    if 'EMPTY_BLOB()' in batch_statement or 'EMPTY_CLOB()' in batch_statement:
        batch_statement = batch_statement.replace("EMPTY_BLOB()", "NULL").replace("EMPTY_CLOB()", "NULL")
    if 'HEXTORAW' in batch_statement:
        hex_list = re.findall(r'\bHEXTORAW\(.*?\)', batch_statement,
                              flags=re.DOTALL | re.I)
        for word in hex_list:
            modified_word = word.rstrip(')') + ",'hex')"
            batch_statement = batch_statement.replace(word, modified_word)
        batch_statement = re.sub(r"HEXTORAW\(", r'decode(', batch_statement)
    return batch_statement


def open_new_csv_file(file_name):
    print(f'Creating CSV file {file_name}')
    csv_file = open(file_name, mode='w', newline='')
    writer = csv.writer(csv_file)
    writer.writerow(['Start_SCN', 'Statement', 'Commit_SCN'])
    csv_file.flush()
    return csv_file, writer


def cdc_data_insert(connection, batch_number, batch_length, batch_size, load_type, data_file_name, batch_start_time,
                    batch_extraction_time, batch_transform_time, batch_load_time, batch_end_time, task_id):
    cursor = connection.cursor()
    try:
        print(batch_number,"===", batch_length,"===", batch_size,"===", load_type,"===", data_file_name,"===", batch_start_time,"===", batch_extraction_time,"===",
             batch_transform_time,"===", batch_load_time,"===", batch_end_time,"===", task_id, 'dataset')
        cursor.execute(
            'call public.sp_audit_cdc_data_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (batch_number, batch_length, str(batch_size), load_type, data_file_name, batch_start_time, batch_extraction_time,
             batch_transform_time, batch_load_time, batch_end_time, task_id, 'dataset'))
        data = cursor.fetchall()
    except Exception as error:
        data = None
        print(f"Error occurred at cdc data insert: {str(error)}")
        pass
    finally:
        connection.commit()
        cursor.close()
    return data




def cdc_errors_insert(connection, batch_error_list):
    cdc_error_insert_query = "insert into public.audit_cdc_error_tracking (transaction, transaction_error, transaction_error_time, data_id) values (%s,%s,%s,%s,%s)"

    cursor = connection.cursor()
    try:
        cursor.executemany(cdc_error_insert_query, batch_error_list)
    except Exception as error:
        print(f"Error occurred at cdc data insert: {str(error)}")
        pass
    finally:
        connection.commit()
        cursor.close()


def parse_timestamp(value):
    if isinstance(value, str):
        # Check if the string matches the timestamp pattern and convert to datetime
        if " " in value:
            try:
                return datetime.strptime(value.strip(), "%Y-%m-%d %H:%M:%S")
            except ValueError:
                pass
        # Check if it's just a date without time
        try:
            return datetime.strptime(value.strip(), "%Y-%m-%d").date()  # Date only (without time)
        except ValueError:
            pass
    return value
    


def cdc_trigger(ti,**kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    table_name = kwargs['table_name']
    batch_size = 1000
    config_file_name = kwargs['file_name']
    cdc_load_type = kwargs['cdc_load_type']
    token_data = kwargs['token_data']
    file_name = kwargs['file_name']
    dag_name = kwargs['dag_id']
    process_type = kwargs['process_type']
    table_name = table_name.split('.')[1]
    print(table_name)

    log_file_list = []

    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
    target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))
    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    extra_path = os.environ['EXTRA_FOLDER']
    root_folder = extra_path  + '/' + str(source_connection_id) + '/' + str(target_connection_id)
    scn_files_folder = root_folder + '/' + 'SCN_Files' + '/' + file_name.lower()
    cdc_data_files_folder = root_folder + '/' + 'CDC_Data_Files' 
    
        
    if table_name in ['', None]:
        scn_files_folder = extra_path + '/' + 'CDC_SCN_Files' + '/' + str(source_DB_details['db_name']).capitalize()
        if not os.path.exists(scn_files_folder):
            os.makedirs(scn_files_folder)
        
        cdc_data_files_folder = extra_path + '/' + 'CDC_Data_Files' + '/' + str(
            source_DB_details['db_name']).capitalize()
        
        source_connection, error = DB_connection(source_DB_details)
        source_cursor = source_connection.cursor()
        scn_fetch_query = "SELECT CURRENT_SCN FROM V$DATABASE"
        scn_data = execute_query(source_connection, scn_fetch_query)
        scn_number = scn_data[0][0]
        
        current_scn_file = scn_files_folder + '/' + 'current_scn_file.txt'
        with open(current_scn_file, 'w') as f_csn:
            f_csn.write(str(scn_number))
        print(scn_number, 'SCN Number as current scn')
        
        open_scn_file = scn_files_folder + '/' + 'open_scn_file.txt'
        with open(open_scn_file, 'w') as file_openscn:
            file_openscn.write(str(0))

        commit_scn_file = scn_files_folder + '/' + 'commit_scn_file.txt'
        with open(commit_scn_file, 'w') as file_commitscn:
            file_commitscn.write(str(0))
    else:
        current_scn_file = scn_files_folder + '/' + 'current_scn_file.txt'
        with open(current_scn_file, 'r') as cscn:
            current_scn = cscn.readline()

        open_scn_file = scn_files_folder + '/' + 'open_scn_file.txt'
        with open(open_scn_file, 'r') as file_openscn:
            open_scn = file_openscn.readline()

        commit_scn_file = scn_files_folder + '/' + 'commit_scn_file.txt'
        with open(commit_scn_file, 'r') as file_csn:
            commit_scn = file_csn.readline()


    if not os.path.exists(scn_files_folder):
        os.makedirs(scn_files_folder)

    if not os.path.exists(cdc_data_files_folder):
        os.makedirs(cdc_data_files_folder)

    current_scn_file = scn_files_folder + '/' + 'current_scn_file.txt'
    with open(current_scn_file, 'r') as file_openscn:
        current_scn = int(file_openscn.readline())

    open_scn_file = scn_files_folder + '/' + 'open_scn_file.txt'
    with open(open_scn_file, 'r') as file_openscn:
        open_scn = int(file_openscn.readline())
    print(open_scn, "==open_scn")

    commit_scn_file = scn_files_folder + '/' + 'commit_scn_file.txt'
    with open(commit_scn_file, 'r') as file_csn:
        commit_scn = int(file_csn.readline())
    print(commit_scn, "==commit_scn")

    config_data = get_config_id(project_connection, file_name, process_type)
    config_id = config_data[0][0]
    dag_id = dag_insert(project_connection, dag_name, 'CDC', schema_name, target_schema, table_name,
                        None,
                         None, None, None,
                        'Running', config_id)
    dag_id = dag_id[0][0]

    task_start_time = datetime.now()
    task_id = task_insert(project_connection, 'cdc_' + str(file_name), 'CDC', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    if open_scn == 0:
        final_scn = current_scn
    else:
        if current_scn < open_scn:
            final_scn = current_scn
        else:
            final_scn = open_scn
    print(final_scn, '==final_scn')


    

    source_connection, error = DB_connection(source_DB_details)
    source_cursor = source_connection.cursor()

    get_sequence_query = f"SELECT MIN(SEQUENCE#) AS SEQ# FROM V$LOG_HISTORY WHERE FIRST_CHANGE# <= {final_scn} AND NEXT_CHANGE# > {final_scn}"
    source_cursor.execute(get_sequence_query)
    sequence_number = source_cursor.fetchone()
    sequence_number = sequence_number[0]

    if sequence_number is not None:
        get_log_paths_query = (
            f"SELECT 'DBMS_LOGMNR.ADD_LOGFILE(''' ||NAME||''');' AS name, SEQUENCE#, THREAD#, deleted FROM V$ARCHIVED_LOG WHERE SEQUENCE# >={sequence_number}")

        source_cursor.execute(get_log_paths_query)
        log_paths = source_cursor.fetchmany(100)

    else:
        get_log_paths_query = f"""SELECT DISTINCT 'DBMS_LOGMNR.ADD_LOGFILE(''' ||MEMBER||''');' AS name,e.* FROM V$LOGFILE e JOIN v$log f ON e.GROUP# =f.GROUP# where e.IS_RECOVERY_DEST_FILE= 'NO'  and ARCHIVED = 'NO'"""
        source_cursor.execute(get_log_paths_query)
        log_paths = source_cursor.fetchall()

    log_file_list.extend([item[0] for item in log_paths])
    log_path_string = '\n'.join([str(item[0]) for item in log_paths])
    log_path_formatted_query = """begin \n{}\n end;""".format(log_path_string)
    print(log_path_formatted_query)
    try:
        source_cursor.execute(log_path_formatted_query)
    except Exception as e:
        print(e)

    log_miner_query = f"""
        begin
       DBMS_LOGMNR.START_LOGMNR(
          STARTSCN =>{final_scn}, -- or use STARTTIME or STARTDATE as needed
           OPTIONS => DBMS_LOGMNR.NO_ROWID_IN_STMT + DBMS_LOGMNR.STRING_LITERALS_IN_STMT + DBMS_LOGMNR.DICT_FROM_ONLINE_CATALOG
       ); END;"""
    
    try:
        source_cursor.execute(log_miner_query)
    except Exception as e:
        get_log_paths_query = f"""SELECT DISTINCT 'DBMS_LOGMNR.ADD_LOGFILE(''' ||MEMBER||''');' AS name,e.* FROM V$LOGFILE e JOIN v$log f ON e.GROUP# =f.GROUP# where e.IS_RECOVERY_DEST_FILE= 'NO'  and ARCHIVED = 'NO'"""
        source_cursor.execute(get_log_paths_query)
        log_paths = source_cursor.fetchall()
        log_path_string = '\n'.join([str(item[0]) for item in log_paths])
        log_path_formatted_query = """begin \n{}\n end;""".format(log_path_string)
        
        try:
            source_cursor.execute(log_path_formatted_query)
        except Exception as e:
            print(e)
        
        log_miner_query = f"""
        begin
        DBMS_LOGMNR.START_LOGMNR(
          STARTSCN =>{final_scn}, -- or use STARTTIME or STARTDATE as needed
           OPTIONS => DBMS_LOGMNR.NO_ROWID_IN_STMT + DBMS_LOGMNR.STRING_LITERALS_IN_STMT + DBMS_LOGMNR.DICT_FROM_ONLINE_CATALOG
        ); END;"""
        source_cursor.execute(log_miner_query)
    

    unqiue_columns_string = ''
    try:
        print(schema_name,"=====",table_name)
        print("")
        source_cursor.execute(f"""SELECT cols.column_name FROM all_cons_columns cols JOIN all_constraints cons ON cons.constraint_name = cols.constraint_name WHERE cons.constraint_type = 'P' AND cons.owner = '{schema_name.upper()}' AND cons.table_name = '{table_name.upper()}'""")
        unqiue_columns = source_cursor.fetchall()
        unqiue_columns_string = unqiue_columns[0][0]
    except Exception as e:
        print(e)

    

    batch_number = 1

    file_limit_size = 1 * 1024 * 1024  # 5MB size
    start_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S-%f")

    if 'abfss://' in target_DB_details['host'] and 'Tables' in target_DB_details['host'] and unqiue_columns_string in ['',None]:
        data_fetch_query = f"""SELECT DISTINCT (e.XIDUSN || '.' || e.XIDSLT || '.' || e.XIDSQN) AS XID,
                                    e.USERNAME,
                                    e.SCN,f.SCN as SCN_FOR_COMMIT,
                                    f.START_SCN,
                                    e.SQL_REDO,
                                    f.COMMIT_SCN,
                                    e.OPERATION, f.OPERATION as OPERATION_COMMIT,
                                    e.ROLLBACK
                    FROM V$LOGMNR_CONTENTS e
                    INNER JOIN V$LOGMNR_CONTENTS f
                        ON (e.XIDUSN || '.' || e.XIDSLT || '.' || e.XIDSQN) = (f.XIDUSN || '.' || f.XIDSLT || '.' || f.XIDSQN)
                    WHERE e.SEG_OWNER in ('{schema_name.upper()}') and e.TABLE_NAME in ('{table_name.upper()}') and f.SCN > {final_scn}
                      AND (e.XIDUSN || '.' || e.XIDSLT || '.' || e.XIDSQN) NOT IN (
                          SELECT DISTINCT (r.XIDUSN || '.' || r.XIDSLT || '.' || r.XIDSQN) AS rollback_xid
                          FROM V$LOGMNR_CONTENTS r
                          WHERE r.ROLLBACK = 1
                      )
                    ORDER BY e.SCN"""
        current_file_name = cdc_data_files_folder + '/' + f"CDC_{schema_name}_{table_name}_{start_time}_batch_{batch_number}.csv"
    elif 'abfss://' in target_DB_details['host'] and 'Tables' in target_DB_details['host']  and unqiue_columns_string not in ['',None]:
        data_fetch_query =f""" SELECT JSON_OBJECT(
                   'scn' VALUE SCN,
                   'operation' VALUE OPERATION,
                   'table' VALUE TABLE_NAME,
                   'columns' VALUE REGEXP_SUBSTR(SQL_REDO, '\\((.*?)\\)', 1, 1, NULL, 1),
                   'values' VALUE REGEXP_SUBSTR(SQL_REDO, 'values\\s*\\((.*?)\\)', 1, 1, NULL, 1),
                   'set' VALUE CASE WHEN OPERATION = 'UPDATE' THEN REGEXP_SUBSTR(SQL_REDO, 'set\\s*(.*?)where ', 1, 1, NULL, 1) END,
                   'update_where' VALUE CASE WHEN OPERATION = 'UPDATE' THEN REGEXP_SUBSTR(SQL_REDO, 'where\\s*(.*);', 1, 1, NULL, 1) END,
                   'delete_where' VALUE CASE WHEN OPERATION = 'DELETE' THEN REGEXP_SUBSTR(SQL_REDO, 'where\\s*(.*);', 1, 1, NULL, 1) END
               ) AS logminer_json
                FROM V$LOGMNR_CONTENTS E
                WHERE OPERATION IN ('INSERT', 'UPDATE', 'DELETE')
                AND SEG_OWNER = '{schema_name.upper()}'
                AND TABLE_NAME = '{table_name.upper()}'
                AND SCN > {final_scn}
                FETCH FIRST {batch_size} ROWS ONLY"""
        current_file_name = cdc_data_files_folder + '/' + f"CDC_{schema_name}_{table_name}_{start_time}_batch_{batch_number}.csv"
    elif 'https://' in target_DB_details['host'] and 'Files' in target_DB_details['host']:
        data_fetch_query =f"""SELECT OPERATION,SQL_REDO,SCN FROM V$LOGMNR_CONTENTS E 
                              WHERE OPERATION IN ('INSERT','UPDATE','DELETE') 
                              AND SEG_OWNER = '{schema_name.upper()}'
                              AND TABLE_NAME = '{table_name.upper()}'
                              AND SCN > {final_scn}
                              FETCH FIRST {batch_size} ROWS ONLY"""
        current_file_name = cdc_data_files_folder + '/' + f"CDC_{schema_name}_{table_name}_{start_time}_batch_{batch_number}.csv"
    else:
        data_fetch_query =f"""SELECT SCN,SQL_REDO FROM V$LOGMNR_CONTENTS E 
                              WHERE OPERATION IN ('INSERT','UPDATE','DELETE') 
                              AND SEG_OWNER = '{schema_name.upper()}'
                              AND TABLE_NAME = '{table_name.upper()}'
                              AND SCN > {final_scn}
                              FETCH FIRST {batch_size} ROWS ONLY"""
        current_file_name = cdc_data_files_folder + '/' + f"CDC_{schema_name}_{table_name}_{start_time}_batch_{batch_number}.csv"

    
    source_cursor.execute(data_fetch_query)
    csv_file, writer = open_new_csv_file(current_file_name)
    
    fetch_start_time ,fetch_end_time= datetime.now(),datetime.now()
    transformation_start_time , transformation_end_time = datetime.now(),datetime.now()
    load_start_time, load_end_time = datetime.now(),datetime.now()
    if 'abfss://' in target_DB_details['host'] and unqiue_columns_string in ['',None]:
        print("in if====")
        spark = SparkSession.builder \
                            .appName("AzureDeltaLakeInsert") \
                            .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
                            .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
                            .config("spark.jars.packages", "io.delta:delta-spark_2.12:3.3.0,org.apache.hadoop:hadoop-azure:3.3.1,org.apache.hadoop:hadoop-azure-datalake:3.3.1") \
                            .config("spark.executor.memory", "4g") \
                            .config("spark.driver.memory", "4g") \
                            .config("spark.sql.shuffle.partitions", "200") \
                            .config("spark.logConf", "true") \
                            .getOrCreate()
    
    
        while True:
            fetch_start_time = datetime.now()
            batch_data = source_cursor.fetchmany(batch_size)
            fetch_end_time = datetime.now()
    
            if batch_data:
                print(f'Batch Length {len(batch_data)} and Batch Number {batch_number}')
                transformation_start_time = datetime.now()
                batch_df = pd.DataFrame(batch_data,
                                        columns=['XID', 'USERNAME', 'SCN', 'SCN_FOR_COMMIT', 'START_SCN', 'SQL_REDO',
                                                 'COMMIT_SCN', 'OPERATION', 'OPERATION_COMMIT', 'ROLLBACK'])
    
                open_transaction_scn_list = \
                    batch_df.groupby('XID').filter(lambda x: 'COMMIT' not in x['OPERATION_COMMIT'].values)[
                        'SCN'].drop_duplicates().tolist()
                committed_transactions = batch_df[batch_df['OPERATION_COMMIT'] == 'COMMIT']
                committed_transactions_scn_list = committed_transactions['SCN'].drop_duplicates().tolist()
    
                if committed_transactions_scn_list:
                    batch_start_time = datetime.now()
                    batch_error_list = []
    
                    open_transaction_scn_list = [item for item in open_transaction_scn_list if
                                                 item not in committed_transactions_scn_list]
                    print(open_transaction_scn_list, '===open transaction list')
                    if open_transaction_scn_list:
                        open_scn = min(open_transaction_scn_list)
                        with open(open_scn_file, 'w') as f_open:
                            f_open.write(str(open_scn))
    
                    target_DB_details = decrypt_database_details(token_data, project_id, 'Target',
                                                                 str(target_connection_id))
                    
                    statement_list = committed_transactions['SQL_REDO'].tolist()
                    statement_list = cdc_st_transform(statement_list, target_DB_details['host'])
                    
                    batch_length = len(statement_list)
                    batch_memory = sum(sys.getsizeof(statement) for statement in statement_list)
                    batch_statement = '\n'.join(statement_list)
                    batch_statement = remove_double_quotes(batch_statement)
                    batch_statement = transform_statement(batch_statement)
                    transformation_end_time = datetime.now()

                    # Set Azure Data Lake configurations
                    spark.conf.set("fs.azure.account.auth.type", "OAuth")
                    spark.conf.set("fs.azure.account.oauth.provider.type", "org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider")
                    spark.conf.set("fs.azure.account.oauth2.client.id", target_DB_details['name'])
                    spark.conf.set("fs.azure.account.oauth2.client.secret", target_DB_details['password'])
                    spark.conf.set("fs.azure.account.oauth2.client.endpoint", "https://login.microsoftonline.com/0eadb77e-42dc-47f8-bbe3-ec2395e0712c/oauth2/token")
                    spark.conf.set("spark.databricks.delta.optimizeWrite.enabled", "true")
                    spark.conf.set("spark.sql.debug.maxToStringFields", "100")
                    batch_error_list = []
                    try:
                        load_start_time = datetime.now()
                        batch_end_scn = int(batch_df['SCN'].max())
                        batch_commit_scn = int(committed_transactions['SCN_FOR_COMMIT'].max())
    
                        final_scn = batch_end_scn
                        commit_scn = batch_commit_scn
                        print(final_scn, '===final and commit in first try==', commit_scn)
                        if cdc_load_type == 'Database':
                            spark.sql(batch_statement)
                        elif cdc_load_type == 'File':
                            print(f"========Writing to CSV: {batch_statement}")
                            file_df = committed_transactions[['SCN', 'SQL_REDO', 'SCN_FOR_COMMIT']]
                            file_df['SQL_REDO'] = file_df['SQL_REDO'].apply(remove_double_quotes)
                            file_df['SQL_REDO'] = file_df['SQL_REDO'].apply(transform_statement)
                            file_df.to_csv(csv_file, header=False, index=False)
                            csv_file.flush()
                            file_size = os.path.getsize(current_file_name)
                            print(f"========Current file size: {file_size}")
    
                            if file_size >= file_limit_size:
                                print(f"File size limit reached. Closing {current_file_name} Renaming file...")
                                csv_file.close()
                                end_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S-%f")
                                replaced_current_file_name = current_file_name.replace('.csv', f'_{end_time}.csv')
                                renamed_file_name = cdc_data_files_folder + '/' + replaced_current_file_name
                                try:
                                    os.rename(current_file_name, renamed_file_name)
                                    print(f"File renamed to: {renamed_file_name}")
                                except Exception as e:
                                    print(f"Error renaming file: {e}")
                                start_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S-%f")
                                if table_name in ['', None]:
                                    current_file_name = cdc_data_files_folder + '/' + f"CDC_{start_time}_batch_{batch_number}.csv"
                                else:
                                    current_file_name = cdc_data_files_folder + '/' + f"CDC_{schema_name}_{table_name}_{start_time}_batch_{batch_number}.csv"
                                csv_file, writer = open_new_csv_file(current_file_name)
                        load_end_time = datetime.now()
    
    
                        if isinstance(final_scn, int) and final_scn is not None:
                            with open(current_scn_file, 'w') as f_csn:
                                f_csn.write(str(final_scn + 1))
    
                        if isinstance(commit_scn, int) and commit_scn is not 0:
                            with open(commit_scn_file, 'w') as f_commit:
                                f_commit.write(str(commit_scn))
    
                    except Exception as error:
                        print("Error occurred at data loading to Target: " + str(error))
    
                        for index, record in committed_transactions.iterrows():
                            end_scn = record['SCN']
                            record_commit_scn = int(record['SCN_FOR_COMMIT'])
                            statement = record['SQL_REDO']
                            
                            statement = cdc_st_transform(statement, target_DB_details['host'])
                            
                            statement = remove_double_quotes(statement)
                            statement = transform_statement(statement)
                            try:
                                batch_end_scn = end_scn
                                batch_commit_scn = record_commit_scn
                                final_scn = batch_end_scn
                                commit_scn = batch_commit_scn
                                print(final_scn, '===final and commit in second try==', commit_scn)
    
                                
                                spark.sql(statement)
                                if isinstance(final_scn, int) and final_scn is not None:
                                    with open(current_scn_file, 'w') as f_csn:
                                        f_csn.write(str(final_scn + 1))
    
                                if isinstance(commit_scn, int) and commit_scn is not 0:
                                    with open(commit_scn_file, 'w') as f_commit:
                                        f_commit.write(str(commit_scn))
                            except Exception as error:
                                print("Error occurred at statement: " + str(statement) + " loading to Target: " + str(
                                    error))
    
                                error_tuple = (
                                    config_id, batch_number, tuple(record.values), str(error), datetime.now())
                                batch_error_list.append(error_tuple)
                        load_end_time = datetime.now()
                    if cdc_load_type == 'Database':
                        batch_end_time = datetime.now()
                        time_taken_by_batch = batch_end_time - batch_start_time
                        time_diff_seconds = time_taken_by_batch.total_seconds()
                        time_diff_minutes = round((time_diff_seconds / 60), 2)
                        extraction_time = (fetch_end_time-fetch_start_time).total_seconds() * 10**3
                        transform_time = (transformation_end_time - transformation_start_time).total_seconds() * 10**3
                        load_time = (load_end_time-load_start_time).total_seconds() * 10**3
    
                        project_connection = connect_database(project_DB_details)
                        project_cursor = project_connection.cursor()
                        
    
                        # error_insert_statement = "insert into public.prj_cdc_error_data (config_id,batch_number,transaction,error,error_time) values (%s,%s,%s,%s,%s)"
                        # project_cursor.executemany(error_insert_statement, batch_error_list)
    
                        # if table_name in ['', None]:
                        #     meta_data_query = "insert into public.prj_cdc_metadata (config_id,operation_name,source_connection_id,target_connection_id,source_schema,table_name,target_schema,batch_number,batch_start_time,batch_end_time,batch_statments_length,batch_statements_size,batch_duration,created_by,extraction_time, transform_time, load_time) values ({0},'{1}',{2}, {3},'{4}','{5}','{6}', {7}, '{8}', '{9}', {10}, {11}, {12}, '{13}','{14}','{15}','{16}')".format(
                        #         config_id, 'CDC', source_connection_id, target_connection_id, None, None,
                        #         None, batch_number,
                        #         batch_start_time, batch_end_time, batch_length, batch_memory, time_diff_minutes, 'Python',extraction_time, transform_time, load_time)
                        # else:
                        #     meta_data_query = "insert into public.prj_cdc_metadata (config_id,operation_name,source_connection_id,target_connection_id,source_schema,table_name,target_schema,batch_number,batch_start_time,batch_end_time,batch_statments_length,batch_statements_size,batch_duration,created_by,extraction_time, transform_time, load_time) values ({0},'{1}',{2}, {3},'{4}','{5}','{6}', {7}, '{8}', '{9}', {10}, {11}, {12}, '{13}','{14}','{15}','{16}')".format(
                        #         config_id, 'CDC', source_connection_id, target_connection_id, schema_name, table_name,
                        #         target_schema, batch_number,
                        #         batch_start_time, batch_end_time, batch_length, batch_memory, time_diff_minutes, 'Python',extraction_time, transform_time, load_time)
                        # project_cursor.execute(meta_data_query)
                        # project_cursor.close()
                        # project_connection.commit()

                        project_connection = connect_database(project_DB_details)
                        cdc_data_id = cdc_data_insert(project_connection, batch_number, batch_length, batch_size, cdc_load_type,
                                                              None, batch_start_time, extract_time, transform_time, load_time,
                                                              batch_end_time, task_id)
                        if batch_error_list:
                            batch_error_list = [t + (cdc_data_id,) for t in batch_error_list]
                            cdc_errors_insert(project_connection, batch_error_list)
    
                    batch_number = batch_number + 1
            else:
                max_scn_query = "SELECT max(START_SCN) FROM V$LOGMNR_CONTENTS"
                source_cursor.execute(max_scn_query)
                max_scn = source_cursor.fetchall()
    
                with open(commit_scn_file, 'r') as file_csn:
                    commit_scn = int(file_csn.readline())
                if commit_scn not in [None,'',0]:
                   final_scn = commit_scn
                else:
                    final_scn= max_scn[0][0]


                if isinstance(final_scn, int) and final_scn is not None:
                    with open(current_scn_file, 'w') as f_csn:
                        f_csn.write(str(final_scn + 1))
    
                print(final_scn, '===final and commit in else==', commit_scn)
    
                get_sequence_query = (f"SELECT MIN(SEQUENCE#) AS SEQ# FROM V$LOG_HISTORY "
                                      f"WHERE FIRST_CHANGE# <= {final_scn + 1} AND "
                                      f"NEXT_CHANGE# > {final_scn + 1}")
                source_cursor.execute(get_sequence_query)
                sequence_number = source_cursor.fetchone()
                sequence_number = sequence_number[0]
    
                if sequence_number is not None:
                    get_log_paths_query = (
                        f"SELECT 'DBMS_LOGMNR.ADD_LOGFILE(''' ||NAME||''');' AS name, SEQUENCE#, THREAD#, deleted FROM V$ARCHIVED_LOG WHERE SEQUENCE# >={sequence_number}")
    
                    source_cursor.execute(get_log_paths_query)
                    log_paths = source_cursor.fetchmany(100)
    
                    log_paths_list = [item[0] for item in log_paths]
                    distinct_log_files = [value for value in log_paths_list if value not in log_file_list]
    
                    print(distinct_log_files)
                    if len(distinct_log_files) > 0:
                        log_path_string = '\n'.join([str(item) for item in distinct_log_files])
                        log_path_formatted_query = """begin \n{}\n end;""".format(log_path_string)
                        print(log_path_formatted_query)
                        source_cursor.execute(log_path_formatted_query)
                        log_file_list.extend(distinct_log_files)
                    else:
                        get_log_paths_query = f"""SELECT DISTINCT 'DBMS_LOGMNR.ADD_LOGFILE(''' ||MEMBER||''');' AS name,e.* FROM V$LOGFILE e JOIN v$log f ON e.GROUP# =f.GROUP# where e.IS_RECOVERY_DEST_FILE= 'NO'  and ARCHIVED = 'NO'"""
                        source_cursor.execute(get_log_paths_query)
                        else_log_paths = source_cursor.fetchall()
                        else_log_paths_list = [item[0] for item in else_log_paths]
                        else_distinct_log_files = [value for value in else_log_paths_list if value not in log_file_list]
                        if len(else_distinct_log_files) > 0:
                            else_log_path_string = '\n'.join([str(item) for item in else_distinct_log_files])
                            else_log_path_formatted_query = """begin \n{}\n end;""".format(else_log_path_string)
                            try:
                                source_cursor.execute(else_log_path_formatted_query)
                                log_file_list.extend(else_distinct_log_files)
                            except Exception as e:
                                logfile_names_list = re.findall(r"(\w:\\.*?\.LOG)", str(e))
                                except_log_path_string = [
                                    "DBMS_LOGMNR.ADD_LOGFILE('" + logfile.replace("\\", "\\\\") + "');" for
                                    logfile in logfile_names_list]
                                except_log_path_list = [value for value in logfile_names_list if
                                                        value not in log_file_list]
    
                                if len(except_log_path_list) > 0:
                                    except_else_log_path_string = '\n'.join([str(item) for item in except_log_path_list])
                                    except_else_log_path_formatted_query = """begin \n{}\n end;""".format(
                                        except_else_log_path_string)
                                    source_cursor.execute(except_else_log_path_formatted_query)
                                    log_file_list.extend(except_log_path_string)
    
                if open_scn is 0:
                    log_miner_query = f""" begin
                                           DBMS_LOGMNR.START_LOGMNR(
                                              STARTSCN =>{final_scn + 1}, -- or use STARTTIME or STARTDATE as needed
                                               OPTIONS => DBMS_LOGMNR.NO_ROWID_IN_STMT + DBMS_LOGMNR.STRING_LITERALS_IN_STMT + DBMS_LOGMNR.DICT_FROM_ONLINE_CATALOG
                                           ); END;"""
                else:
                    log_miner_query = f"""
                            begin
                           DBMS_LOGMNR.START_LOGMNR(
                              STARTSCN =>{open_scn}, -- or use STARTTIME or STARTDATE as needed
                               OPTIONS => DBMS_LOGMNR.NO_ROWID_IN_STMT + DBMS_LOGMNR.STRING_LITERALS_IN_STMT + DBMS_LOGMNR.DICT_FROM_ONLINE_CATALOG
                           ); END;"""
                print(log_miner_query, '===in else case')
                source_cursor.execute(log_miner_query)
    
                data_fetch_query = f"""SELECT DISTINCT (e.XIDUSN || '.' || e.XIDSLT || '.' || e.XIDSQN) AS XID,
                                                    e.USERNAME,
                                                    e.SCN,f.SCN as SCN_FOR_COMMIT,
                                                    f.START_SCN,
                                                    e.SQL_REDO,
                                                    f.COMMIT_SCN,
                                                    e.OPERATION, f.OPERATION as OPERATION_COMMIT,
                                                    e.ROLLBACK
                                    FROM V$LOGMNR_CONTENTS e
                                    INNER JOIN V$LOGMNR_CONTENTS f
                                        ON (e.XIDUSN || '.' || e.XIDSLT || '.' || e.XIDSQN) = (f.XIDUSN || '.' || f.XIDSLT || '.' || f.XIDSQN)
                                    WHERE e.SEG_OWNER in ('{schema_name.upper()}') and e.TABLE_NAME in ('{table_name.upper()}') and f.SCN > {commit_scn}
                                      AND (e.XIDUSN || '.' || e.XIDSLT || '.' || e.XIDSQN) NOT IN (
                                          SELECT DISTINCT (r.XIDUSN || '.' || r.XIDSLT || '.' || r.XIDSQN) AS rollback_xid
                                          FROM V$LOGMNR_CONTENTS r
                                          WHERE r.ROLLBACK = 1
                                      )
                                    ORDER BY e.SCN"""
                print(data_fetch_query, '===in else case')
                source_cursor.execute(data_fetch_query)
    

    elif ('abfss://' in target_DB_details['host'] or 'datawarehouse.fabric.microsoft.com' in target_DB_details['host'])  and unqiue_columns_string not in ['',None]:
        print("in elif==========")
        batch_number = 1
        while True:
            fetch_start_time = datetime.now()
            batch_data = source_cursor.fetchall()
            fetch_end_time = datetime.now()


            source_cursor.execute("""SELECT LISTAGG(COLUMN_NAME, ',') WITHIN GROUP (ORDER BY COLUMN_ID) AS column_names FROM all_tab_columns WHERE owner || '.' || table_name = :schema_name||'.'||:table_name""",schema_name=schema_name.upper(),table_name=table_name.upper())
            results = source_cursor.fetchall()
            columns_string = results[0][0]
            columns_list = columns_string.split(',')
            columns_list.append('OPERATITION')
            transformation_start_time = datetime.now()
            df = pd.DataFrame(columns=columns_list)
            if batch_data:
                
                for row in batch_data:
                    
                    logminer_json = row[0] 
                    data = json.loads(logminer_json)  # Parse the JSON string into a Python dictionary


                    if data["operation"] == "INSERT":
                        commit_scn = data["scn"]
                        values = data["values"]
                        columns_names = data["columns"]

                        # Step 1: Extract column names
                        column_list = [col.strip('"') for col in columns_names.split(",")]
                        print(values)
                        values_cleaned = re.sub(r"TIMESTAMP\s+'([^']+)'", r"'\1'", values)  # Replace TIMESTAMP '...' with '...'
                        print(values_cleaned)
                        values_cleaned = values_cleaned.replace("NULL", "None")  # Replace NULL with None
                        print(values_cleaned)
                        values_list = eval(f"[{values_cleaned}]")  # Convert the cleaned string to a list of values
                        output_dict = {column_list[i]: values_list[i] for i in range(len(column_list))}
                        output_dict['OPERATITION'] = 'INSERT'
                        for key, value in output_dict.items():
                            if isinstance(value, str) and ("TIMESTAMP" in value or "DATE" in key.upper()):
                                output_dict[key] = parse_timestamp(value)

                        df = pd.concat([df, pd.DataFrame([output_dict])], ignore_index=True)

                    if data["operation"] == "UPDATE":
                        commit_scn = data["scn"]
                        ##set condition
                        set_string = data["set"]
                        # Clean the string to be in a valid JSON format:
                        # - Replace `=` with `:` for key-value pairs
                        # - Replace single quotes with double quotes for valid JSON string formatting
                        set_string = re.sub(r"TIMESTAMP\s*'([^']+)'", r'"\1"', set_string)
                        clean_string = "{" + set_string.replace(" = ", ":").replace("'", "\"") + "}"
                        set_dict = json.loads(clean_string)
                        for key, value in set_dict.items():
                            if isinstance(value, str) and ("TIMESTAMP" in value or "DATE" in key.upper()):
                                set_dict[key] = parse_timestamp(value)

                        #where condition
                        where_string = data["update_where"]
                        where_string = re.sub(r"TIMESTAMP\s*'([^']+)'", r'"\1"', where_string)
                        # Replace IS None with null for valid JSON
                        where_string = where_string.replace("IS None", "IS null")
                        # Replace IS null with valid JSON null
                        where_string = where_string.replace("IS NULL", ": null")
                        # Clean up the string for valid JSON formatting
                        where_clean_string = "{" + where_string.replace(" = ", ":").replace("'", "\"").replace(" and ", ", ") + "}"
                        where_dict = json.loads(where_clean_string)
                        #print(where_dict,'where_dict')


                        for key, value in set_dict.items():
                            if key in where_dict:  # Check if the key exists in the dictionary
                                where_dict[key] = value  # Update the value

                        for key, value in where_dict.items():
                            #print(key,value,'key,values')
                            if isinstance(value, str) and ("TIMESTAMP" in value or "DATE" in key.upper()):
                                where_dict[key] = parse_timestamp(value)

                        #updated_where_clean_string = json.dumps(where_dict, indent=4)
                        #print(where_dict,'where_dict',type(where_dict))
                        where_dict['OPERATITION'] = 'UPDATE'
                        #print(where_dict)
                        #print(where_dict,'where_dict')
                        df = pd.concat([df, pd.DataFrame([where_dict])], ignore_index=True)

                    if data["operation"] == "DELETE":
                        commit_scn = data["scn"]
                        where_string = data["delete_where"]
                        if where_string.startswith("ROWID = '"):
                            print("Skipping this file because it starts with 'ROWID = '")
                        else:
                            #print(where_string,'where_string')
                            where_string = re.sub(r"TIMESTAMP\s*'([^']+)'", r'"\1"', where_string)
                            # Replace IS None with null for valid JSON
                            where_string = where_string.replace("IS None", "IS null")
                            # Replace IS null with valid JSON null
                            where_string = where_string.replace("IS NULL", ": null")
                            # Clean up the string for valid JSON formatting
                            where_clean_string = "{" + where_string.replace(" = ", ":").replace("'", "\"").replace(" and ", ", ") + "}"
                            #print(where_clean_string,'where_clean_string')
                            where_dict = json.loads(where_clean_string)
                            #print(where_dict,'delete condition')
                            where_dict['OPERATITION'] = 'DELETE'
                            df = pd.concat([df, pd.DataFrame([where_dict])], ignore_index=True)
                if 'datawarehouse.fabric.microsoft.com' in target_DB_details['host']:
                    batch_length = len(df)
                    batch_memory = df.memory_usage(deep=True).sum()
                    output = df.to_csv(index = False, encoding="utf-8")
                    current_time = datetime.now()
                    batch_start_time = datetime.now()
                    load_start_time = datetime.now()
                    batch_error_list= []
                    file_name_time = current_time.strftime("%Y_%m_%d_%Hh_%Mm_%Ss")
                    file_name  = f"CDC_Data_Files/{schema_name}/{table_name}/{table_name}_{file_name_time}.csv"
                    try:
                        blob_service = BlobServiceClient.from_connection_string(f"DefaultEndpointsProtocol=https;AccountName=qmigstg1137;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net")
                        container_client = blob_service.get_container_client('qmigrator')
                        blob_client = blob_service.get_blob_client(container='qmigrator', blob=f"{file_name}") 

                        blob_client.upload_blob(output,overwrite=True,content_settings=ContentSettings(content_type="text/csv"))
                        credential = ClientSecretCredential(target_DB_details['service_name'], target_DB_details['name'], target_DB_details['password'])
                        connection_string = f"Driver={{ODBC Driver 18 for SQL Server}};Server={target_DB_details['host']},1433;Database={target_DB_details['db_name']};Encrypt=Yes;TrustServerCertificate=No"
                        params = urllib.parse.quote(connection_string)
                        token_object = credential.get_token("https://database.windows.net//.default")
                        token_as_bytes = bytes(token_object.token, "UTF-8")
                        encoded_bytes = bytes(chain.from_iterable(zip(token_as_bytes, repeat(0)))) 
                        token_bytes = struct.pack("<i", len(encoded_bytes)) + encoded_bytes 
                        attrs_before = {1256: token_bytes}
                        target_connection = pyodbc.connect(connection_string, attrs_before=attrs_before)
                        target_cursor = target_connection.cursor()
                        sql_query = f"""EXEC SyncTables 
                            @file_path = 'https://qmigstg1137.blob.core.windows.net/qmigrator/{file_name}',
                            @table_name = '{table_name.upper()}',
                            @IdColumn = '{unqiue_columns_string}',
                            @schema_name = '{schema_name.upper()}';
                        """
                        print(sql_query)
                        target_cursor.execute(sql_query)
                        target_connection.commit()
                        target_cursor.close()
                        target_connection.close()
                        if isinstance(commit_scn, int) and commit_scn is not 0:
                            with open(commit_scn_file, 'w') as f_commit:
                                f_commit.write(str(commit_scn))
                    except Exception as e:
                        error_tuple = (
                                        config_id, batch_number, None, str(e), datetime.now())
                        batch_error_list.append(error_tuple)
                        print(e)

                else:
                    df = df.groupby(unqiue_columns_string).last().reset_index()


                    batch_length = len(df)
                    batch_memory = df.memory_usage(deep=True).sum()


                    filtered_df = df[df['OPERATITION'].isin(['INSERT', 'UPDATE'])]
                    delete_ids = df[df['OPERATITION'] == 'DELETE'][unqiue_columns_string].tolist()
                    transformation_end_time = datetime.now()
                    sql_query = f"{unqiue_columns_string} in ({', '.join(map(str, delete_ids))})"

                    #filtered_df = filtered_df.sort_values('OPERATITION', ascending=False).drop_duplicates('AHCID', keep='first')

                    table = pyarrow.Table.from_pandas(filtered_df, preserve_index=False)
                    batch_start_time = datetime.now()
                    load_start_time = datetime.now()
                    batch_error_list= []
                    try:
                        if not filtered_df.empty or delete_ids:
                            print(f'fetch tables from fabric =')
                            credential = ClientSecretCredential(target_DB_details['service_name'], target_DB_details['name'], target_DB_details['password'])

                            delta_token = credential.get_token("https://storage.azure.com/.default").token
                            storage_options = {"bearer_token": delta_token, "use_fabric_endpoint": "true"}

                            # delta_table_path = f'abfss://<EMAIL>/cdc_lakehouse.Lakehouse/Tables'
                            delta_table_path = target_DB_details['host']
                            print (f'{delta_table_path}/{schema_name.upper()}/{table_name.upper()}')

                            dt = DeltaTable(table_uri=f"{delta_table_path}/{schema_name.upper()}/{table_name.upper()}", storage_options=storage_options)


                        if not filtered_df.empty:
                            print('merge records found')
                            predicate_condition = f's."{unqiue_columns_string}" = t."{unqiue_columns_string}"'
                            print(predicate_condition,"===predicate_condition")
                            print(table,"==table")

                            (
                            dt.merge(
                            source=table,
                            predicate=predicate_condition,#'s."AHCID" = t."AHCID"',#no of column need to check
                            source_alias="s",
                            target_alias="t",
                            )
                            .when_not_matched_insert_all()
                            .when_matched_update_all()
                            .execute()
                            )

                        if  delete_ids:
                            print('delete statements found') 
                            dt.delete(sql_query)

                        if isinstance(commit_scn, int) and commit_scn is not 0:
                            with open(commit_scn_file, 'w') as f_commit:
                                f_commit.write(str(commit_scn))
                    except Exception as e:
                        error_tuple = (
                                        config_id, batch_number, None, str(e), datetime.now())
                        batch_error_list.append(error_tuple)
                        print(e)

                load_end_time = datetime.now()

                batch_end_time = datetime.now()
                extraction_time = (fetch_end_time- fetch_start_time).total_seconds() * 10**3
                transform_time = (transformation_end_time - transformation_start_time).total_seconds() * 10**3
                load_time = (load_end_time - load_start_time).total_seconds() * 10**3
                time_taken_by_batch = batch_end_time - batch_start_time
                time_diff_seconds = time_taken_by_batch.total_seconds()
                time_diff_minutes = round((time_diff_seconds / 60), 2)
    
                project_connection = connect_database(project_DB_details)
                project_cursor = project_connection.cursor()
                print(batch_error_list)

                project_connection = connect_database(project_DB_details)
                cdc_data_id = cdc_data_insert(project_connection, batch_number, batch_length, batch_size, cdc_load_type,
                                                      None, batch_start_time, extraction_time, transform_time, load_time,
                                                      batch_end_time, task_id)
                if batch_error_list:
                    batch_error_list = [t + (cdc_data_id,) for t in batch_error_list]
                    cdc_errors_insert(project_connection, batch_error_list)
    
                # error_insert_statement = "insert into public.prj_cdc_error_data (config_id,batch_number,transaction,error,error_time) values (%s,%s,%s,%s,%s)"
                # project_cursor.executemany(error_insert_statement, batch_error_list
                #                            )
    
                # if table_name in ['', None]:
                #     meta_data_query = "insert into public.prj_cdc_metadata (config_id,operation_name,source_connection_id,target_connection_id,source_schema,table_name,target_schema,batch_number,batch_start_time,batch_end_time,batch_statments_length,batch_statements_size,batch_duration,created_by,extraction_time, transform_time, load_time) values ({0},'{1}',{2}, {3},'{4}','{5}','{6}', {7}, '{8}', '{9}', {10}, {11}, {12}, '{13}','{14}','{15}','{16}')".format(
                #         config_id, 'CDC', source_connection_id, target_connection_id, None, None,
                #         None, batch_number,
                #         batch_start_time, batch_end_time, batch_length, batch_memory, time_diff_minutes, 'Python',extraction_time, transform_time, load_time)
                # else:
                #     meta_data_query = "insert into public.prj_cdc_metadata (config_id,operation_name,source_connection_id,target_connection_id,source_schema,table_name,target_schema,batch_number,batch_start_time,batch_end_time,batch_statments_length,batch_statements_size,batch_duration,created_by,extraction_time, transform_time, load_time) values ({0},'{1}',{2}, {3},'{4}','{5}','{6}', {7}, '{8}', '{9}', {10}, {11}, {12}, '{13}','{14}','{15}','{16}')".format(
                #         config_id, 'CDC', source_connection_id, target_connection_id, schema_name, table_name,
                #         target_schema, batch_number,
                #         batch_start_time, batch_end_time, batch_length, batch_memory, time_diff_minutes, 'Python',extraction_time, transform_time, load_time)
                # project_cursor.execute(meta_data_query)
                # project_cursor.close()
                # project_connection.commit()
                batch_number = batch_number+1
    
            else:
                max_scn_query = "SELECT max(START_SCN) FROM V$LOGMNR_CONTENTS"
                source_cursor.execute(max_scn_query)
                max_scn = source_cursor.fetchall()
                print(commit_scn_file,"=========",current_scn_file)
                with open(commit_scn_file, 'r') as file_csn:
                    print(file_csn.readline())
                    commit_scn = int(file_csn.readline())
                with open(current_scn_file, 'r') as file_csn:
                    print(file_csn.readline())
                    current_scn = int(file_csn.readline())
                if commit_scn not in [None,'',0]:
                   final_scn = commit_scn
                else:
                    final_scn= current_scn
                    


                if isinstance(final_scn, int) and final_scn is not None:
                    with open(current_scn_file, 'w') as f_csn:
                        f_csn.write(str(final_scn + 1))
    
                print(final_scn, '===final and commit in else==', commit_scn)
    
                get_sequence_query = (f"SELECT MIN(SEQUENCE#) AS SEQ# FROM V$LOG_HISTORY "
                                      f"WHERE FIRST_CHANGE# <= {final_scn + 1} AND "
                                      f"NEXT_CHANGE# > {final_scn + 1}")
                source_cursor.execute(get_sequence_query)
                sequence_number = source_cursor.fetchone()
                sequence_number = sequence_number[0]
    
                if sequence_number is not None:
                    get_log_paths_query = (
                        f"SELECT 'DBMS_LOGMNR.ADD_LOGFILE(''' ||NAME||''');' AS name, SEQUENCE#, THREAD#, deleted FROM V$ARCHIVED_LOG WHERE SEQUENCE# >={sequence_number}")
    
                    source_cursor.execute(get_log_paths_query)
                    log_paths = source_cursor.fetchmany(batch_size)
    
                    log_paths_list = [item[0] for item in log_paths]
                    distinct_log_files = [value for value in log_paths_list if value not in log_file_list]
    
                    print(distinct_log_files)
                    if len(distinct_log_files) > 0:
                        log_path_string = '\n'.join([str(item) for item in distinct_log_files])
                        try:
                            log_path_formatted_query = """begin \n{}\n end;""".format(log_path_string)
                        except Exception as e:
                            print(e)
                        print(log_path_formatted_query)
                        source_cursor.execute(log_path_formatted_query)
                        log_file_list.extend(distinct_log_files)
                    else:
                        get_log_paths_query = f"""SELECT DISTINCT 'DBMS_LOGMNR.ADD_LOGFILE(''' ||MEMBER||''');' AS name,e.* FROM V$LOGFILE e JOIN v$log f ON e.GROUP# =f.GROUP# where e.IS_RECOVERY_DEST_FILE= 'NO'  and ARCHIVED = 'NO'"""
                        source_cursor.execute(get_log_paths_query)
                        else_log_paths = source_cursor.fetchall()
                        else_log_paths_list = [item[0] for item in else_log_paths]
                        else_distinct_log_files = [value for value in else_log_paths_list if value not in log_file_list]
                        if len(else_distinct_log_files) > 0:
                            else_log_path_string = '\n'.join([str(item) for item in else_distinct_log_files])
                            else_log_path_formatted_query = """begin \n{}\n end;""".format(else_log_path_string)
                            try:
                                source_cursor.execute(else_log_path_formatted_query)
                                log_file_list.extend(else_distinct_log_files)
                            except Exception as e:
                                logfile_names_list = re.findall(r"(\w:\\.*?\.LOG)", str(e))
                                except_log_path_string = [
                                    "DBMS_LOGMNR.ADD_LOGFILE('" + logfile.replace("\\", "\\\\") + "');" for
                                    logfile in logfile_names_list]
                                except_log_path_list = [value for value in logfile_names_list if
                                                        value not in log_file_list]
    
                                if len(except_log_path_list) > 0:
                                    except_else_log_path_string = '\n'.join([str(item) for item in except_log_path_list])
                                    except_else_log_path_formatted_query = """begin \n{}\n end;""".format(
                                        except_else_log_path_string)
                                    source_cursor.execute(except_else_log_path_formatted_query)
                                    log_file_list.extend(except_log_path_string)
    
                if open_scn is 0:
                    log_miner_query = f""" begin
                                           DBMS_LOGMNR.START_LOGMNR(
                                              STARTSCN =>{final_scn + 1}, -- or use STARTTIME or STARTDATE as needed
                                               OPTIONS => DBMS_LOGMNR.NO_ROWID_IN_STMT + DBMS_LOGMNR.STRING_LITERALS_IN_STMT + DBMS_LOGMNR.DICT_FROM_ONLINE_CATALOG
                                           ); END;"""
                else:
                    log_miner_query = f"""
                            begin
                           DBMS_LOGMNR.START_LOGMNR(
                              STARTSCN =>{open_scn}, -- or use STARTTIME or STARTDATE as needed
                               OPTIONS => DBMS_LOGMNR.NO_ROWID_IN_STMT + DBMS_LOGMNR.STRING_LITERALS_IN_STMT + DBMS_LOGMNR.DICT_FROM_ONLINE_CATALOG
                           ); END;"""
                print(log_miner_query, '===in else case')
                source_cursor.execute(log_miner_query)
    
                data_fetch_query = f"""SELECT JSON_OBJECT(
                   'scn' VALUE SCN,
                   'operation' VALUE OPERATION,
                   'table' VALUE TABLE_NAME,
                   'columns' VALUE REGEXP_SUBSTR(SQL_REDO, '\\((.*?)\\)', 1, 1, NULL, 1),
                   'values' VALUE REGEXP_SUBSTR(SQL_REDO, 'values\\s*\\((.*?)\\)', 1, 1, NULL, 1),
                   'set' VALUE CASE WHEN OPERATION = 'UPDATE' THEN REGEXP_SUBSTR(SQL_REDO, 'set\\s*(.*?)where ', 1, 1, NULL, 1) END,
                   'update_where' VALUE CASE WHEN OPERATION = 'UPDATE' THEN REGEXP_SUBSTR(SQL_REDO, 'where\\s*(.*);', 1, 1, NULL, 1) END,
                   'delete_where' VALUE CASE WHEN OPERATION = 'DELETE' THEN REGEXP_SUBSTR(SQL_REDO, 'where\\s*(.*);', 1, 1, NULL, 1) END
                    ) AS logminer_json
                    FROM V$LOGMNR_CONTENTS E
                    WHERE OPERATION IN ('INSERT', 'UPDATE', 'DELETE')
                    AND SEG_OWNER = '{schema_name.upper()}'
                    AND TABLE_NAME = '{table_name.upper()}'
                    AND SCN > {final_scn+1}
                    FETCH FIRST {batch_size} ROWS ONLY"""
                print(data_fetch_query, '===in else case')
                source_cursor.execute(data_fetch_query)

    elif 'https://' in target_DB_details['host'] and 'Files' in target_DB_details['host']:
        batch_number = 1
        while True:
            fetch_start_time = datetime.now()
            batch_data = source_cursor.fetchall()
            fetch_end_time = datetime.now()
            batch_start_time = datetime.now()
            source_cursor.execute("""SELECT LISTAGG(COLUMN_NAME, ',') WITHIN GROUP (ORDER BY COLUMN_ID) AS column_names FROM all_tab_columns WHERE owner || '.' || table_name = :schema_name||'.'||:table_name""",schema_name=schema_name.upper(),table_name=table_name.upper())
            results = source_cursor.fetchall()
            columns_string = results[0][0]
            columns_list = columns_string.split(',')
            columns_list.append('__rowMarker__')
            df = pd.DataFrame(columns=columns_list)
            if batch_data:
                batch_length = len(batch_data)
                batch_memory = sum(sys.getsizeof(batch_data) for statement in batch_data)
                    
                batch_error_list=[]
                data_records =''

                transformation_start_time = datetime.now()
                for row in batch_data:
                    if 'INSERT' == row[0]:
                        scn_number = row[2]
                        columns_match = re.search(r'\((.*?)\)\s*values', row[1], re.DOTALL | re.IGNORECASE)
                        columns = [col.strip().strip('"') for col in columns_match.group(1).split(',')]
            
                        # Extract values part
                        values_match = re.search(r'values\s*\((.*?)\);', row[1], re.DOTALL | re.IGNORECASE)
                        values_raw = values_match.group(1)
            
                        # Replace TIMESTAMP and NULL with quoted values or None
                        values_raw = re.sub(r"TIMESTAMP\s*'([^']*)'", r"'''\1'''", values_raw)# timestamp is removing
                        values_raw = values_raw.replace("NULL", "None")
            
                        # Convert to actual Python list
                        values_list = ast.literal_eval("[" + values_raw + "]")
            
                        # Map columns to values
                        data = dict(zip(columns, values_list))
                        data['__rowMarker__'] = 0
            
                        # Creating a DataFrame with a single row
                        df = pd.concat([df, pd.DataFrame([data])], ignore_index=True)
                    
                    set_values = {}
                    if 'UPDATE' == row[0]:
                        scn_number = row[2]
                        match = re.search(r'SET\s+(.*?)\s+WHERE', row[1], re.DOTALL | re.IGNORECASE)
                        set_clause = match.group(1).strip()
            
                        where_match = re.search(r'WHERE\s+(.*?)(?:;|$)',  row[1], re.DOTALL | re.IGNORECASE)
                        where_clause = where_match.group(1).strip() if where_match else ""
            
                        #cleaned_set_clause = clean_timestamps(set_clause)
                        cleaned_set_clause = re.sub(r"TIMESTAMP\s*'([^']*)'", r"'\1'", set_clause)
                        where_clause = re.sub(r"TIMESTAMP\s*'([^']*)'", r"'\1'", where_clause)
            
                        set_values = dict(re.findall(r'"([^"]+)"\s*=\s*(NULL|\'[^\']+\'|\d+)', cleaned_set_clause))#set condition making set values
                        updated_where_clause = replace_where_values(where_clause, set_values)
                        df = pd.concat([df, parse_where_clause_to_df(updated_where_clause,row[0])], ignore_index=True)
            
                    
                    if 'DELETE' == row[0]:
                        scn_number = row[2]
                        where_match = re.search(r'WHERE\s+(.*?)(?:;|$)',  row[1], re.DOTALL | re.IGNORECASE)
                        where_clause = where_match.group(1).strip() if where_match else ""
                        #cleaned_set_clause = clean_timestamps(where_clause)
                        cleaned_set_clause = re.sub(r"TIMESTAMP\s*'([^']*)'", r"'\1'", where_clause)
                        #df = pd.concat([df, pd.DataFrame([parse_where_clause_to_df(cleaned_set_clause)])], ignore_index=True)
                        df = pd.concat([df, parse_where_clause_to_df(cleaned_set_clause,row[0])], ignore_index=True)
            
                df = df.groupby(unqiue_columns_string).last().reset_index()
                df = df.reindex(columns=['__rowMarker__'] + df.columns.drop('__rowMarker__').tolist())


                try:
                    if not df.empty:
                        extra_path = os.environ['EXTRA_FOLDER']
                        file_path = extra_path + f"/Data_Migration_Files/{table_name}"
                        # Constants
                        onelake_account_url = "https://onelake.dfs.fabric.microsoft.com"
                        file_system_name = target_DB_details['host'].split('onelake.dfs.fabric.microsoft.com/')[1]
                        print("==1==")
                        credential = ClientSecretCredential(target_DB_details['service_name'], target_DB_details['name'], target_DB_details['password'])
                        print("==2==")
                        service_client = DataLakeServiceClient(account_url=onelake_account_url, credential=credential)
                        print("==3==")
                        file_system_client = service_client.get_file_system_client(file_system_name)
                        print("==4==")
                        onelake_file_name = table_name.upper()
                        print("==5==")
                        table_file_list = []
                        file_dir_list = os.listdir(file_path)
                        print(file_dir_list,"====file_dir_list")
                        for files in file_dir_list:
                            if '.parquet' in files:
                                base_name = str(files.split('.')[0])
                                try:
                                    table_file_list.append(int(str(base_name)))
                                except Exception as e:
                                    print(e)
                        
                        print("==6==")
                        new_seq = str(max(table_file_list)+1).zfill(20)
                        onelake_file_name= f"{table_name}/{new_seq}.parquet"
                        storage_file_name = f"{file_path}/{new_seq}.parquet"
                        print("==7==")
                        file_client = file_system_client.get_file_client(onelake_file_name)
                        print("==8==")
                        df.to_parquet(storage_file_name, engine='pyarrow')
                        print("==9==")
                        with open(storage_file_name,"rb") as data:
                            file_client.upload_data(data, overwrite=True)
                        print("==10==")
                        if isinstance(commit_scn, int) and commit_scn is not 0:
                                with open(commit_scn_file, 'w') as f_commit:
                                    f_commit.write(str(commit_scn))
                    else:
                        print("DataFrame is empty.")
                except Exception as e:
                        error_tuple = (
                                        config_id, batch_number, None, str(e), datetime.now())
                        batch_error_list.append(error_tuple)
                        print(e)

                load_end_time = datetime.now()

                batch_end_time = datetime.now()
                extraction_time = (fetch_end_time- fetch_start_time).total_seconds() * 10**3
                transform_time = (transformation_end_time - transformation_start_time).total_seconds() * 10**3
                load_time = (load_end_time - load_start_time).total_seconds() * 10**3
                time_taken_by_batch = batch_end_time - batch_start_time
                time_diff_seconds = time_taken_by_batch.total_seconds()
                time_diff_minutes = round((time_diff_seconds / 60), 2)
    
                project_connection = connect_database(project_DB_details)
                project_cursor = project_connection.cursor()
                print(batch_error_list)

                project_connection = connect_database(project_DB_details)
                cdc_data_id = cdc_data_insert(project_connection, batch_number, batch_length, batch_size, cdc_load_type,
                                                      None, batch_start_time, extraction_time, transform_time, load_time,
                                                      batch_end_time, task_id)
                if batch_error_list:
                    batch_error_list = [t + (cdc_data_id,) for t in batch_error_list]
                    cdc_errors_insert(project_connection, batch_error_list)

                batch_number = batch_number+1
            
            else:
                max_scn_query = "SELECT max(START_SCN) FROM V$LOGMNR_CONTENTS"
                source_cursor.execute(max_scn_query)
                max_scn = source_cursor.fetchall()
                print(commit_scn_file,"=========",current_scn_file)
                with open(commit_scn_file, 'r') as file_csn:
                    commit_scn = int(file_csn.readline())
                with open(current_scn_file, 'r') as file_csn:
                    current_scn = int(file_csn.readline())
                if commit_scn not in [None,'',0,'0']:
                   final_scn = commit_scn
                else:
                    final_scn= current_scn

                print(final_scn,"==========final_Scn")
                    

                
                if isinstance(final_scn, int) and final_scn is not None:
                    with open(current_scn_file, 'w') as f_csn:
                        f_csn.write(str(final_scn + 1))
    
                print(final_scn, '===final and commit in else==', commit_scn)
    
                get_sequence_query = (f"SELECT MIN(SEQUENCE#) AS SEQ# FROM V$LOG_HISTORY WHERE FIRST_CHANGE# <= {str(final_scn+1)} AND NEXT_CHANGE# > {str(final_scn+1)}")
                source_cursor.execute(get_sequence_query)
                sequence_number = source_cursor.fetchone()
                sequence_number = sequence_number[0]
    
                if sequence_number is not None:
                    get_log_paths_query = (
                        f"SELECT 'DBMS_LOGMNR.ADD_LOGFILE(''' ||NAME||''');' AS name, SEQUENCE#, THREAD#, deleted FROM V$ARCHIVED_LOG WHERE SEQUENCE# >={sequence_number}")
    
                    source_cursor.execute(get_log_paths_query)
                    log_paths = source_cursor.fetchmany(batch_size)
    
                    log_paths_list = [item[0] for item in log_paths]
                    distinct_log_files = [value for value in log_paths_list if value not in log_file_list]
    
                    print(distinct_log_files)
                    if len(distinct_log_files) > 0:
                        log_path_string = '\n'.join([str(item) for item in distinct_log_files])
                        try:
                            log_path_formatted_query = """begin \n{}\n end;""".format(log_path_string)
                        except Exception as e:
                            print(e)
                        print(log_path_formatted_query)
                        source_cursor.execute(log_path_formatted_query)
                        log_file_list.extend(distinct_log_files)
                    else:
                        get_log_paths_query = f"""SELECT DISTINCT 'DBMS_LOGMNR.ADD_LOGFILE(''' ||MEMBER||''');' AS name,e.* FROM V$LOGFILE e JOIN v$log f ON e.GROUP# =f.GROUP# where e.IS_RECOVERY_DEST_FILE= 'NO'  and ARCHIVED = 'NO'"""
                        source_cursor.execute(get_log_paths_query)
                        else_log_paths = source_cursor.fetchall()
                        else_log_paths_list = [item[0] for item in else_log_paths]
                        else_distinct_log_files = [value for value in else_log_paths_list if value not in log_file_list]
                        if len(else_distinct_log_files) > 0:
                            else_log_path_string = '\n'.join([str(item) for item in else_distinct_log_files])
                            else_log_path_formatted_query = """begin \n{}\n end;""".format(else_log_path_string)
                            try:
                                source_cursor.execute(else_log_path_formatted_query)
                                log_file_list.extend(else_distinct_log_files)
                            except Exception as e:
                                logfile_names_list = re.findall(r"(\w:\\.*?\.LOG)", str(e))
                                except_log_path_string = [
                                    "DBMS_LOGMNR.ADD_LOGFILE('" + logfile.replace("\\", "\\\\") + "');" for
                                    logfile in logfile_names_list]
                                except_log_path_list = [value for value in logfile_names_list if
                                                        value not in log_file_list]
    
                                if len(except_log_path_list) > 0:
                                    except_else_log_path_string = '\n'.join([str(item) for item in except_log_path_list])
                                    except_else_log_path_formatted_query = """begin \n{}\n end;""".format(
                                        except_else_log_path_string)
                                    source_cursor.execute(except_else_log_path_formatted_query)
                                    log_file_list.extend(except_log_path_string)
    
                if open_scn is 0:
                    log_miner_query = f""" begin
                                           DBMS_LOGMNR.START_LOGMNR(
                                              STARTSCN =>{final_scn+1 }, -- or use STARTTIME or STARTDATE as needed
                                               OPTIONS => DBMS_LOGMNR.NO_ROWID_IN_STMT + DBMS_LOGMNR.STRING_LITERALS_IN_STMT + DBMS_LOGMNR.DICT_FROM_ONLINE_CATALOG
                                           ); END;"""
                else:
                    log_miner_query = f"""
                            begin
                           DBMS_LOGMNR.START_LOGMNR(
                              STARTSCN =>{open_scn}, -- or use STARTTIME or STARTDATE as needed
                               OPTIONS => DBMS_LOGMNR.NO_ROWID_IN_STMT + DBMS_LOGMNR.STRING_LITERALS_IN_STMT + DBMS_LOGMNR.DICT_FROM_ONLINE_CATALOG
                           ); END;"""
                print(log_miner_query, '===in else case')
                source_cursor.execute(log_miner_query)


                
                data_fetch_query = f"""SELECT OPERATION,SQL_REDO,SCN
                    FROM V$LOGMNR_CONTENTS E
                    WHERE OPERATION IN ('INSERT', 'UPDATE', 'DELETE')
                    AND SEG_OWNER = '{schema_name.upper()}' 
                    AND TABLE_NAME = '{table_name.upper()}'
                    AND SCN > {final_scn}
                    FETCH FIRST {batch_size} ROWS ONLY"""
                print(data_fetch_query, '===in else case')
                source_cursor.execute(data_fetch_query)
    


    else:
        print("in else========")
        batch_number = 1
        while True:
            fetch_start_time = datetime.now()
            batch_data = source_cursor.fetchall()
            fetch_end_time = datetime.now()
            batch_start_time = datetime.now()
            if batch_data:
                batch_length = len(batch_data)
                batch_memory = sum(sys.getsizeof(batch_data) for statement in batch_data)
                    
                batch_error_list=[]
                data_records =''

                transformation_start_time = datetime.now()
                for row in batch_data:
                    if "where ROWID =" in row[1]:  
                        if isinstance(commit_scn, int) and commit_scn is not 0:
                            with open(commit_scn_file, 'w') as f_commit:
                                f_commit.write(str(row[0]+1))
                        print("Skipping invalid SQL:", row)
                        continue  # Skip this iteration if the statement is malformed
                    else:
                        commit_scn = row[0]
                        data_records += row[1].replace("TIMESTAMP ' ","'")
                    # print(data_records)
                transformation_start_time = datetime.now()


                load_start_time = datetime.now()
                try:
                    print(target_DB_details)
                    credential = ClientSecretCredential(target_DB_details['service_name'], target_DB_details['name'], target_DB_details['password'])
                    connection_string = f"Driver={{ODBC Driver 18 for SQL Server}};Server={target_DB_details['host']},1433;Database={target_DB_details['db_name']};Encrypt=Yes;TrustServerCertificate=No"
                    params = urllib.parse.quote(connection_string)
                    token_object = credential.get_token("https://database.windows.net//.default")
                    token_as_bytes = bytes(token_object.token, "UTF-8")
                    encoded_bytes = bytes(chain.from_iterable(zip(token_as_bytes, repeat(0)))) 
                    token_bytes = struct.pack("<i", len(encoded_bytes)) + encoded_bytes 
                    attrs_before = {1256: token_bytes}
                    target_connection = pyodbc.connect(connection_string, attrs_before=attrs_before)
                    target_cursor = target_connection.cursor()
                    print(data_records)
                    if 'datawarehouse.fabric.microsoft.com' in target_DB_details['host']:
                        data_records_list = data_records.split(";")
                        for record in data_records_list:
                            target_cursor.execute(record+';')
                    else:
                        target_cursor.execute(data_records)
                    target_connection.commit()
                    target_cursor.close()
                    target_connection.close()
                    if isinstance(commit_scn, int) and commit_scn is not 0:
                        with open(commit_scn_file, 'w') as f_commit:
                            f_commit.write(str(commit_scn))
                    load_end_time = datetime.now()
                except Exception as e:
                    print("in exception")
                    error_tuple = (
                                    config_id, batch_number, None, str(e), datetime.now())
                    batch_error_list.append(error_tuple)
                    print(e)


                batch_end_time = datetime.now()
                time_taken_by_batch = batch_end_time - batch_start_time
                extraction_time= (fetch_end_time - fetch_start_time).total_seconds() * 10**3
                transform_time = (transformation_end_time - transformation_start_time).total_seconds() * 10**3
                load_time = (load_end_time - load_start_time).total_seconds() * 10**3
                time_diff_seconds = time_taken_by_batch.total_seconds()
                time_diff_minutes = round((time_diff_seconds / 60), 2)
    
                project_connection = connect_database(project_DB_details)
                project_cursor = project_connection.cursor()
                print(batch_error_list)
    
                # error_insert_statement = "insert into public.prj_cdc_error_data (config_id,batch_number,transaction,error,error_time) values (%s,%s,%s,%s,%s)"
                # project_cursor.executemany(error_insert_statement, batch_error_list
                #                            )
    
                # if table_name in ['', None]:
                #     meta_data_query = "insert into public.prj_cdc_metadata (config_id,operation_name,source_connection_id,target_connection_id,source_schema,table_name,target_schema,batch_number,batch_start_time,batch_end_time,batch_statments_length,batch_statements_size,batch_duration,created_by,extraction_time, transform_time, load_time) values ({0},'{1}',{2}, {3},'{4}','{5}','{6}', {7}, '{8}', '{9}', {10}, {11}, {12}, '{13}','{14}','{15}','{16}')".format(
                #         config_id, 'CDC', source_connection_id, target_connection_id, None, None,
                #         None, batch_number,
                #         batch_start_time, batch_end_time, batch_length, batch_memory, time_diff_minutes, 'Python',extraction_time, transform_time, load_time)
                # else:
                #     meta_data_query = "insert into public.prj_cdc_metadata (config_id,operation_name,source_connection_id,target_connection_id,source_schema,table_name,target_schema,batch_number,batch_start_time,batch_end_time,batch_statments_length,batch_statements_size,batch_duration,created_by,extraction_time, transform_time, load_time) values ({0},'{1}',{2}, {3},'{4}','{5}','{6}', {7}, '{8}', '{9}', {10}, {11}, {12}, '{13}','{14}','{15}','{16}')".format(
                #         config_id, 'CDC', source_connection_id, target_connection_id, schema_name, table_name,
                #         target_schema, batch_number,
                #         batch_start_time, batch_end_time, batch_length, batch_memory, time_diff_minutes, 'Python',extraction_time, transform_time, load_time)
                # project_cursor.execute(meta_data_query)
                # project_cursor.close()
                # project_connection.commit()

                project_connection = connect_database(project_DB_details)
                cdc_data_id = cdc_data_insert(project_connection, batch_number, batch_length, batch_size, cdc_load_type,
                                                      None, batch_start_time, extract_time, transform_time, load_time,
                                                      batch_end_time, task_id)
                if batch_error_list:
                    batch_error_list = [t + (cdc_data_id,) for t in batch_error_list]
                    cdc_errors_insert(project_connection, batch_error_list)
                batch_number = batch_number+1
    
            else:
                max_scn_query = "SELECT max(START_SCN) FROM V$LOGMNR_CONTENTS"
                source_cursor.execute(max_scn_query)
                max_scn = source_cursor.fetchall()
    
                with open(commit_scn_file, 'r') as file_csn:
                    commit_scn = int(file_csn.readline())
                if commit_scn not in [None,'',0]:
                   final_scn = commit_scn
                   
                else:
                    final_scn= max_scn[0][0]
    
                if isinstance(final_scn, int) and final_scn is not None:
                    with open(current_scn_file, 'w') as f_csn:
                        f_csn.write(str(final_scn + 1))
    
                print(final_scn, '===final and commit in else==', commit_scn)
    
                get_sequence_query = (f"SELECT MIN(SEQUENCE#) AS SEQ# FROM V$LOG_HISTORY "
                                      f"WHERE FIRST_CHANGE# <= {final_scn + 1} AND "
                                      f"NEXT_CHANGE# > {final_scn + 1}")
                source_cursor.execute(get_sequence_query)
                sequence_number = source_cursor.fetchone()
                sequence_number = sequence_number[0]
    
                if sequence_number is not None:
                    get_log_paths_query = (
                        f"SELECT 'DBMS_LOGMNR.ADD_LOGFILE(''' ||NAME||''');' AS name, SEQUENCE#, THREAD#, deleted FROM V$ARCHIVED_LOG WHERE SEQUENCE# >={sequence_number}")
    
                    source_cursor.execute(get_log_paths_query)
                    log_paths = source_cursor.fetchmany(batch_size)
    
                    log_paths_list = [item[0] for item in log_paths]
                    distinct_log_files = [value for value in log_paths_list if value not in log_file_list]
    
                    print(distinct_log_files)
                    if len(distinct_log_files) > 0:
                        log_path_string = '\n'.join([str(item) for item in distinct_log_files])
                        log_path_formatted_query = """begin \n{}\n end;""".format(log_path_string)
                        print(log_path_formatted_query)
                        source_cursor.execute(log_path_formatted_query)
                        log_file_list.extend(distinct_log_files)
                    else:
                        get_log_paths_query = f"""SELECT DISTINCT 'DBMS_LOGMNR.ADD_LOGFILE(''' ||MEMBER||''');' AS name,e.* FROM V$LOGFILE e JOIN v$log f ON e.GROUP# =f.GROUP# where e.IS_RECOVERY_DEST_FILE= 'NO'  and ARCHIVED = 'NO'"""
                        source_cursor.execute(get_log_paths_query)
                        else_log_paths = source_cursor.fetchall()
                        else_log_paths_list = [item[0] for item in else_log_paths]
                        else_distinct_log_files = [value for value in else_log_paths_list if value not in log_file_list]
                        if len(else_distinct_log_files) > 0:
                            else_log_path_string = '\n'.join([str(item) for item in else_distinct_log_files])
                            else_log_path_formatted_query = """begin \n{}\n end;""".format(else_log_path_string)
                            try:
                                source_cursor.execute(else_log_path_formatted_query)
                                log_file_list.extend(else_distinct_log_files)
                            except Exception as e:
                                logfile_names_list = re.findall(r"(\w:\\.*?\.LOG)", str(e))
                                except_log_path_string = [
                                    "DBMS_LOGMNR.ADD_LOGFILE('" + logfile.replace("\\", "\\\\") + "');" for
                                    logfile in logfile_names_list]
                                except_log_path_list = [value for value in logfile_names_list if
                                                        value not in log_file_list]
    
                                if len(except_log_path_list) > 0:
                                    except_else_log_path_string = '\n'.join([str(item) for item in except_log_path_list])
                                    except_else_log_path_formatted_query = """begin \n{}\n end;""".format(
                                        except_else_log_path_string)
                                    source_cursor.execute(except_else_log_path_formatted_query)
                                    log_file_list.extend(except_log_path_string)
    
                if open_scn is 0:
                    log_miner_query = f""" begin
                                           DBMS_LOGMNR.START_LOGMNR(
                                              STARTSCN =>{final_scn + 1}, -- or use STARTTIME or STARTDATE as needed
                                               OPTIONS => DBMS_LOGMNR.NO_ROWID_IN_STMT + DBMS_LOGMNR.STRING_LITERALS_IN_STMT + DBMS_LOGMNR.DICT_FROM_ONLINE_CATALOG
                                           ); END;"""
                else:
                    log_miner_query = f"""
                            begin
                           DBMS_LOGMNR.START_LOGMNR(
                              STARTSCN =>{open_scn}, -- or use STARTTIME or STARTDATE as needed
                               OPTIONS => DBMS_LOGMNR.NO_ROWID_IN_STMT + DBMS_LOGMNR.STRING_LITERALS_IN_STMT + DBMS_LOGMNR.DICT_FROM_ONLINE_CATALOG
                           ); END;"""
                print(log_miner_query, '===in else case')
                source_cursor.execute(log_miner_query)
    
                data_fetch_query = f"""SELECT SCN,SQL_REDO FROM V$LOGMNR_CONTENTS E 
                              WHERE OPERATION IN ('INSERT','UPDATE','DELETE') 
                              AND SEG_OWNER = '{schema_name.upper()}'
                              AND TABLE_NAME = '{table_name.upper()}'
                              AND SCN > {commit_scn}
                              FETCH FIRST {batch_size} ROWS ONLY"""
                print(data_fetch_query, '===in else case')
                source_cursor.execute(data_fetch_query)

