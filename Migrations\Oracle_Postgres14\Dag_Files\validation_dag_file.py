import os, sys, json, requests
import pandas as pd
from datetime import datetime, timedelta
from import_file import import_file
from airflow import DAG
from kubernetes.client import models as k8s
from airflow.operators.python import PythonOperator
from airflow.models.baseoperator import chain

connection_url = os.getenv("API_HOST", 'http://qmig-eng.qmig-ns.svc.cluster.local:8080')
connection_url = connection_url + '/api/v1/'


def api_authentication():
    url = connection_url + 'Common/Security/VerifyPython'
    project_id = os.environ['PROJECT_ID']
    params = {
        "projectId": str(project_id),
        "content": "8/2DlAH8q1R+untey0ZuqMuD89WX4m2XoSCWhxQ+UOk="}
    response = requests.get(url, params=params)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = json.loads(status_response['text'])
        token_data = data['token']
        return token_data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def api_decrypt(data, token_data):
    url = connection_url + 'Common/Cryptography/DecryptData'
    payload = {
        "NeedToDecrypt": data,
        "user": "Python"
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.post(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    encrypted_source = status_response1['text']
    return encrypted_source


def decrypt_file(token_data, file_path, decryption_path, dag_name, task_name):
    with open(file_path, 'r') as encrypted_file:
        encrypted = encrypted_file.read()
    decrypted = api_decrypt(encrypted, token_data)

    if not os.path.exists(decryption_path):
        os.makedirs(decryption_path)

    file_name = file_path.split('/')[-1]
    with open(decryption_path + '/' + dag_name + '_' + task_name + '_' + file_name, 'w') as decrypted_file:
        decrypted_file.write(decrypted)


def delete_files_in_directory(file_path):
    if os.path.isfile(file_path):
        os.remove(file_path)


def validation_task_trigger(ti, **kwargs):
    token_data = kwargs['token_data']
    dag_name = kwargs['dag_id']
    task_name = kwargs['task_name']
    tag_name = kwargs['tag_name']

    decryption_path = os.environ['EXTRA_FOLDER'] + '/tmp'
    sys.path.append(decryption_path)

    current_directory = os.getcwd() + '/' + 'dags'

    decrypt_file(token_data, current_directory + '/' + 'validation_triggers.py', decryption_path, dag_name, task_name)
    import_object = import_file(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'validation_triggers.py')
    delete_files_in_directory(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'validation_triggers.py')

    function_name = [i for i in dir(import_object) if i.lower() == str(tag_name.lower() + '_trigger').lower()][0]
    validation_function_call = getattr(import_object, function_name.strip())
    validation_function_call(ti, **kwargs)


def summary_task_trigger(ti, **kwargs):
    token_data = kwargs['token_data']
    dag_name = kwargs['dag_id']
    task_name = kwargs['task_name']

    decryption_path = os.environ['EXTRA_FOLDER'] + '/tmp'
    sys.path.append(decryption_path)

    current_directory = os.getcwd() + '/' + 'dags'

    decrypt_file(token_data, current_directory + '/' + 'validation_triggers.py', decryption_path, dag_name, task_name)
    import_object = import_file(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'validation_triggers.py')
    delete_files_in_directory(decryption_path + '/' + dag_name + '_' + task_name + '_' + 'validation_triggers.py')

    function_name = [i for i in dir(import_object) if i.lower() == str('validation_summary_trigger').lower()][0]
    validation_function_call = getattr(import_object, function_name.strip())
    validation_function_call(ti, **kwargs)


def create_dag(dag_id, tag_name, schedule_interval, pause_flag, default_args, max_active_runs, process_type, project_id,
               source_connection_id, target_connection_id, schema_name, target_schema, dag_tables_list, file_name,
               token_data, priority_dict, chunk_configuration_dict):
    dag = DAG(dag_id, schedule_interval=schedule_interval, default_args=default_args,
              render_template_as_native_obj=True, max_active_runs=max_active_runs,
              tags=[tag_name], is_paused_upon_creation=pause_flag,
              concurrency=int(chunk_configuration_dict['Concurrency']))

    executor_config_memory = {
        "pod_override": k8s.V1Pod(
            spec=k8s.V1PodSpec(
                containers=[
                    k8s.V1Container(
                        name='base',
                        resources=k8s.V1ResourceRequirements(
                            requests={'cpu': '300m', 'memory': '300Mi'},
                            limits={'cpu': '600m', 'memory': '600Mi'})
                    )
                ]
            )
        ),
    }

    validation_tasks = []
    for index, table_list in enumerate(dag_tables_list):
        if target_schema != '':
            target_schema = target_schema
            file_target_schema = target_schema
        else:
            target_schema = table_list[0]
            file_target_schema = ''
        validation_task = PythonOperator(
            task_id=f'{table_list[0]}_{table_list[1]}_{process_type.lower()}',
            op_kwargs={'dag_id': dag_id, 'project_id': project_id, 'process_type': process_type,
                       'source_connection_id': source_connection_id,
                       'target_connection_id': target_connection_id, 'schema': table_list[0],
                       'file_schema': schema_name, 'target_schema': target_schema,
                       'file_target_schema': file_target_schema, 'table_name': table_list[1], 'token_data': token_data,
                       'task_name': f'{table_list[0]}_{table_list[1]}_{process_type.lower()}', 'file_name': file_name,
                       'concurrency': int(chunk_configuration_dict['Concurrency'])},
            executor_config=executor_config_memory,
            python_callable=validation_task_trigger,
            weight_rule='absolute',
            trigger_rule='all_success',
            dag=dag)
        validation_tasks.append(validation_task)

    summary_task = PythonOperator(
        task_id='Summary_' + str(dag_tables_list[0][1]),
        op_kwargs={'dag_id': dag_id, 'task_name': 'Summary_' + str(dag_tables_list[0][1]),
                   'source_connection_id': source_connection_id, 'target_connection_id': target_connection_id,
                   'token_data': token_data, 'file_name': file_name, 'process_type': process_type},
        python_callable=summary_task_trigger,
        priority_weight=priority_dict[dag_id],
        weight_rule='absolute',
        dag=dag)

    chain(validation_tasks, summary_task)
    return dag


extra_path = os.environ['EXTRA_FOLDER']
excel_file = '{0}/@Config_File_Path'.format(extra_path)

config_read = pd.read_excel(excel_file, sheet_name='Configuration')
config_read.fillna('', inplace=True)
config_list = config_read.to_dict(orient='records')

config_data = {}
for i in config_list: config_data.update({i['Parameter']: i['Value']})

priority_read = pd.read_excel(excel_file, sheet_name='Priority_Weights')
priority_list = priority_read.to_dict(orient='records')
priority_dict = {}
for i in priority_list: priority_dict.update({i['Dag Name']: i['Priority_Weight']})

chunk_configuration_dict = {}
if os.path.isfile(f'{extra_path}/@Config_File_Folder/chunk_configuration.json'):
    with open(f'{extra_path}/@Config_File_Folder/chunk_configuration.json', 'r') as f:
        chunk_configuration_dict = json.loads(f.read())

token_data = api_authentication()

schedule_interval = None
pause_flag = True

dags_df = pd.read_excel(excel_file, sheet_name='Dags')
dags_df = dags_df.groupby('Dag Name')

for dag_name, dag_tables_list in dags_df:
    default_args = {'owner': 'airflow',
                    'start_date': datetime(2023, 9, 11, 7, 00, 00),
                    'retries': 3,
                    'retry_delay': timedelta(minutes=4)
                    }
    max_active_runs = 1

    tag_name = ''
    if str(dag_name).startswith('Pre_'):
        tag_name = f"{config_data['File_Name']} Pre Validation"
    elif str(dag_name).startswith('Post_'):
        tag_name = f"{config_data['File_Name']} Post Validation"
    elif str(dag_name).startswith('Complete_'):
        tag_name = f"{config_data['File_Name']} Complete Validation"

    globals()[dag_name] = create_dag(dag_name, tag_name, schedule_interval, pause_flag, default_args, max_active_runs,
                                     config_data['Process_Type'], config_data['Project_Id'],
                                     config_data['Source_Connection_Id'], config_data['Target_Connection_Id'],
                                     config_data['Schema'], config_data['Target_Schema'],
                                     dag_tables_list.values.tolist(), config_data['File_Name'],
                                     token_data, priority_dict, chunk_configuration_dict)
