import os, sys, re, shutil
from import_file import import_file
import pandas as pd
from common_modules.api import api_authentication, decrypt_database_details
from common_modules.stored_procedures import insert_config_name


def copy_load_dag_files(file_name, config_files_path, local_migration_path, dag_path):
    source_file = local_migration_path + '/' + 'Dag_Files' + '/' + 'load_files_dag_file.py'
    target_file = dag_path + '/' + f'load_files_dag_file_{file_name}.py'
    with open(source_file, 'r') as file:
        file_content = file.read()
        file_replaced_content = re.sub('@Config_File_Path',
                                       f'{config_files_path}/Load_Files/{file_name}.xlsx'.replace('\\', '/'),
                                       file_content)
        file_replaced_content = file_replaced_content.replace('@schedule_interval', 'None').replace("'@pause_flag'",
                                                                                                     'True')
    with open(target_file, 'w') as f:
        f.write(file_replaced_content)

    dag_triggers_file = local_migration_path + '/' + 'Dag_Files' + '/' + 'dag_triggers.py'
    trigger_destination_file = dag_path + '/' + 'dag_triggers.py'
    with open(dag_triggers_file, 'rb') as f:
        file_content = f.read()
    with open(trigger_destination_file, 'wb') as f:
        f.write(file_content)


def load_files_trigger(task_name, project_id, migration_name, source_connection_id, target_connection_id, file_name, cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')
        dag_path = getattr(import_object, 'Dag_Path')

        working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')
        local_migration_path = local_root_path + '/' + 'Migrations' + '/' + migration_name

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)

            token_data = api_authentication()

            target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(target_connection_id))

            project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
            project_function_call = getattr(import_object, 'connect_database')

            config_files_path = root_path + '/' + str(source_connection_id) + '/' + str(
                target_connection_id) + '/' + 'Config_Files'

            if not os.path.exists(config_files_path + '/' + 'Load_Files'):
                os.makedirs(config_files_path + '/' + 'Load_Files')

            if not os.path.exists(working_directory_path):
                os.makedirs(working_directory_path)

            load_file_name = 'load_files_' + file_name.replace('.xlsx', '')
            load_config_file_path = config_files_path + '/' + 'Load_Files' + '/' + load_file_name + '.xlsx'
            temp_load_config_path = working_directory_path + '/' + load_file_name + '.xlsx'

            with pd.ExcelWriter(temp_load_config_path, engine="xlsxwriter") as writer:
                load_files_config_list = [
                    (task_name, project_id, migration_name, source_connection_id, target_connection_id,
                     '', '', '', load_file_name)]
                load_files_config_df = pd.DataFrame(load_files_config_list,
                                                    columns=['Process_Type', 'Project_Id',
                                                             'Migration_Name',
                                                             'Source_Connection_Id',
                                                             'Target_Connection_Id', 'Schema', 'Table_Name',
                                                             'Target_Schema', 'File_Name'])
                load_files_config_df = load_files_config_df.transpose()
                key_value_pairs = load_files_config_df.to_dict()[0]
                load_files_config_df = pd.DataFrame(list(key_value_pairs.items()),
                                                    columns=['Parameter', 'Value'])
                load_files_config_df.fillna('', inplace=True)
                load_files_config_df.to_excel(writer, sheet_name="Configuration", index=False)

                load_dag_tasks_list = [(load_file_name, str(target_DB_details['db_name']).capitalize(),
                                        'Load_Files_' + file_name.replace('.xlsx', ''))]
                load_tasks_df = pd.DataFrame(load_dag_tasks_list,
                                             columns=['File_Name', 'Target_DB_Name',
                                                      'Dag Name'])
                load_tasks_df.fillna('', inplace=True)
                load_tasks_df.to_excel(writer, sheet_name='Dags', index=False)

            shutil.copyfile(temp_load_config_path, load_config_file_path)
            project_connection = project_function_call(project_DB_details)
            insert_config_name(project_connection, load_file_name, task_name, source_connection_id,
                               target_connection_id, None, None, 'Created')
            copy_load_dag_files(load_file_name, config_files_path.replace(root_path + '/', ''), local_migration_path, dag_path)
        else:
            print(f'Module path {db_module_path} not found')
    else:
        print(f'Config path {config_path} not found')