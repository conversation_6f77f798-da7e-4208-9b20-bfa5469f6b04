import os, re, requests, json, psycopg2, ibm_db_dbi, logging, csv, shutil, struct, pyodbc, sys
from datetime import datetime
import pandas as pd
from azure.identity import ClientSecretCredential
from azure.storage.blob import BlobServiceClient, ContentSettings
from deltalake import write_deltalake, DeltaTable, <PERSON>hem<PERSON>, Field
from itertools import chain, repeat
from pyspark.sql import SparkSession

connection_url = os.getenv("API_HOST", 'http://qmig-eng.qmig-ns.svc.cluster.local:8080')
connection_url = connection_url + '/api/v1/'


def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


def DB_connection(database_data):
    host_name = database_data['host']
    port = database_data['port']
    service_name = database_data['db_name']
    user_name = database_data['name']
    password = database_data['password']
    dsn = (
        f"DATABASE={service_name};"
        f"HOSTNAME={host_name};"
        f"PORT={port};"
        f"PROTOCOL=TCPIP;"
        f"UID={user_name};"
        f"PWD={password};"
    )
    try:
        connection = ibm_db_dbi.connect(dsn)
        error = ''
    except ibm_db_dbi.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near database connection", e)
    return connection, error


def target_DB_connection(db_data):
    try:
        connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                      host=db_data['host'], database=db_data['db_name'],
                                      port=db_data['port'])
        error = ''
    except psycopg2.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near target database connection", e)
    return connection, error


def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        data = cursor.fetchall()
    except ibm_db_dbi.DatabaseError as e:
        print("Issue found near source database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data


def api_authentication():
    url = connection_url + 'Common/Security/VerifyPython'
    project_id = os.environ['PROJECT_ID']
    params = {
        "projectId": str(project_id),
        "content": "8/2DlAH8q1R+untey0ZuqMuD89WX4m2XoSCWhxQ+UOk="}
    response = requests.get(url, params=params)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = json.loads(status_response['text'])
        token_data = data['token']
        return token_data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def get_project_DB_info(token_data, project_id):
    url = connection_url + 'Common/Master/GetProjectMigDetailSelect'
    payload = {
        "projectId": str(project_id),
        "migsrcType": "D"
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.post(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = data_text['jsonResponseData']
        data_text = [dict for dict in data_text['Table1'] if dict['dbconname'] == 'PRJDB' + str(project_id)][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def get_source_DB_info(token_data, project_id, source_connection_id):
    url = connection_url + 'Common/Master/GetDBConnections'
    payload = {
        "projectId": str(project_id),
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.get(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = [dict for dict in data_text['Table1'] if
                     dict['migsrctgt'] == 'S' and dict['Connection_ID'] == source_connection_id][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def get_target_DB_info(token_data, project_id, target_connection_id):
    url = connection_url + 'Common/Master/GetDBConnections'
    payload = {
        "projectId": str(project_id),
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response1 = requests.get(url, data=json.dumps(payload), headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = [dict for dict in data_text['Table1'] if
                     dict['migsrctgt'] == 'T' and dict['Connection_ID'] == target_connection_id][0]
        return data_text
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def decrypt_string(encrypted_str, token_data):
    application_url = connection_url + 'Common/Cryptography/DecryptData'
    payload = {
        "NeedToDecrypt": encrypted_str
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response = requests.post(application_url, data=json.dumps(payload), headers=headers)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = response.text
        return data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def decrypt_database_details(token_data, project_id, category, connection_id):
    prj_data = {}
    prj_db_data = {}
    if category == 'Project':
        prj_db_data = get_project_DB_info(token_data, project_id)
    elif category == 'Source':
        prj_db_data = get_source_DB_info(token_data, project_id, connection_id)
    elif category == 'Target':
        prj_db_data = get_target_DB_info(token_data, project_id, connection_id)
    password = decrypt_string(prj_db_data['dbpassword'], token_data)
    userid = decrypt_string(prj_db_data['dbuserid'], token_data)
    userid = re.split('\s+', re.sub(r"[\x00-\x1F\x7F]", ' ', userid))
    userid = ' '.join(userid).strip()
    password = re.split('\s+', re.sub(r"[\x00-\x1F\x7F]", ' ', password))
    password = ' '.join(password).strip()
    port = prj_db_data['dbport']
    host = prj_db_data['dbhost']
    if category == 'Project':
        service_name = ''
        parallelprocess = ''
    else:
        service_name = prj_db_data['service_name']
        parallelprocess = prj_db_data['parallelprocess']
    dbname = prj_db_data['dbname']
    prj_data['password'] = password
    prj_data['name'] = userid
    prj_data['port'] = port
    prj_data['host'] = host
    prj_data['db_name'] = dbname
    prj_data['service_name'] = service_name
    prj_data['parallelprocess'] = parallelprocess
    return prj_data


def get_config_id(connection, file_name, process_type):
    cursor = connection.cursor()
    try:
        cursor.execute(
            f"select config_id, transaction_number, transaction_file, config_status from audit_config where config_name = '{file_name}' and process_type = '{process_type}'")
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def transaction_insert(connection, config_id, transaction_number, transaction_file, config_status):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_transaction_insert(%s,%s,%s,%s,%s);fetch all in "dataset";',
                       (config_id, transaction_number, transaction_file,config_status, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data

def dag_insert(connection, dag_name, dag_type, schema_name, target_schema, table_name,
               table_size, concurrency, chunk_size, chunk_parts, dag_status, config_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_dags_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (dag_name, dag_type, schema_name, target_schema, table_name, table_size, concurrency, chunk_size,
             chunk_parts, dag_status, config_id, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data

def dag_update(connection, dag_id, dag_status, dag_end_time):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.sp_audit_dags_update(%s,%s,%s,%s);fetch all in "dataset";',
                       (dag_id, dag_status,dag_end_time, 'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def task_insert(connection, task_name, task_type, attempt, lower_bound, upper_bound, request_memory, limit_memory, task_start_time,
                task_status, dag_id):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_task_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (task_name, task_type, attempt, str(lower_bound), str(upper_bound), request_memory, limit_memory, str(task_start_time),
             task_status, dag_id,'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data


def task_update(connection, task_id, task_row_count, task_status, task_error, task_end_time, extraction_time, transform_time, load_time):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.sp_audit_task_update(%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
            (task_id, task_row_count, task_status, task_error, task_end_time, extraction_time, transform_time, load_time,'dataset'))
        data = cursor.fetchall()
    except Exception as err:
        data = None
        pass
    finally:
        connection.commit()
        cursor.close()
    return data

def pre_validation_trigger(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    target_schema = kwargs['target_schema']
    table_name = kwargs['table_name']
    file_name = kwargs['file_name']
    token_data = kwargs['token_data']
    dag_name = kwargs['dag_id']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
    source_connection, error = DB_connection(source_DB_details)

    process_type = 'Initial_Data_Load' if file_name.startswith('init_') else 'E2E_Data_Load'
    config_data = get_config_id(project_connection, file_name, process_type)
    config_id = config_data[0][0]

    if config_data[0][1] in ['', None]:

        transaction_insert(project_connection, config_id, None, None, 'Running')


    dag_id = dag_insert(project_connection, dag_name, 'Data_Migration', schema_name, target_schema, table_name, kwargs['table_size'],
                        kwargs['concurrency'], kwargs['chunk_size'], kwargs['chunk_parts'],
                        'Running', config_id)
    dag_id = dag_id[0][0]
    ti.xcom_push(key='dag_config', value={'dag_id': dag_id, 'config_id': config_id})

    task_start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_id = task_insert(project_connection, 'pre_validation_task', 'Pre_Validation', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]

    task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_update(project_connection, task_id, None, 'Success', str(error), task_end_time, None, None, None)
    print(f"Pre validation completed for {table_name}")
    return False


def table_migration(ti, **kwargs):
    project_id = kwargs['project_id']
    source_connection_id = kwargs['source_connection_id']
    target_connection_id = kwargs['target_connection_id']
    schema_name = kwargs['schema']
    table_name = kwargs['table_name']
    part_name = kwargs['task_name']
    token_data = kwargs['token_data']
    lower_bound = kwargs['lower_bound']
    upper_bound = kwargs['upper_bound']
    request_memory = kwargs['request_memory']
    limit_memory = kwargs['limit_memory']

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']
    attempt = ti.try_number

    task_error = ''
    extraction_time = None
    transform_time = None
    load_time = None

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)
    task_start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_id = task_insert(project_connection, str(table_name).lower() + '_' + part_name.lower(), 'Data_Migration',
                          attempt,
                          lower_bound, upper_bound, request_memory, limit_memory, task_start_time, 'Running', dag_id)

    try:
        extra_path = os.environ['EXTRA_FOLDER']
        if target_connection_id in ['', None]:
            root_folder = extra_path + '/' + str(source_connection_id)
        else:
            root_folder = extra_path + '/' + str(source_connection_id) + '/' + str(target_connection_id)

        log_path = root_folder + '/' + 'Data_Migration_Logs'
        if not os.path.exists(log_path):
            os.makedirs(log_path)
        task_logger = logging.getLogger(__name__)
        task_logger.setLevel(logging.DEBUG)
        log_handler = logging.FileHandler(log_path + '/' + str(table_name).lower() + '_' + part_name.lower() + '.log')
        log_handler.setFormatter(logging.Formatter("%(asctime)s [%(levelname)s] - %(message)s"))
        log_handler.addFilter(lambda record: record.levelno != logging.WARNING)
        task_logger.addHandler(log_handler)

        task_logger.debug(table_name)
        task_logger.debug('Table execution started')

        source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
        source_connection, error = DB_connection(source_DB_details)

        datatypes_fetch_query = "SELECT COLNAME,TYPENAME FROM SYSCAT.COLUMNS WHERE TABSCHEMA = '{0}' AND TABNAME = '{1}' ORDER BY COLNO".format(
            schema_name.upper(),table_name.upper())
        datatypes_data = execute_query(source_connection, datatypes_fetch_query)

        target_datatypes_data = []
        for row in datatypes_data:
            if row[1].upper() in ['DECIMAL', 'NUMERIC']:
                target_datatypes_data.append((row[0], 'double'))
            elif row[1].upper() in ['CHAR', 'VARCHAR']:
                target_datatypes_data.append((row[0], 'string'))
            elif row[1].upper() == 'INTEGER':
                target_datatypes_data.append((row[0], 'integer'))
            elif row[1].upper() == 'DATE':
                target_datatypes_data.append((row[0], 'timestamp'))
            else:
                target_datatypes_data.append((row[0], 'string'))

        column_names_list = [row[0].upper() for row in datatypes_data]

        source_data_query = """SELECT /*+ OPTIMIZATION 8 */ * FROM {0}.{1} ORDER BY HEX(RID_BIT({0}.{1})) OFFSET '{2}' ROWS FETCH NEXT '{3}' ROWS ONLY""".format(
            schema_name.upper(), table_name.upper(), str(lower_bound), str(upper_bound))
        task_logger.debug(source_data_query)

        extraction_start_time = datetime.now()
        source_cursor = source_connection.cursor()
        source_cursor.arraysize = 100000
        source_cursor.prefetchrows = 100001
        source_cursor.execute(source_data_query)
        table_data_list = source_cursor.fetchall()
        source_cursor.close()
        extraction_end_time = datetime.now()
        extraction_time = (extraction_end_time - extraction_start_time).total_seconds() / 60

        print(f"Extracted records: {len(table_data_list)}")

        if table_data_list:
            transform_start_time = datetime.now()
            df = pd.DataFrame(table_data_list, columns=column_names_list)
            for column_name, data_type in target_datatypes_data:
                if column_name in df.columns:
                    if data_type == 'timestamp':
                        df[column_name] = pd.to_datetime(df[column_name], errors='coerce')
                    elif data_type == 'integer':
                        df[column_name] = pd.to_numeric(df[column_name], errors='coerce', downcast='integer')
                    elif data_type == 'double':
                        df[column_name] = pd.to_numeric(df[column_name], errors='coerce', downcast='float')
                    elif data_type == 'string':
                        df[column_name] = df[column_name].astype(str)
            transform_end_time = datetime.now()
            transform_time = (transform_end_time - transform_start_time).total_seconds() / 60

            target_DB_details = decrypt_database_details(token_data, project_id, 'Target',
                                                         str(target_connection_id))
            credential = ClientSecretCredential(target_DB_details['service_name'], target_DB_details['name'],
                                                target_DB_details['password'])
            if 'abfss://' in target_DB_details['host']:
                delta_token = credential.get_token("https://storage.azure.com/.default").token
                storage_options = {"bearer_token": delta_token, "use_fabric_endpoint": "true"}
                delta_table_path = target_DB_details['host']

                fields = [Field(name, data_type) for name, data_type in target_datatypes_data]
                schema_object = Schema(fields)

                load_start_time = datetime.now()
                DeltaTable.create(table_uri=f"{delta_table_path}/{schema_name}/{table_name}",
                                  schema=schema_object.to_pyarrow(),
                                  storage_options=storage_options,
                                  mode='ignore')

                write_deltalake(f"{delta_table_path}/{schema_name}/{table_name}", df, schema=schema_object.to_pyarrow(),
                                mode='append',
                                storage_options=storage_options)
                load_end_time = datetime.now()

            else:
                if target_connection_id in ['', None]:
                    root_folder = str(source_connection_id)
                else:
                    root_folder = str(source_connection_id) + '/' + str(target_connection_id)
                data_files_path = f"{root_folder}/Data_Files/{schema_name.lower()}/{table_name.lower()}"
                file_name = data_files_path + '/' + table_name.lower() + '_' + part_name.lower() + '.csv'

                csv_data = df.to_csv(index=False, encoding="utf-8")

                connection_string = "DefaultEndpointsProtocol=https;AccountName=qmigstg1137;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
                container_name = 'qmigrator'
                blob_service_client = BlobServiceClient.from_connection_string(connection_string)
                container_client = blob_service_client.get_container_client(container_name)
                if not container_client.exists():
                    container_client.create_container()
                blob_client = container_client.get_blob_client(file_name)
                blob_client.upload_blob(csv_data, overwrite=True,
                                        content_settings=ContentSettings(content_type="text/csv"))

                connection_string = f"Driver={{ODBC Driver 18 for SQL Server}};Server={target_DB_details['host']},1433;Database={target_DB_details['db_name']};Encrypt=Yes;TrustServerCertificate=No"
                token_object = credential.get_token("https://database.windows.net//.default")
                token_as_bytes = bytes(token_object.token, "UTF-8")
                encoded_bytes = bytes(chain.from_iterable(zip(token_as_bytes, repeat(0))))
                token_bytes = struct.pack("<i", len(encoded_bytes)) + encoded_bytes
                attrs_before = {1256: token_bytes}

                if 'datawarehouse.fabric.microsoft.com' in target_DB_details['host']:
                    query = f"""COPY INTO {schema_name.upper()}.{table_name.upper()}
                        FROM 'https://qmigstg1137.blob.core.windows.net/{container_name}/{file_name}'
                        WITH (
                        FILE_TYPE = 'CSV',
                        CREDENTIAL=(IDENTITY= 'Storage Account Key', SECRET='****************************************************************************************'),
                        FIRSTROW =2
                        )
                        """
                else:
                    query = f"""BULK INSERT {schema_name.upper()}.{table_name.upper()}
                        FROM '{file_name}'
                        WITH (
                            DATA_SOURCE = 'MyBlobStorage',
                            FIELDTERMINATOR = ',', 
                            ROWTERMINATOR = '\n', 
                            FIRSTROW = 2  
                        );
                        """

                load_start_time = datetime.now()
                target_connection = pyodbc.connect(connection_string, attrs_before=attrs_before)
                target_cursor = target_connection.cursor()
                target_cursor.execute(query)
                target_connection.commit()
                target_cursor.close()
                target_connection.close()
                load_end_time = datetime.now()

            load_time = (load_end_time - load_start_time).total_seconds() / 60

            task_status = 'Success'
            task_logger.debug('Table execution ended')

            task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')

            project_connection = connect_database(project_DB_details)
            task_update(project_connection, task_id, len(table_data_list), task_status, None, task_end_time,
                        extraction_time, transform_time, load_time)
    except Exception as e:
        task_status = 'Fail'
        task_error = str(e)

        task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        project_connection = connect_database(project_DB_details)
        task_update(project_connection, task_id, None, task_status, task_error, task_end_time, extraction_time, transform_time,
                    load_time)
        ti.UP_FOR_RETRY

def complete_validation_trigger(ti, **kwargs):
    process_type = kwargs['process_type']
    project_id = kwargs['project_id']
    token_data = kwargs['token_data']

    dag_config = ti.xcom_pull(key='dag_config', task_ids='pre_validation_task')
    dag_id = dag_config['dag_id']

    project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
    project_connection = connect_database(project_DB_details)

    task_start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_id = task_insert(project_connection, 'complete_validation_task', 'Complete_Validation', ti.try_number, None,
                          None, None, None, task_start_time, 'Running', dag_id)
    task_id = task_id[0][0]
    task_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    task_update(project_connection, task_id, None, 'Success', None, task_end_time, None, None, None)

    if process_type == 'Initial_Data_Load':
        dag_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        dag_update(project_connection, dag_id, 'Success', dag_end_time)
    return False













