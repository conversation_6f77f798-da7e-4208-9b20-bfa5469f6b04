Data_Load::
    python main_migration.py -task Initial_Data_Load/E2E_Data_Load -project_id 1167 -mig_name Oracle_Postgres14 -source_connection_id 1
    -target_connection_id 2 -schema GGDB2 -target_schema GGDB2 -table_name Demo -request_cpu 1000m -limit_cpu 2000m -data_load_type Database/File
    -cdc_load_type Database/File -file_name  -cloud_category Cloud

CDC::
    python main_migration.py -task CDC -project_id 1167 -mig_name Oracle_Postgres14 -source_connection_id 1
     -target_connection_id 2 -schema GGDB2 -target_schema GGDB2 -table_name Demo -cdc_load_type File/Database
     -file_name -cloud_category Cloud

Load_Files::
     python main_migration.py -task Load_Files -project_id 1167 -mig_name Oracle_Postgres14 -source_connection_id 1
     -target_connection_id 2 -file_name -cloud_category Cloud

Job Agent:
     python main_migration.py -task Job_Agent -project_id 1167 -mig_name Oracle_Postgres14 -source_connection_id 1
     -target_connection_id 2 -cloud_category Cloud

Validation::
     python main_migration.py -task Validation -project_id 1167 -mig_name Oracle_Postgres14 -source_connection_id 1
     -target_connection_id 2 -schema GGDB2 -target_schema GGDB2 -file_name -cloud_category Cloud

Data_compare::
    python main_migration.py -task Data_Compare -project_id 1167 -mig_name Oracle_Postgres14 -source_connection_id 1
     -target_connection_id 2 -schema GGDB2 -target_schema GGDB2 -table_name Demo -compare_type Detailed_Compare/Sample_Compare
     -request_cpu 1000m -limit_cpu 2000m -file_name -cloud_category Cloud





Catchup::

    python main_migration.py -task Catchup -project_id 1167 -mig_name Oracle_Postgres14
     -parent_file config_old -pod_configuration_type Default/Custom -request_cpu 1000m -limit_cpu 2000m -request_memory 2Gi
     -limit_memory 4Gi-file_name config_new -cloud_category Cloud


Extract tables::

    python main_migration.py -task Extract_Tables -project_id 1167 -mig_name Oracle_Postgres14 -source_connection_id 1 -schema GGDB2


CPU Memory Utilization::

    python main_migration.py -task CPU_Memory_Utilization -project_id 1167 -mig_name Oracle_Postgres14 -source_connection_id 1 -cloud_category Cloud


CDC Report::

    python main_migration.py -task CDC_Report -project_id 1167 -mig_name Oracle_Postgres14 -start_time
     -end_time  -file_name -cloud_category Cloud

Load CDC Files::

    python main_migration.py -task Load_CDC_Files -project_id 1167 -mig_name Oracle_Postgres14 -target_connection_id 2
     -schema GGDB2 -table_name Demo -batch_size -file_name -cloud_category Cloud

GG Template Load::

    python main_migration.py -task GG_Template_Load -project_id 1167 -mig_name Oracle_Postgres14 -operation Extract/Globals/Odbc/Mgr
     -source_connection_id 1 -target_connection_id 2 -schema GGDB2 -schema_id  -table_name -pod_service -trail_path -gg_home
     -orcl_home -pod_name -cloud_category Cloud

E2E Mydumper::

    python main_migration.py -task E2E_Mydumper -project_id 1167 -mig_name Oracle_Postgres14 -source_connection_id 1 -file_name -cloud_category Cloud
