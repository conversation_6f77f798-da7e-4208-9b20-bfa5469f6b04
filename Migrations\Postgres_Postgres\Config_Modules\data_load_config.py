import re, os, sys, time, shutil, json, math
from import_file import import_file
import pandas as pd
from common_modules.api import api_authentication, decrypt_database_details
from common_modules.stored_procedures import insert_config_name

def copy_dag_files(file_name, config_files_path, local_migration_path, dag_path, root_path):
    source_file = local_migration_path + '/' + 'Dag_Files' + '/' + 'dag_file.py'
    target_file = dag_path + '/' + f'dag_file_{file_name}.py'

    with open(source_file, 'r') as file:
        file_content = file.read()
        file_replaced_content = re.sub(r'@Config_File_Path',
                                       rf'{config_files_path}/Data_Migration/{file_name}.xlsx'.replace('\\', '/'),
                                       file_content)
        file_replaced_content = re.sub(r'@Config_File_Folder',
                                       config_files_path.replace('\\', '/'),
                                       file_replaced_content)
    with open(target_file, 'w') as f:
        f.write(file_replaced_content)

    dag_triggers_file = local_migration_path + '/' + 'Dag_Files' + '/' + 'dag_triggers.py'
    trigger_destination_file = dag_path + '/' + 'dag_triggers.py'
    with open(dag_triggers_file, 'rb') as f:
        file_content = f.read()
    with open(trigger_destination_file, 'wb') as f:
        f.write(file_content)

    if not os.path.exists(root_path + '/Validation_Queries/'):
        os.makedirs(root_path + '/Validation_Queries/')

    xml_files = local_migration_path + '/' + 'validation_queries.xml'
    xml_destination_file = root_path + '/' + 'Validation_Queries' + '/' + 'validation_queries.xml'
    shutil.copyfile(xml_files, xml_destination_file)


def calculate_chunk_parts(table_size, row_count, chunksize, record_size):
    no_of_parts = 0
    parts_list = []
    interval = 0
    final_tuple_list = []
    if table_size != 0 and row_count != 0:
        no_of_parts = math.ceil(table_size / int(chunksize))
        parts_list = ['Part_' + str(i) for i in range(1, no_of_parts + 1)]
        interval = math.ceil((row_count - 0) / no_of_parts)
    else:
        if row_count != 0:
            no_of_parts = 1
            parts_list = ['Part_1']
            interval = math.ceil((row_count - 0) / no_of_parts)

    if no_of_parts != 0:
        lower_bound = 0
        upper_bound = lower_bound + interval

        for index, part in enumerate(parts_list):
            if index == len(parts_list) - 1:
                updated_chunk_size = int(table_size) - (index * int(chunksize))
                chunk_record_size = math.ceil((row_count - lower_bound) * record_size)
                created_tuple = (part, row_count, updated_chunk_size, lower_bound, 'Max', chunk_record_size)
            else:
                chunk_record_size = math.ceil((upper_bound - lower_bound) * record_size)
                created_tuple = (part, row_count, chunksize, lower_bound, upper_bound, chunk_record_size)
            final_tuple_list.append(created_tuple)
            lower_bound = upper_bound
            upper_bound = upper_bound + interval
    return final_tuple_list


def data_load_trigger(task_name, project_id, migration_name, source_connection_id, target_connection_id, schema_name,
                      target_schema, table_name, request_cpu, limit_cpu, data_load_type, cdc_load_type,
                      file_name,cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')
        dag_path = getattr(import_object, 'Dag_Path')

        working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')
        local_migration_path = local_root_path + '/' + 'Migrations' + '/' + migration_name

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)

            token_data = api_authentication()


            source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(source_connection_id))
            source_function_call = getattr(import_object, 'DB_connection')

            project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
            project_function_call = getattr(import_object, 'connect_database')

            execute_query_function_call = getattr(import_object, 'execute_query')

            if target_connection_id in ['', None]:
                config_files_path = root_path + '/' + str(source_connection_id) + '/' + 'Config_Files'
            else:
                config_files_path = root_path + '/' + str(source_connection_id) + '/' + str(
                    target_connection_id) + '/' + 'Config_Files'

            if not os.path.exists(config_files_path + '/' + 'Data_Migration'):
                os.makedirs(config_files_path + '/' + 'Data_Migration')

            if not os.path.isfile(config_files_path + '/' + 'chunk_configuration.json'):
                configuration_json_file = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'chunk_configuration.json'
                destination_json_file = config_files_path + '/' + 'chunk_configuration.json'
                shutil.copyfile(configuration_json_file, destination_json_file)

            if not os.path.exists(working_directory_path):
                os.makedirs(working_directory_path)

            tag = ''
            if task_name == 'Initial_Data_Load':
                tag = 'init_'
            elif task_name == 'E2E_Data_Load':
                tag = 'e2e_'

            excel_file_name = tag + str(file_name).lower().strip()
            config_path = config_files_path + '/' + 'Data_Migration' + '/' + excel_file_name + '.xlsx'
            temp_config_path = working_directory_path + '/' + excel_file_name + '.xlsx'

            dag_tasks_list = []
            if data_load_type != 'PG_Dump':
                if not os.path.isfile(config_files_path + '/' + 'chunk_configuration.json'):
                    shutil.copyfile(local_migration_path + '/' + 'chunk_configuration.json', config_files_path + '/' + 'chunk_configuration.json')

                with open(config_files_path + '/' + 'chunk_configuration.json', 'r') as f:
                    chunk_configuration = json.loads(f.read())
                    chunk_size = chunk_configuration['Chunk_Size']

                schema_list_query = """
                SELECT schema_name
                FROM information_schema.schemata
                WHERE schema_name NOT IN ('information_schema', 'pg_catalog')
                AND schema_name NOT LIKE 'pg_toast%'
                AND schema_name NOT LIKE 'pg_temp%'
                """

                table_list_query = """
                SELECT distinct c.relname AS table_name,'VALID'as STATUS
                FROM pg_catalog.pg_class c
                JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
                WHERE upper(n.nspname) = upper('@schemaname')
                AND c.relkind = 'r'
                ORDER BY c.relname
                """

                tables_list = []
                source_connection, error = source_function_call(source_DB_details)
                if table_name != '':
                    tables_list = [(schema_name.upper(), i.upper()) for i in table_name.split(',') if i.strip()]
                elif schema_name != '' and table_name == '':
                    schema_list = [i for i in schema_name.split(',') if i.strip()]
                    if schema_list:
                        for schema in schema_list:
                            table_list_query = table_list_query.replace('@schemaname', schema.upper())
                            schema_table_list = execute_query_function_call(source_connection, table_list_query)
                            if schema_table_list:
                                tables_list.extend([(schema.upper(), i[0]) for i in schema_table_list])
                elif schema_name == '' and table_name == '':
                    schema_list = execute_query_function_call(source_connection, schema_list_query)
                    if schema_list:
                        original_table_list_query = table_list_query  # preserve the original query
                        for schema in schema_list:
                            table_list_query = original_table_list_query.replace('@schemaname', schema[0].upper())
                            schema_table_list = execute_query_function_call(source_connection, table_list_query)
                            if schema_table_list:
                                tables_list.extend([(schema[0], i[0]) for i in schema_table_list])

                with pd.ExcelWriter(temp_config_path, engine="openpyxl") as writer:
                    for table_tuple in tables_list:
                        datatypes_query = f"select distinct data_type from information_schema.columns where upper(table_schema) =upper('{table_tuple[0][0]}') and upper(table_name)=upper('{table_tuple[1]}')"
                        datatypes_list = execute_query_function_call(source_connection, datatypes_query)

                        data_query = f"""
                            with cte as (select
                                nspname as owner,
                                c.relname as table_name,
                                ROUND(pg_total_relation_size(c.oid) / 1024 / 1024,
                                2) as size_mb
                            from
                                pg_class c
                            join
                                pg_namespace n on
                                c.relnamespace = n.oid
                            where
                                upper(nspname) = upper('{table_tuple[0]}')
                                and upper(c.relname) in ('{table_tuple[1].upper()}')
                            union all
                            select
                                nspname as owner,
                                c.relname as table_name,
                                ROUND(pg_total_relation_size(c.oid) / 1024 / 1024,
                                2) as size_mb
                            from
                                pg_class c
                            join
                                pg_namespace n on
                                c.relnamespace = n.oid
                            join
                                    pg_stat_user_tables s on
                                s.relname = c.relname
                            where
                                c.relkind = 'r'
                                and upper(nspname) = upper('{table_tuple[0]}')
                                and upper(c.relname) in ('{table_tuple[1].upper()}')
                            )
                            select owner, table_name,(
                                select count(*) from {table_tuple[0]}.{table_tuple[1]}) as num_rows,ROUND(SUM(size_mb),2) as MB
                            from cte group by owner,table_name
                        """
                        table_data_list = execute_query_function_call(source_connection, data_query)

                        if any(element in datatypes_list for element in ['bytea', 'xml']):
                            table_category = 'Lob'
                        else:
                            table_category = 'Non_Lob'
                        dag_name = f'{table_tuple[0]}_{table_tuple[1]}_{file_name.upper()}'

                        if table_data_list:
                            table_list = str(table_data_list[0][3]).replace('KB', '')
                            table_size = round(float(table_list.strip()) / 1024, 2)
                            row_count = int(table_data_list[0][2])

                            if row_count != 0:
                                record_size = table_size / row_count
                            else:
                                record_size = 0
                            parts_list = calculate_chunk_parts(table_size, row_count, chunk_size, record_size)

                            count = 1
                            if parts_list:
                                for part_tuple in parts_list:
                                    dag_tuple = (
                                        table_tuple[0], table_tuple[1], table_category, dag_name, f'Part_{count}',
                                        part_tuple[5], part_tuple[3], part_tuple[4])
                                    dag_tasks_list.append(dag_tuple)
                                    count = count + 1
                            else:
                                dag_tuple = (
                                    schema_name, table_name, table_category, dag_name,'Part_1', chunk_size,0,

                                    0)
                                dag_tasks_list.append(dag_tuple)
                    if dag_tasks_list:
                        if task_name in ['E2E_Data_Load']:
                            tables_str = ','.join([f'{table_tuple[0]}.{table_tuple[1]}' for table_tuple in tables_list])
                            cdc_dag_tuple = (
                                schema_name, tables_str, 'CDC', 'CDC_' + file_name.replace('.xlsx', ''),
                                'CDC_' + file_name.replace('.xlsx', ''),
                                None, None, None)
                            dag_tasks_list.append(cdc_dag_tuple)
                            if cdc_load_type == 'File':
                                load_files_dag_tuple = (
                                    schema_name,tables_str, 'Load_Files',
                                    'Load_Files_' + file_name.replace('.xlsx', ''),
                                    'Load_Files_' + file_name.replace('.xlsx', ''),
                                    None, None, None)
                                dag_tasks_list.append(load_files_dag_tuple)

                        tasks_df = pd.DataFrame(dag_tasks_list,
                                                columns=['Schema_Name', 'Table_Name', 'Table_Category', 'Dag Name',
                                                         'Part_Name', 'Chunk_Size', 'Lower_Bound', 'Upper_Bound'])
                        tasks_df.fillna('', inplace=True)
                        tasks_df.to_excel(writer, sheet_name='Dags', index=False)
                        dag_names_list = tasks_df['Dag Name'].drop_duplicates().to_list()
                    config_list = [
                        (task_name, int(project_id), migration_name, schema_name, target_schema, table_name,
                         str(source_connection_id), str(target_connection_id), data_load_type, cdc_load_type,
                         request_cpu,
                         limit_cpu, excel_file_name)]
                    config_df = pd.DataFrame(config_list,
                                             columns=['Process_Type', 'Project_Id', 'Migration_Name', 'Schema',
                                                      'Target_Schema', 'Table_Name', 'Source_Connection_Id',
                                                      'Target_Connection_Id', 'Data_Load_Type', 'CDC_Load_Type',
                                                      'Request_CPU', 'Limit_CPU', 'File_Name'])
                    config_df = config_df.transpose()
                    key_value_pairs = config_df.to_dict()[0]
                    config_df = pd.DataFrame(list(key_value_pairs.items()), columns=['Parameter', 'Value'])
                    config_df.fillna('', inplace=True)
                    config_df.to_excel(writer, sheet_name="Configuration", index=False)

                    if dag_names_list:
                        priority_list = []
                        for dag_name in dag_names_list:
                            epoch_time = time.time()
                            priority_tuple = (dag_name, int(epoch_time))
                            priority_list.append(priority_tuple)
                            time.sleep(1)
                        priority_df = pd.DataFrame(priority_list, columns=['Dag Name', 'Priority_Weight'])
                        priority_df.to_excel(writer, sheet_name="Priority_Weights", index=False)
            else:
                with pd.ExcelWriter(temp_config_path, engine="openpyxl") as writer:
                    dag_name = f'E2E_Migration_{file_name.upper()}'

                    if table_name != '':
                        schema_name = schema_name
                        table_name = table_name
                        target_schema = schema_name
                    elif schema_name != '' and table_name == '':
                        schema_name = schema_name
                        table_name = 'Schema'
                        target_schema = schema_name
                    elif schema_name == '' and table_name == '':
                        schema_name = source_DB_details['db_name']
                        table_name = 'Database'
                        target_schema = 'Database'

                    dag_tasks_list = [(schema_name, table_name, 'E2E_Migration', dag_name, dag_name, None, None,None)]

                    tasks_df = pd.DataFrame(dag_tasks_list,
                                            columns=['Schema_Name', 'Table_Name', 'Table_Category', 'Dag Name',
                                                     'Part_Name', 'Chunk_Size', 'Lower_Bound', 'Upper_Bound'])
                    tasks_df.fillna('', inplace=True)
                    tasks_df.to_excel(writer, sheet_name='Dags', index=False)
                    dag_names_list = tasks_df['Dag Name'].drop_duplicates().to_list()

                    config_list = [
                        (task_name, int(project_id), migration_name, schema_name, target_schema, table_name,
                         str(source_connection_id), str(target_connection_id), data_load_type, cdc_load_type, request_cpu,
                         limit_cpu, excel_file_name)]
                    config_df = pd.DataFrame(config_list,
                                             columns=['Process_Type', 'Project_Id', 'Migration_Name', 'Schema',
                                                      'Target_Schema', 'Table_Name', 'Source_Connection_Id',
                                                      'Target_Connection_Id', 'Data_Load_Type', 'CDC_Load_Type',
                                                      'Request_CPU', 'Limit_CPU', 'File_Name'])
                    config_df = config_df.transpose()
                    key_value_pairs = config_df.to_dict()[0]
                    config_df = pd.DataFrame(list(key_value_pairs.items()), columns=['Parameter', 'Value'])
                    config_df.fillna('', inplace=True)
                    config_df.to_excel(writer, sheet_name="Configuration", index=False)

                    if dag_names_list:
                        priority_list = []
                        for dag_name in dag_names_list:
                            epoch_time = time.time()
                            priority_tuple = (dag_name, int(epoch_time))
                            priority_list.append(priority_tuple)
                            time.sleep(1)
                        priority_df = pd.DataFrame(priority_list, columns=['Dag Name', 'Priority_Weight'])
                        priority_df.to_excel(writer, sheet_name="Priority_Weights", index=False)

            shutil.copyfile(temp_config_path, config_path)

            project_connection = project_function_call(project_DB_details)
            if target_connection_id in ['', None]:
                target_connection_id = None
            insert_config_name(project_connection, excel_file_name, task_name, source_connection_id,
                               target_connection_id, request_cpu, limit_cpu, 'Created')
            copy_dag_files(excel_file_name, config_files_path.replace(root_path + '/', ''), local_migration_path, dag_path, root_path)
        else:
            print(f'Module path {db_module_path} not found')
    else:
        print(f'Config path {config_path} not found')
